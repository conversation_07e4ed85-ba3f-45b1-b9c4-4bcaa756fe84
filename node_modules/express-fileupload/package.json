{"name": "express-fileupload", "version": "1.5.1", "author": "<PERSON> <<EMAIL>>", "description": "Simple express file upload middleware that wraps around Busboy", "main": "./lib/index", "scripts": {"pretest": "node ./test/pretests.js", "posttest": "node ./test/posttests.js", "test": "nyc  --reporter=html --reporter=text mocha -- -R spec", "lint": "eslint ./", "lint:fix": "eslint --fix ./", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"busboy": "^1.6.0"}, "engines": {"node": ">=12.0.0"}, "keywords": ["express", "file-upload", "upload", "forms", "multipart", "files", "busboy", "middleware"], "license": "MIT", "repository": "richardgirges/express-fileupload", "devDependencies": {"coveralls": "^3.1.1", "eslint": "^7.31.0", "express": "^4.18.1", "md5": "^2.3.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "rimraf": "^3.0.2", "rnd-file": "^0.0.1", "supertest": "^6.1.5"}}