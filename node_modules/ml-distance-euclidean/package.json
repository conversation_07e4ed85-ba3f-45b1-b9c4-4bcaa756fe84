{"name": "ml-distance-euclidean", "version": "2.0.0", "description": "Compute the euclidean distance between two vectors", "main": "./lib/euclidean.js", "module": "./lib-es6/euclidean.js", "types": "./lib/euclidean.d.ts", "files": ["lib", "lib-es6"], "scripts": {"clean": "rimraf lib lib-es6", "prepublishOnly": "npm run tsc", "test": "npm run test-only && npm run tslint", "test-only": "jest", "tsc": "npm run clean && npm run tsc-es5 && npm run tsc-es6", "tsc-es5": "tsc", "tsc-es6": "tsc --project tsconfig.es6.json", "tslint": "tslint --project tsconfig.base.json", "tslint-fix": "npm run tslint -- --fix"}, "repository": {"type": "git", "url": "git+https://github.com/mljs/distance-euclidean.git"}, "keywords": ["euclidean", "squared", "distance", "metric", "vector", "data", "mining", "datamining", "machine", "learning"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mljs/distance-euclidean/issues"}, "homepage": "https://github.com/mljs/distance-euclidean#readme", "jest": {"testEnvironment": "node", "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "devDependencies": {"@types/jest": "^23.3.1", "jest": "^23.5.0", "rimraf": "^2.6.2", "ts-jest": "^23.1.3", "tslint": "^5.11.0", "tslint-config-prettier": "^1.14.0", "typescript": "^3.0.1"}}