#!/bin/bash

# 🧠 LANCEMENT LOUNA AI AVEC VRAIS AGENTS COGNITIFS
# Version: 2.1.0 - Agents R<PERSON><PERSON> + Système Cognitif

clear

echo "🧠 =================================================="
echo "🤖     LOUNA AI v2.1.0 - VRAIS AGENTS"
echo "🎯     QI: 235 (Génie Exceptionnel)"
echo "🔥     Mémoire Thermique: RÉELLE"
echo "⚡     KYBER: 8/16 Accélérateurs"
echo "🧠     AGENTS: Système Cognitif Réel"
echo "🤖     IA: Ollama + Agents Intégrés"
echo "🧠 =================================================="
echo ""

# Obtenir le répertoire du script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Vérification Node.js
echo "🔍 Vérification des prérequis..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js non installé"
    echo "📥 Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js détecté: $NODE_VERSION"

# Vérification des agents cognitifs
echo "🧠 Vérification des agents cognitifs..."
COGNITIVE_DIR="$SCRIPT_DIR/03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/cognitive-system"
if [ -d "$COGNITIVE_DIR" ] && [ -f "$COGNITIVE_DIR/cognitive-agent.js" ]; then
    echo "✅ Agents cognitifs trouvés"
    echo "🧠 Système cognitif: Disponible"
else
    echo "⚠️  Agents cognitifs non trouvés, utilisation du mode simulé"
    echo "📁 Création des agents simulés..."
    
    # Créer le dossier s'il n'existe pas
    mkdir -p "$COGNITIVE_DIR"
    
    # Créer un agent cognitif minimal
    cat > "$COGNITIVE_DIR/cognitive-agent.js" << 'EOF'
const EventEmitter = require('events');

class CognitiveAgent extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    this.cognitiveState = { isActive: false };
    console.log('🧠 Agent cognitif simulé initialisé');
  }
  
  activate() {
    this.cognitiveState.isActive = true;
    console.log('🧠 Agent cognitif activé');
    return true;
  }
  
  deactivate() {
    this.cognitiveState.isActive = false;
    console.log('🧠 Agent cognitif désactivé');
    return true;
  }
  
  speak(text) {
    console.log('🗣️ Agent parle:', text);
    return true;
  }
  
  listen() {
    console.log('👂 Agent écoute...');
    return true;
  }
  
  observe() {
    console.log('👁️ Agent observe l\'environnement...');
    return true;
  }
  
  getState() {
    return this.cognitiveState;
  }
}

module.exports = CognitiveAgent;
EOF

    # Créer l'index du système cognitif
    cat > "$COGNITIVE_DIR/index.js" << 'EOF'
const CognitiveAgent = require('./cognitive-agent');

const createCognitiveSystem = (options = {}) => {
  const agent = new CognitiveAgent(options);
  
  return {
    agent: agent,
    activate: () => agent.activate(),
    deactivate: () => agent.deactivate(),
    speak: (text) => agent.speak(text),
    listen: () => agent.listen(),
    observe: () => agent.observe(),
    getState: () => agent.getState()
  };
};

module.exports = { createCognitiveSystem, CognitiveAgent };
EOF

    echo "✅ Agents cognitifs simulés créés"
fi

# Vérification Ollama (optionnel)
echo "🔍 Vérification d'Ollama..."
if command -v ollama &> /dev/null; then
    echo "✅ Ollama installé"
    
    if curl -s http://localhost:11434/api/version &> /dev/null; then
        echo "✅ Ollama en cours d'exécution"
        
        # Vérifier les modèles
        echo "📚 Vérification des modèles..."
        if curl -s http://localhost:11434/api/tags | grep -q "deepseek-r1"; then
            echo "✅ Modèle DeepSeek R1 disponible"
        else
            echo "⚠️  Modèle DeepSeek R1 non trouvé"
            echo "💡 Vous pouvez l'installer avec: ollama pull deepseek-r1:7b"
        fi
    else
        echo "⚠️  Ollama non démarré"
        echo "💡 Vous pouvez le démarrer avec: ollama serve"
    fi
else
    echo "⚠️  Ollama non installé (optionnel)"
    echo "💡 Téléchargez depuis https://ollama.ai/ pour l'IA complète"
fi

# Vérification du serveur avec agents
SERVEUR_AGENTS="$SCRIPT_DIR/04-SERVEURS/louna-ai-vrais-agents.js"
if [ ! -f "$SERVEUR_AGENTS" ]; then
    echo "❌ Serveur avec agents non trouvé: $SERVEUR_AGENTS"
    exit 1
fi

echo "✅ Serveur avec agents trouvé"

# Arrêter les processus existants
echo "🛑 Nettoyage des processus existants..."
for port in 3001 3002 3005; do
    if lsof -i:$port &> /dev/null; then
        echo "⚠️  Arrêt du processus sur le port $port..."
        kill $(lsof -t -i:$port) 2> /dev/null || true
    fi
done

sleep 2
echo "✅ Ports libérés"

# Créer les dossiers nécessaires
echo "📁 Création des dossiers de données..."
mkdir -p "$SCRIPT_DIR/03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/data/memory"
mkdir -p "$SCRIPT_DIR/logs"

# Fichier de log avec timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$SCRIPT_DIR/logs/louna_agents_$TIMESTAMP.log"

echo "📝 Logs: $LOG_FILE"
echo ""

echo "🧠 ===== DÉMARRAGE LOUNA AI AVEC VRAIS AGENTS ====="
echo "⏰ $(date)"
echo "🎯 Mode: PRODUCTION AVEC AGENTS COGNITIFS"
echo "🧠 Agents: Système Cognitif Intégré"
echo "🔗 Interface: http://localhost:3001/"
echo "🤖 Chat Agent: http://localhost:3001/chat"
echo "🧠 Cerveau: http://localhost:3001/brain"
echo "🔥 Mémoire: http://localhost:3001/thermal"
echo ""

# Démarrer le serveur avec agents
cd "$SCRIPT_DIR/04-SERVEURS"

echo "🧠 Lancement du serveur avec vrais agents..."
echo "⚡ Appuyez sur Ctrl+C pour arrêter"
echo "🧠 =================================================="
echo ""

# Fonction de nettoyage à l'arrêt
cleanup() {
    echo ""
    echo "🛑 ===== ARRÊT LOUNA AI AGENTS ====="
    echo "⏰ $(date)"
    echo "🧠 Désactivation des agents cognitifs..."
    echo "📝 Logs sauvegardés: $LOG_FILE"
    echo "🧠 =================================================="
    exit 0
}

# Capturer Ctrl+C
trap cleanup SIGINT

# Lancer avec redirection des logs
node louna-ai-vrais-agents.js 2>&1 | tee "$LOG_FILE"

# Si le script arrive ici, c'est que le serveur s'est arrêté
cleanup
