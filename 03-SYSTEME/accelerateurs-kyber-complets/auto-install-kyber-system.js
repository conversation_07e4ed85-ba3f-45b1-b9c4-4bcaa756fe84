#!/usr/bin/env node

/**
 * 🚀 SYSTÈME AUTO-INSTALLATION KYBER
 * QI: 235 - INSTALLATION AUTOMATIQUE + PERSISTANCE
 */

const fs = require("fs");
const path = require("path");

class AutoInstallKyberSystem {
    constructor() {
        this.baseDir = "/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE";
        this.dataDir = path.join(this.baseDir, "07-DONNEES", "kyber-persistent");
        this.accelerators = new Map();
        this.installedAccelerators = new Map();
        this.autoInstallEnabled = true;
        this.persistenceEnabled = true;
        
        this.init();
    }

    async init() {
        console.log("🚀 Auto-installation KYBER initialisée");
        this.ensureDirectories();
        this.setupDefaultAccelerators();
        await this.startAutoInstallation();
        this.startPersistenceMonitoring();
        console.log("✅ Système KYBER auto-installé et persistant");
    }

    ensureDirectories() {
        const dirs = [this.dataDir, path.join(this.baseDir, "07-DONNEES"), path.join(this.baseDir, "10-LOGS")];
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`📁 Répertoire créé: ${dir}`);
            }
        });
    }

    setupDefaultAccelerators() {
        const accelerators = {
            "cpu_optimizer": { name: "CPU Optimizer", boost: 2.5, autoInstall: true, persistent: true },
            "memory_accelerator": { name: "Memory Accelerator", boost: 3.0, autoInstall: true, persistent: true },
            "response_turbo": { name: "Response Turbo", boost: 4.0, autoInstall: true, persistent: true },
            "ltx_generator_boost": { name: "LTX Generator Boost", boost: 5.0, autoInstall: true, persistent: true },
            "thermal_regulator": { name: "Thermal Regulator", boost: 2.0, autoInstall: true, persistent: true },
            "neural_connector": { name: "Neural Connector", boost: 3.5, autoInstall: true, persistent: true },
            "circulation_enhancer": { name: "Circulation Enhancer", boost: 2.8, autoInstall: true, persistent: true },
            "reflexive_booster": { name: "Reflexive Booster", boost: 4.5, autoInstall: true, persistent: true }
        };

        for (const [id, config] of Object.entries(accelerators)) {
            this.accelerators.set(id, config);
        }
        console.log(`🎯 ${this.accelerators.size} accélérateurs KYBER configurés`);
    }

    async startAutoInstallation() {
        console.log("🚀 Démarrage auto-installation...");
        for (const [id, config] of this.accelerators) {
            if (config.autoInstall) {
                await this.installAccelerator(id, config);
            }
        }
        
        // Vérification automatique toutes les minutes
        setInterval(() => this.checkAndReinstallAccelerators(), 60000);
        console.log("✅ Auto-installation active");
    }

    async installAccelerator(id, config) {
        try {
            console.log(`🔧 Installation: ${config.name}...`);
            const installation = {
                id, name: config.name, boost: config.boost,
                installedAt: new Date(), autoInstalled: true, persistent: config.persistent
            };
            this.installedAccelerators.set(id, installation);
            await this.saveInstalledAccelerators();
            console.log(`✅ ${config.name} installé (${config.boost}x boost)`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur installation ${id}:`, error.message);
            return false;
        }
    }

    async checkAndReinstallAccelerators() {
        for (const [id, config] of this.accelerators) {
            if (config.autoInstall && config.persistent && !this.installedAccelerators.has(id)) {
                console.log(`🔄 Réinstallation: ${config.name}`);
                await this.installAccelerator(id, config);
            }
        }
    }

    startPersistenceMonitoring() {
        setInterval(() => this.saveInstalledAccelerators(), 30000);
        console.log("👁️ Surveillance persistance active");
    }

    async saveInstalledAccelerators() {
        try {
            const installed = {};
            for (const [id, config] of this.installedAccelerators) {
                installed[id] = { ...config, installedAt: config.installedAt.toISOString() };
            }
            const filePath = path.join(this.dataDir, "installed-accelerators.json");
            fs.writeFileSync(filePath, JSON.stringify(installed, null, 2));
            console.log(`💾 ${this.installedAccelerators.size} accélérateurs sauvegardés`);
        } catch (error) {
            console.error("❌ Erreur sauvegarde:", error.message);
        }
    }

    getStatus() {
        return {
            autoInstallEnabled: this.autoInstallEnabled,
            persistenceEnabled: this.persistenceEnabled,
            totalAccelerators: this.accelerators.size,
            installedAccelerators: this.installedAccelerators.size,
            activeAccelerators: this.installedAccelerators.size
        };
    }
}

const autoInstallKyberSystem = new AutoInstallKyberSystem();
if (typeof global !== "undefined") global.autoInstallKyberSystem = autoInstallKyberSystem;
module.exports = autoInstallKyberSystem;
