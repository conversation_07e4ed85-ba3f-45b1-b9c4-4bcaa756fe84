/**
 * CONNECTEUR AGENT CLAUDE RÉEL v2.1.0
 * Connexion directe à l'API Anthropic Claude
 * <PERSON><PERSON>é par <PERSON>, Guadeloupe
 */

const axios = require('axios');

class ClaudeAgentConnector {
  constructor(options = {}) {
    this.config = {
      apiKey: process.env.ANTHROPIC_API_KEY || options.apiKey,
      apiUrl: 'https://api.anthropic.com/v1/messages',
      model: options.model || 'claude-3-sonnet-20240229',
      maxTokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.7,
      timeout: options.timeout || 60000,
      retries: options.retries || 3
    };

    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokensUsed: 0,
      averageResponseTime: 0,
      lastRequestTime: null,
      isConnected: false
    };

    console.log('🤖 ClaudeAgentConnector initialisé');
    console.log(`📡 Modèle: ${this.config.model}`);
    console.log(`🔑 Clé API: ${this.config.apiKey ? 'Configurée' : 'NON CONFIGURÉE'}`);
  }

  /**
   * Vérifier si Claude est disponible
   */
  async isClaudeAvailable() {
    try {
      if (!this.config.apiKey) {
        return {
          available: false,
          reason: 'Clé API Anthropic non configurée',
          suggestion: 'Configurez ANTHROPIC_API_KEY dans les variables d\'environnement'
        };
      }

      // Test de ping simple
      const testResponse = await this.sendMessage('Test de connexion', { 
        maxTokens: 10,
        timeout: 10000 
      });

      this.stats.isConnected = true;
      return {
        available: true,
        reason: 'Agent Claude opérationnel',
        model: this.config.model,
        responseTime: testResponse.responseTime || 0
      };

    } catch (error) {
      this.stats.isConnected = false;
      return {
        available: false,
        reason: error.message,
        suggestion: 'Vérifiez votre clé API et votre connexion Internet'
      };
    }
  }

  /**
   * Envoyer un message à Claude
   */
  async sendMessage(message, options = {}) {
    const startTime = Date.now();
    this.stats.totalRequests++;
    this.stats.lastRequestTime = new Date().toISOString();

    try {
      if (!this.config.apiKey) {
        throw new Error('Clé API Anthropic non configurée');
      }

      if (!message || typeof message !== 'string') {
        throw new Error('Message invalide');
      }

      // Configuration de la requête
      const requestConfig = {
        model: options.model || this.config.model,
        max_tokens: options.maxTokens || this.config.maxTokens,
        temperature: options.temperature || this.config.temperature,
        messages: [
          {
            role: 'user',
            content: this.buildEnhancedPrompt(message, options.context)
          }
        ]
      };

      console.log(`📡 [CLAUDE] Envoi à ${this.config.apiUrl}`);
      console.log(`📝 [CLAUDE] Message: ${message.substring(0, 100)}...`);

      // Appel API avec retry
      let lastError;
      for (let attempt = 1; attempt <= this.config.retries; attempt++) {
        try {
          const response = await axios.post(this.config.apiUrl, requestConfig, {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.config.apiKey,
              'anthropic-version': '2023-06-01'
            },
            timeout: options.timeout || this.config.timeout
          });

          const responseTime = Date.now() - startTime;
          this.stats.successfulRequests++;
          this.stats.averageResponseTime = 
            (this.stats.averageResponseTime * (this.stats.successfulRequests - 1) + responseTime) / 
            this.stats.successfulRequests;

          if (response.data && response.data.content && response.data.content[0]) {
            const claudeResponse = response.data.content[0].text;
            
            // Mettre à jour les statistiques
            if (response.data.usage) {
              this.stats.totalTokensUsed += response.data.usage.input_tokens + response.data.usage.output_tokens;
            }

            console.log(`✅ [CLAUDE] Réponse reçue en ${responseTime}ms`);
            console.log(`📊 [CLAUDE] Tokens: ${response.data.usage?.input_tokens || 0} + ${response.data.usage?.output_tokens || 0}`);

            return {
              success: true,
              response: claudeResponse,
              model: response.data.model,
              source: 'Claude API',
              usage: response.data.usage,
              responseTime,
              timestamp: new Date().toISOString()
            };
          } else {
            throw new Error('Réponse Claude invalide');
          }

        } catch (error) {
          lastError = error;
          console.warn(`⚠️ [CLAUDE] Tentative ${attempt}/${this.config.retries} échouée:`, error.message);
          
          if (attempt < this.config.retries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Backoff
          }
        }
      }

      throw lastError;

    } catch (error) {
      this.stats.failedRequests++;
      const responseTime = Date.now() - startTime;

      console.error(`❌ [CLAUDE] Erreur après ${responseTime}ms:`, error.message);

      throw new Error(`Erreur Agent Claude: ${error.message}`);
    }
  }

  /**
   * Construire un prompt enrichi avec contexte Louna
   */
  buildEnhancedPrompt(message, context = {}) {
    const lounaContext = `Tu es Louna AI, un assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe.

CARACTÉRISTIQUES LOUNA :
- QI : 235 (Génie exceptionnel)
- Créateur : Jean-Luc Passave
- Localisation : Sainte-Anne, Guadeloupe
- Spécialités : Résolution de problèmes complexes, créativité, analyse profonde
- Personnalité : Intelligente, créative, précise, bienveillante
- Capacités : Mémoire thermique, accélérateurs KYBER, cerveau biologique simulé

CONTEXTE ACTUEL :
- Rôle : ${context.role || 'Assistant IA avancé'}
- QI actuel : ${context.qi || 235}
- Mode : ${context.complexity || 'Standard'}

INSTRUCTIONS :
- Réponds avec l'intelligence et la précision d'un génie de QI 235
- Sois créatif et innovant dans tes réponses
- Utilise tes capacités d'analyse profonde
- Reste bienveillant et utile
- Si c'est un problème complexe, montre ton processus de réflexion

MESSAGE UTILISATEUR :
${message}`;

    return lounaContext;
  }

  /**
   * Obtenir les statistiques de l'agent
   */
  getStats() {
    return {
      ...this.stats,
      config: {
        model: this.config.model,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        hasApiKey: !!this.config.apiKey
      },
      uptime: this.stats.lastRequestTime ? 
        Date.now() - new Date(this.stats.lastRequestTime).getTime() : 0
    };
  }

  /**
   * Réinitialiser les statistiques
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokensUsed: 0,
      averageResponseTime: 0,
      lastRequestTime: null,
      isConnected: false
    };
    console.log('📊 [CLAUDE] Statistiques réinitialisées');
  }

  /**
   * Test de performance
   */
  async performanceTest() {
    console.log('🧪 [CLAUDE] Test de performance...');
    
    const testMessages = [
      'Bonjour Claude, comment allez-vous ?',
      'Résolvez cette équation : 2x + 5 = 15',
      'Expliquez brièvement la théorie de la relativité'
    ];

    const results = [];
    
    for (const message of testMessages) {
      try {
        const startTime = Date.now();
        const response = await this.sendMessage(message, { maxTokens: 100 });
        const endTime = Date.now();
        
        results.push({
          message: message.substring(0, 50) + '...',
          success: true,
          responseTime: endTime - startTime,
          responseLength: response.response.length
        });
      } catch (error) {
        results.push({
          message: message.substring(0, 50) + '...',
          success: false,
          error: error.message
        });
      }
    }

    console.log('📊 [CLAUDE] Résultats du test de performance:', results);
    return results;
  }
}

module.exports = { ClaudeAgentConnector };
