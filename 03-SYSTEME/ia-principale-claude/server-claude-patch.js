/**
 * PATCH SERVEUR CLAUDE v2.1.0
 * Ajout de la vraie connexion Claude au serveur Louna
 * Créé par <PERSON>, Guadeloupe
 */

// Import du connecteur Claude
const { ClaudeAgentConnector } = require('./claude-agent-connector');

// Initialiser le connecteur Claude
const claudeConnector = new ClaudeAgentConnector();

// VRAIE ROUTE /api/chat POUR CLAUDE
const addClaudeRoutes = (app) => {

    // Route principale pour le chat avec Claude
    app.post('/api/chat', async (req, res) => {
        try {
            const { message, testMode } = req.body;

            if (!message) {
                return res.status(400).json({
                    success: false,
                    error: 'Message requis'
                });
            }

            console.log(`🤖 [CLAUDE] Message reçu: "${message}"`);
            console.log(`🔍 [CLAUDE] Mode test: ${testMode ? 'Oui' : 'Non'}`);

            // Vérifier d'abord si <PERSON> est disponible
            const availability = await claudeConnector.isClaudeAvailable();

            if (!availability.available) {
                console.log(`❌ [CLAUDE] Indisponible: ${availability.reason}`);

                return res.status(503).json({
                    success: false,
                    error: `Agent Claude indisponible: ${availability.reason}`,
                    suggestion: availability.suggestion,
                    fallbackAvailable: true,
                    source: 'Claude Connection Check'
                });
            }

            // Envoyer le message à Claude
            console.log(`📡 [CLAUDE] Envoi du message à l'API Anthropic...`);

            const startTime = Date.now();
            const result = await claudeConnector.sendMessage(message);
            const responseTime = Date.now() - startTime;

            console.log(`✅ [CLAUDE] Réponse reçue en ${responseTime}ms (${result.response.length} caractères)`);

            res.json({
                success: true,
                response: result.response,
                model: result.model,
                source: result.source,
                usage: result.usage,
                responseTime: responseTime,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error(`❌ [CLAUDE] Erreur: ${error.message}`);

            res.status(500).json({
                success: false,
                error: error.message,
                source: 'Claude API Error',
                fallbackAvailable: true,
                timestamp: new Date().toISOString()
            });
        }
    });

    // Route de diagnostic Claude
    app.get('/api/claude/diagnostic', async (req, res) => {
        try {
            console.log(`🔍 [CLAUDE] Démarrage diagnostic complet...`);

            const diagnostic = await claudeConnector.runDiagnostic();

            console.log(`📊 [CLAUDE] Diagnostic terminé: ${diagnostic.analysis.overallStatus}`);

            res.json(diagnostic);
        } catch (error) {
            console.error(`❌ [CLAUDE] Erreur diagnostic: ${error.message}`);

            res.status(500).json({
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    });

    // Route de statut Claude
    app.get('/api/claude/status', async (req, res) => {
        try {
            const availability = await claudeConnector.isClaudeAvailable();

            console.log(`📡 [CLAUDE] Statut: ${availability.available ? 'Disponible' : 'Indisponible'}`);

            res.json({
                ...availability,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error(`❌ [CLAUDE] Erreur statut: ${error.message}`);

            res.status(500).json({
                available: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    });

    // Route de test de connexion
    app.post('/api/claude/test', async (req, res) => {
        try {
            const testMessage = req.body.message || "Bonjour Louna, peux-tu confirmer que tu es bien connectée ?";

            console.log(`🧪 [CLAUDE] Test de connexion avec message: "${testMessage}"`);

            const result = await claudeConnector.sendMessage(testMessage);

            console.log(`✅ [CLAUDE] Test réussi`);

            res.json({
                success: true,
                testMessage: testMessage,
                response: result.response,
                model: result.model,
                source: result.source,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error(`❌ [CLAUDE] Test échoué: ${error.message}`);

            res.status(500).json({
                success: false,
                testMessage: req.body.message,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    });

    // Route de configuration (mode local uniquement)
    app.get('/api/config/check', (req, res) => {
        console.log(`🔑 [CONFIG] Mode local - Aucune clé API requise`);

        res.json({
            localMode: true,
            hasValidKeys: true, // Toujours vrai en mode local
            keys: {
                local: true,
                ollama: true
            },
            recommendations: {
                local: 'Mode local activé',
                ollama: 'Utilisation d\'Ollama en local'
            },
            timestamp: new Date().toISOString()
        });
    });

    // Route de santé générale
    app.get('/api/health', (req, res) => {
        res.json({
            status: 'healthy',
            service: 'Louna AI Server',
            version: '3.0.0',
            mode: 'LOCAL_ONLY',
            claude: {
                connector: 'loaded',
                localMode: true
            },
            ollama: {
                available: true,
                url: 'http://localhost:11434'
            },
            timestamp: new Date().toISOString()
        });
    });

    // Route ping pour tests de latence
    app.get('/api/ping', (req, res) => {
        res.json({
            pong: true,
            timestamp: new Date().toISOString()
        });
    });

    console.log(`🤖 [CLAUDE] Routes ajoutées au serveur:`);
    console.log(`   POST /api/chat - Chat principal avec Claude`);
    console.log(`   GET  /api/claude/diagnostic - Diagnostic complet`);
    console.log(`   GET  /api/claude/status - Statut de disponibilité`);
    console.log(`   POST /api/claude/test - Test de connexion`);
    console.log(`   GET  /api/config/check - Vérification configuration`);
    console.log(`   GET  /api/health - Santé du serveur`);
    console.log(`   GET  /api/ping - Test de latence`);
};

module.exports = { addClaudeRoutes, claudeConnector };
