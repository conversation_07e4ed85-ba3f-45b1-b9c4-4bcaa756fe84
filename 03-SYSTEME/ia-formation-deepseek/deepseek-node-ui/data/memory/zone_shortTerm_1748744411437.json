{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748744363827_p1c0ccrf6", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:19:23.827Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:19:23.827Z"}, {"id": "mem_1748744363827_z8wxzz1rc", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:19:23.827Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:19:23.827Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T02:20:11.437Z", "kyberAccelerated": true}