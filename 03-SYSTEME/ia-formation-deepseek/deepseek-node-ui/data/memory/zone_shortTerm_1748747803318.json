{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748747751702_pmmvjaho9", "content": "Montage invisible vs montage expressif. Jump cuts, match cuts, cross-cutting, montage parallèle. Rythme et tempo : accélération, ralentissement, pauses. Transitions créatives : wipe, iris, morphing. Montage selon l'émotion et le genre.", "type": "knowledge_transfer", "timestamp": "2025-06-01T03:15:51.702Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T03:15:51.702Z"}, {"id": "mem_1748747751702_0ujs<PERSON>jc", "content": "Pipeline de post-production : ingestion, organisation, montage offline/online, étalonnage, mixage, export. Gestion des médias, proxies, métadonnées. Collaboration équipe, versions, sauvegarde. Optimisation performance et qualité.", "type": "knowledge_transfer", "timestamp": "2025-06-01T03:15:51.702Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T03:15:51.702Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T03:16:43.318Z", "kyberAccelerated": true}