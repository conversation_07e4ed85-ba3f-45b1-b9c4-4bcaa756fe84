{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748744033783_dfs77hbt0", "content": "Correction primaire : exposition, contraste, balance des blancs. Correction secondaire : masques, tracking, isolement couleurs. Look créatif : L<PERSON>s, courbes, roues chromatiques. Cohérence visuelle et ambiance narrative.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:13:53.783Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:13:53.783Z"}, {"id": "mem_1748744033783_i7m8spew0", "content": "Montage invisible vs montage expressif. Jump cuts, match cuts, cross-cutting, montage parallèle. Rythme et tempo : accélération, ralentissement, pauses. Transitions créatives : wipe, iris, morphing. Montage selon l'émotion et le genre.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:13:53.783Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:13:53.783Z"}, {"id": "mem_1748744063783_gtdq7es3b", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:14:23.783Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:14:23.783Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T02:14:54.906Z", "kyberAccelerated": true}