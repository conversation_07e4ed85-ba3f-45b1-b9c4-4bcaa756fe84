{
  "timestamp": "2025-06-01T10:04:17.745Z",
  "zones": {
    "instant": [
      {
        "id": "mem_1748772257446_sqo4a85f0",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T10:04:17.446Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T10:04:17.446Z"
      }
    ]
  },
  "stats": {
    "totalEntries": 114289,
    "cyclesPerformed": 3649,
    "lastCycle": "2025-06-01T10:04:17.589Z",
    "averageTemperature": 37,
    "kyberOperations": 282722,
    "fluidSaves": 58221,
    "acceleratorUsage": 283459,
    "totalSaves": 58188,
    "totalLoads": 0,
    "lastSave": "2025-06-01T10:04:17.666Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 3245.************
  },
  "kyberAccelerated": true
}s": 58188,
    "totalLoads": 0,
    "lastSave": "2025-06-01T10:04:17.666Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 3245.************
  },
  "kyberAccelerated": true
}erated": true
}