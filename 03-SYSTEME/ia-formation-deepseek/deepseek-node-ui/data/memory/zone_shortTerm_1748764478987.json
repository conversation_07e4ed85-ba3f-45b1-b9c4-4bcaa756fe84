{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748764418244_f0vzh0vvo", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:53:38.244Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:53:38.244Z"}, {"id": "mem_1748764418244_36crsghdk", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:53:38.244Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:53:38.244Z"}, {"id": "mem_1748764448244_90jhojdzo", "content": "Création personnages complexes : backstory, motivations conscientes/inconscientes, défauts, forces. Archétypes universels, personnalité unique. Relations interpersonnelles, dynamiques de groupe. Évolution psychologique crédible.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:54:08.244Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:54:08.244Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T07:54:38.987Z", "kyberAccelerated": true}