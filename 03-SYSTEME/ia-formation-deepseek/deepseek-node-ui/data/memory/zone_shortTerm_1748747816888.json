{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748747751702_pmmvjaho9", "content": "Montage invisible vs montage expressif. Jump cuts, match cuts, cross-cutting, montage parallèle. Rythme et tempo : accélération, ralentissement, pauses. Transitions créatives : wipe, iris, morphing. Montage selon l'émotion et le genre.", "type": "knowledge_transfer", "timestamp": "2025-06-01T03:15:51.702Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T03:15:51.702Z"}, {"id": "mem_1748747751702_0ujs<PERSON>jc", "content": "Pipeline de post-production : ingestion, organisation, montage offline/online, étalonnage, mixage, export. Gestion des médias, proxies, métadonnées. Collaboration équipe, versions, sauvegarde. Optimisation performance et qualité.", "type": "knowledge_transfer", "timestamp": "2025-06-01T03:15:51.702Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T03:15:51.702Z"}, {"id": "mem_1748747781703_x9xqoksjv", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T03:16:21.703Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T03:16:21.703Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T03:16:56.888Z", "kyberAccelerated": true}