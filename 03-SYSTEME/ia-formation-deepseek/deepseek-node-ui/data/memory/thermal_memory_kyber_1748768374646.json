{
  "timestamp": "2025-06-01T08:59:34.646Z",
  "zones": {
    "instant": [
      {
        "id": "mem_1748768374592_2ovrim686",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T08:59:34.592Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T08:59:34.592Z"
      }
    ]
  },
  "stats": {
    "totalEntries": 61698,
    "cyclesPerformed": 3369,
    "lastCycle": "2025-06-01T08:59:34.320Z",
    "averageTemperature": 37,
    "kyberOperations": 192232,
    "fluidSaves": 31365,
    "acceleratorUsage": 192840,
    "totalSaves": 31351,
    "totalLoads": 0,
    "lastSave": "2025-06-01T08:59:34.592Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2976.************
  },
  "kyberAccelerated": true
}s": 31351,
    "totalLoads": 0,
    "lastSave": "2025-06-01T08:59:34.592Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2976.************
  },
  "kyberAccelerated": true
}erated": true
}