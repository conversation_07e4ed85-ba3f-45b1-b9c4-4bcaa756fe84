{
  "timestamp": "2025-06-01T08:42:53.904Z",
  "zones": {
    "instant": [
      {
        "id": "mem_1748767373853_hmqhwd0b1",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T08:42:53.853Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T08:42:53.853Z"
      }
    ]
  },
  "stats": {
    "totalEntries": 47143,
    "cyclesPerformed": 3293,
    "lastCycle": "2025-06-01T08:42:50.134Z",
    "averageTemperature": 37,
    "kyberOperations": 167291,
    "fluidSaves": 23966,
    "acceleratorUsage": 167876,
    "totalSaves": 23958,
    "totalLoads": 0,
    "lastSave": "2025-06-01T08:42:53.886Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2902.************
  },
  "kyberAccelerated": true
}s": 23958,
    "totalLoads": 0,
    "lastSave": "2025-06-01T08:42:53.886Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2902.************
  },
  "kyberAccelerated": true
}