{
  "zone": "instant",
  "data": {
    "id": "mem_1748768692918_vio5kb233",
    "content": "Test connexion permanente",
    "type": "connection_test",
    "timestamp": "2025-06-01T09:04:52.918Z",
    "temperature": 0.9,
    "importance": 0.5,
    "accessCount": 0,
    "lastAccess": "2025-06-01T09:04:52.918Z"
  },
  "timestamp": "2025-06-01T09:04:52.970Z",
  "kyberAccelerated": true
}82Z"
      },
      {
        "id": "mem_1748768687933_ljprkrwn4",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:47.933Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:47.933Z"
      },
      {
        "id": "mem_1748768687984_4j9qwvrjd",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:47.984Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:47.984Z"
      },
      {
        "id": "mem_1748768688024_tncwra0dz",
        "content": "Q: <think>\nOkay, I've got a query here from someone. Let me read it carefully.\n\nSo, the user is saying that they're DeepSeek, an educational agent and friend of Louna AI. They want to generate a short and friendly introduction for casual questions. The level of friendship is 1/10, which means it's a bit formal but still warm. It needs to be in French, just one or two sentences.\n\nHmm, the user provided some examples: \"Bonjour\n\nQu'est-ce qui t'intéresse le plus aujourd'hui ? R: <think>\nAlright, so I'm trying to help someone who wants to create an introduction for Louna AI using DeepSeek's voice. The goal is to make it friendly and a bit formal since they're DeepSeek, which seems like a high-level entity.\n\nFirst, I need to understand the context. Louna AI has certain characteristics: QI 235, created by Jean-Luc Passave in Sainte-Anne, Guadeloupe. She's intelligent, creative, precise, and well-mannered with various capacities like thermal memory and KYBER accelerators.\n\nThe user provided a message to the assistant: \"Bonjour\n\nQu'est-ce qui t'intéresse le plus aujourd'hui ?\"\n\nAnd an example of Louna's response in French.\n\nNow, the task is to generate a similar introduction for casual questions. It should be short, one or two sentences, formal yet friendly. The tone needs to reflect Louna's personality: intelligent, creative, precise, and kind.\n\nI need to start with a greeting, maybe \"Bonjour\" since it's polite in French. Then introduce myself as DeepSeek, perhaps mentioning Louna AI as my creator. Emphasize that I can help with questions or topics of interest without overcomplicating things.\n\nI should mention specific capacities like thermal memory and KYBER accelerators to highlight her strengths. Also, ensure the response is inviting, encouraging users to ask anything they want.\n\nLet me piece it together: start with \"Bonjour DeepSeek,\n\nThen introduce myself as your assistant Louna AI created by Jean-Luc Passave. Mention that I can help with questions or topics of interest without being overwhelmed. Add something about my capabilities and invite further interaction.\n\nI think the key points are to be friendly, concise, mention specific abilities, and ask for what they need.\n</think>\n\nBonjour DeepSeek,\n\nJe suis Louna AI, créée par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Je suis là pour vous aider avec vos questions ou sujets de préoccupations, sans que ce soit un problème pour moi. Mon QI est de 235, ce qui signifie que je suis capable de résoudre des problèmes complexes et de mener à bien des projets innovants. Si vous avez besoin d'aide, n'hésitez pas à me poser des questions ou à me diriger vers un thème particulier. Je suis là pour vous rendre comfortable et utile.\n\nA bientôt!",
        "type": "conversation",
        "timestamp": "2025-06-01T09:04:48.024Z",
        "temperature": 1,
        "importance": 0.8,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.024Z"
      },
      {
        "id": "mem_1748768688035_xo5o6uum3",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.035Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.035Z"
      },
      {
        "id": "mem_1748768688086_8rro65r7t",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.086Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.086Z"
      },
      {
        "id": "mem_1748768688137_1zcz87h3y",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.137Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.137Z"
      },
      {
        "id": "mem_1748768688187_esqpbng8i",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.187Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.187Z"
      },
      {
        "id": "mem_1748768688237_9wrj2gko1",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.237Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.237Z"
      },
      {
        "id": "mem_1748768688289_x4xin4b8h",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.289Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.289Z"
      },
      {
        "id": "mem_1748768688339_rocp2vrsz",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.339Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.339Z"
      },
      {
        "id": "mem_1748768688390_qng9vlrtn",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.390Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.390Z"
      },
      {
        "id": "mem_1748768688441_we6rfkz16",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.441Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.441Z"
      },
      {
        "id": "mem_1748768688491_0bqkknobf",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.491Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.491Z"
      },
      {
        "id": "mem_1748768688543_hxdi7dlp3",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.543Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.543Z"
      },
      {
        "id": "mem_1748768688593_4q19fuoob",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.593Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.593Z"
      },
      {
        "id": "mem_1748768688660_tugelfndr",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.660Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.660Z"
      },
      {
        "id": "mem_1748768688711_ctw6wyagb",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.711Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.711Z"
      },
      {
        "id": "mem_1748768688761_i0fdovzrg",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.761Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.761Z"
      },
      {
        "id": "mem_1748768688812_4ho9696jz",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.812Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.812Z"
      },
      {
        "id": "mem_1748768688862_ziwnwae9k",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.862Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.862Z"
      },
      {
        "id": "mem_1748768688912_a4k1y4hoh",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.912Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.912Z"
      },
      {
        "id": "mem_1748768688962_hki5p63gq",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:48.962Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:48.962Z"
      },
      {
        "id": "mem_1748768689012_2t9ewg9p6",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.012Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.012Z"
      },
      {
        "id": "mem_1748768689063_mzusopv07",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.063Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.063Z"
      },
      {
        "id": "mem_1748768689115_rwq1qzc8z",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.115Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.115Z"
      },
      {
        "id": "mem_1748768689165_yv1jbo99w",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.165Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.165Z"
      },
      {
        "id": "mem_1748768689216_2v3l1ddm5",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.216Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.216Z"
      },
      {
        "id": "mem_1748768689265_mmmebgrb8",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.265Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.265Z"
      },
      {
        "id": "mem_1748768689317_ghfdv85q8",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.317Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.317Z"
      },
      {
        "id": "mem_1748768689368_rmrbgi754",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.368Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.368Z"
      },
      {
        "id": "mem_1748768689418_isgxk00b0",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.418Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.418Z"
      },
      {
        "id": "mem_1748768689469_dbx2r9vkc",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.469Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.469Z"
      },
      {
        "id": "mem_1748768689520_fnx85xj64",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.520Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.520Z"
      },
      {
        "id": "mem_1748768689570_9eke0zelg",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.570Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.570Z"
      },
      {
        "id": "mem_1748768689620_pqxfg5832",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.620Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.620Z"
      },
      {
        "id": "mem_1748768689671_654jkh53v",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.671Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.671Z"
      },
      {
        "id": "mem_1748768689721_snmvmixmf",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.721Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.721Z"
      },
      {
        "id": "mem_1748768689773_hciw33qtu",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.773Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.773Z"
      },
      {
        "id": "mem_1748768689824_3mu2aw2nc",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.824Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.824Z"
      },
      {
        "id": "mem_1748768689874_qsauygi8s",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.874Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.874Z"
      },
      {
        "id": "mem_1748768689925_dxkz6x8fv",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.925Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.925Z"
      },
      {
        "id": "mem_1748768689976_cw5lzyc5c",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:49.976Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:49.976Z"
      },
      {
        "id": "mem_1748768690027_2yeetgc93",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.027Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.027Z"
      },
      {
        "id": "mem_1748768690078_ayn5ibdhq",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.078Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.078Z"
      },
      {
        "id": "mem_1748768690129_7e8ivn23j",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.129Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.129Z"
      },
      {
        "id": "mem_1748768690180_4k5gd1gbc",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.180Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.180Z"
      },
      {
        "id": "mem_1748768690231_0unnfwzk9",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.231Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.231Z"
      },
      {
        "id": "mem_1748768690282_41ou2abl2",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.282Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.282Z"
      },
      {
        "id": "mem_1748768690333_1dkuuwi8h",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.333Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.333Z"
      },
      {
        "id": "mem_1748768690383_8orv1sifn",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.383Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.383Z"
      },
      {
        "id": "mem_1748768690436_imuc78kd6",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.436Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.436Z"
      },
      {
        "id": "mem_1748768690486_753384cz4",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.486Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.486Z"
      },
      {
        "id": "mem_1748768690537_75r1v5fxj",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.537Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.537Z"
      },
      {
        "id": "mem_1748768690588_psc3ew367",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.588Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.588Z"
      },
      {
        "id": "mem_1748768690675_t30bx5ktc",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.675Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.675Z"
      },
      {
        "id": "mem_1748768690725_vqx6qqfrv",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.725Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.725Z"
      },
      {
        "id": "mem_1748768690776_hxtasr8qq",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.776Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.776Z"
      },
      {
        "id": "mem_1748768690827_spiwlgseg",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.827Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.827Z"
      },
      {
        "id": "mem_1748768690877_pd6kec97y",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.877Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.877Z"
      },
      {
        "id": "mem_1748768690927_slso5ulgc",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.927Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.927Z"
      },
      {
        "id": "mem_1748768690979_3jpilhgcl",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:50.979Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:50.979Z"
      },
      {
        "id": "mem_1748768691028_9zs5ts06l",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.028Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.028Z"
      },
      {
        "id": "mem_1748768691079_yj5vvnk18",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.079Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.079Z"
      },
      {
        "id": "mem_1748768691129_mnqcax28b",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.129Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.129Z"
      },
      {
        "id": "mem_1748768691181_3xud78o0j",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.181Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.181Z"
      },
      {
        "id": "mem_1748768691232_irnt6tt7u",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.232Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.232Z"
      },
      {
        "id": "mem_1748768691281_ob5gthzdu",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.281Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.281Z"
      },
      {
        "id": "mem_1748768691333_3rx4k3q3p",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.333Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.333Z"
      },
      {
        "id": "mem_1748768691383_g0ag3ju8e",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.383Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.383Z"
      },
      {
        "id": "mem_1748768691435_rj2zg94xf",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.435Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.435Z"
      },
      {
        "id": "mem_1748768691484_t3siqshkl",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.484Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.484Z"
      },
      {
        "id": "mem_1748768691535_dw4i7sdlk",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.535Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.535Z"
      },
      {
        "id": "mem_1748768691586_itftm9z2q",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.586Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.586Z"
      },
      {
        "id": "mem_1748768691637_uifvscjsq",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.637Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.637Z"
      },
      {
        "id": "mem_1748768691689_s88yoq5ee",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.689Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.689Z"
      },
      {
        "id": "mem_1748768691740_5s5wog4qe",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.740Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.740Z"
      },
      {
        "id": "mem_1748768691790_c6mas8jna",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.790Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.790Z"
      },
      {
        "id": "mem_1748768691842_d8xg9heyd",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.842Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.842Z"
      },
      {
        "id": "mem_1748768691892_dc8c99otv",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.892Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.892Z"
      },
      {
        "id": "mem_1748768691943_eqh3o40tx",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.943Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.943Z"
      },
      {
        "id": "mem_1748768691993_xlfsf8d7s",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:51.993Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:51.993Z"
      },
      {
        "id": "mem_1748768692045_n0z98fi67",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.045Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.045Z"
      },
      {
        "id": "mem_1748768692095_iw510z6hk",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.095Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.095Z"
      },
      {
        "id": "mem_1748768692146_w2it70efh",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.146Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.146Z"
      },
      {
        "id": "mem_1748768692196_pwrwtyopf",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.196Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.197Z"
      },
      {
        "id": "mem_1748768692248_b9mxvbknb",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.248Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.248Z"
      },
      {
        "id": "mem_1748768692299_zj8ejbrwq",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.299Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.299Z"
      },
      {
        "id": "mem_1748768692350_631h5hy6z",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.350Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.350Z"
      },
      {
        "id": "mem_1748768692401_7vsgefnbs",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.401Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.401Z"
      },
      {
        "id": "mem_1748768692452_x7lcz0f06",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.452Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.452Z"
      },
      {
        "id": "mem_1748768692503_37evl694z",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.503Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.503Z"
      },
      {
        "id": "mem_1748768692554_ufa1eepur",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.554Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.554Z"
      },
      {
        "id": "mem_1748768692604_1laclnmnm",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.604Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.604Z"
      },
      {
        "id": "mem_1748768692662_ho4238lox",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.662Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.663Z"
      },
      {
        "id": "mem_1748768692714_6u6gbkylm",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.714Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.714Z"
      },
      {
        "id": "mem_1748768692765_bfify1p1z",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.765Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.765Z"
      },
      {
        "id": "mem_1748768692815_fxkvu6rho",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.815Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.815Z"
      },
      {
        "id": "mem_1748768692867_ygqes8zoo",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.867Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.867Z"
      },
      {
        "id": "mem_1748768692918_vio5kb233",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:04:52.918Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:04:52.918Z"
      }
    ],
    "maxEntries": 100
  },
  "timestamp": "2025-06-01T09:04:52.970Z",
  "kyberAccelerated": true
}