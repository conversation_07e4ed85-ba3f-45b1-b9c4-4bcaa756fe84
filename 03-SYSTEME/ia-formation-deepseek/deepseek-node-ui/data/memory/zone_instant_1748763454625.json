{"zone": "instant", "data": {"temperature": 100, "entries": [{"id": "mem_1748763449209_krjh23alk", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:37:29.209Z", "temperature": 1, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:37:29.209Z"}, {"id": "mem_1748763449209_ko8iax81v", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:37:29.209Z", "temperature": 0.95, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:37:29.209Z"}], "maxEntries": 100}, "timestamp": "2025-06-01T07:37:34.625Z", "kyberAccelerated": true}