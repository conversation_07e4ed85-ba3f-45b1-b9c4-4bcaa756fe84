{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748744363827_z8wxzz1rc", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:19:23.827Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:19:23.827Z"}, {"id": "mem_1748744393828_0em65oddt", "content": "Création personnages complexes : backstory, motivations conscientes/inconscientes, défauts, forces. Archétypes universels, personnalité unique. Relations interpersonnelles, dynamiques de groupe. Évolution psychologique crédible.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:19:53.828Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:19:53.828Z"}, {"id": "mem_1748744393828_p7rv30jcr", "content": "Show don't tell, symbolisme visuel, métaphores. Storyboard, animatique, prévisualisation. <PERSON><PERSON><PERSON><PERSON> visuel, ellipses temporelles, flashbacks. Narration non-linéaire, perspectives multiples, narrateur non-fiable.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:19:53.828Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:19:53.828Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T02:20:26.476Z", "kyberAccelerated": true}