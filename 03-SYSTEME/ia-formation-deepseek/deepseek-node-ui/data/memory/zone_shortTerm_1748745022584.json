{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748744963831_vqzmh727c", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:29:23.831Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:29:23.831Z"}, {"id": "mem_1748744963831_w693pijlh", "content": "Intégration éléments 3D/2D, green screen, tracking, rotoscoping. Simulation particules, fluides, destruction. Rendu photoréaliste : éclairage, matériaux, textures. Pipeline VFX : previs, tournage, post-production.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:29:23.831Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:29:23.831Z"}, {"id": "mem_1748744993832_1mk877l7s", "content": "Création personnages complexes : backstory, motivations conscientes/inconscientes, défauts, forces. Archétypes universels, personnalité unique. Relations interpersonnelles, dynamiques de groupe. Évolution psychologique crédible.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:29:53.832Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:29:53.832Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T02:30:22.584Z", "kyberAccelerated": true}