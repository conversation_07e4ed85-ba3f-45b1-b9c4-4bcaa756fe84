{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748761649132_lg0gfmyhy", "content": "Intégration éléments 3D/2D, green screen, tracking, rotoscoping. Simulation particules, fluides, destruction. Rendu photoréaliste : éclairage, matériaux, textures. Pipeline VFX : previs, tournage, post-production.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:07:29.132Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:07:29.132Z"}, {"id": "mem_1748761679132_3q0opy5z1", "content": "Création personnages complexes : backstory, motivations conscientes/inconscientes, défauts, forces. Archétypes universels, personnalité unique. Relations interpersonnelles, dynamiques de groupe. Évolution psychologique crédible.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:07:59.132Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:07:59.132Z"}, {"id": "mem_1748761679132_wai8nv368", "content": "Acte 1-2-3, voyage du héros, arc narratif. Développement personnages : motivation, conflit, évolution. Dialogue naturel, sous-texte, révélations. Tension dramatique, climax, résolution. Adaptation selon le médium.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:07:59.132Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:07:59.132Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T07:08:46.454Z", "kyberAccelerated": true}