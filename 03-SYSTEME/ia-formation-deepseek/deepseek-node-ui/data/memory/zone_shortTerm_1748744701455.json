{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748744633830_tw7uorax5", "content": "Montage invisible vs montage expressif. Jump cuts, match cuts, cross-cutting, montage parallèle. Rythme et tempo : accélération, ralentissement, pauses. Transitions créatives : wipe, iris, morphing. Montage selon l'émotion et le genre.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:23:53.830Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:23:53.830Z"}, {"id": "mem_1748744663829_35it2omwy", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:24:23.829Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:24:23.829Z"}, {"id": "mem_1748744663829_4rmczc9cz", "content": "Intégration éléments 3D/2D, green screen, tracking, rotoscoping. Simulation particules, fluides, destruction. Rendu photoréaliste : éclairage, matériaux, textures. Pipeline VFX : previs, tournage, post-production.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:24:23.829Z", "temperature": 0.8145062499999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:24:23.829Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T02:25:01.455Z", "kyberAccelerated": true}