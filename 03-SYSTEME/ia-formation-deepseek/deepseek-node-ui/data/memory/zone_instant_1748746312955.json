{"zone": "instant", "data": {"temperature": 100, "entries": [{"id": "mem_1748746282770_ou2retxc6", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:51:22.770Z", "temperature": 0.9025, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:51:22.770Z"}, {"id": "mem_1748746312770_gbl4r58ba", "content": "Show don't tell, symbolisme visuel, métaphores. Storyboard, animatique, prévisualisation. <PERSON><PERSON><PERSON><PERSON> visuel, ellipses temporelles, flashbacks. Narration non-linéaire, perspectives multiples, narrateur non-fiable.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:51:52.770Z", "temperature": 1, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:51:52.770Z"}, {"id": "mem_1748746312770_wp2yr8j36", "content": "Création personnages complexes : backstory, motivations conscientes/inconscientes, défauts, forces. Archétypes universels, personnalité unique. Relations interpersonnelles, dynamiques de groupe. Évolution psychologique crédible.", "type": "knowledge_transfer", "timestamp": "2025-06-01T02:51:52.770Z", "temperature": 1, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T02:51:52.770Z"}], "maxEntries": 100}, "timestamp": "2025-06-01T02:51:52.955Z", "kyberAccelerated": true}