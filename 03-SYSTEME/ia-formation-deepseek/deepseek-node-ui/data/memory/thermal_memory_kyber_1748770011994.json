{
  "timestamp": "2025-06-01T09:26:51.994Z",
  "zones": {
    "instant": [
      {
        "id": "mem_1748770011892_xhkkmq47f",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:26:51.892Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:26:51.892Z"
      }
    ]
  },
  "stats": {
    "totalEntries": 87731,
    "cyclesPerformed": 3506,
    "lastCycle": "2025-06-01T09:26:45.172Z",
    "averageTemperature": 37,
    "kyberOperations": 237024,
    "fluidSaves": 44661,
    "acceleratorUsage": 237673,
    "totalSaves": 44646,
    "totalLoads": 0,
    "lastSave": "2025-06-01T09:26:51.905Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 3109.************
  },
  "kyberAccelerated": true
}s": 44646,
    "totalLoads": 0,
    "lastSave": "2025-06-01T09:26:51.905Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 3109.************
  },
  "kyberAccelerated": true
}