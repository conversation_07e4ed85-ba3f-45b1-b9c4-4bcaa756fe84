{"zone": "shortTerm", "data": {"temperature": 80, "entries": [{"id": "mem_1748764388243_pj6vb0qdc", "content": "Montage invisible vs montage expressif. Jump cuts, match cuts, cross-cutting, montage parallèle. Rythme et tempo : accélération, ralentissement, pauses. Transitions créatives : wipe, iris, morphing. Montage selon l'émotion et le genre.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:53:08.243Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:53:08.243Z"}, {"id": "mem_1748764418244_f0vzh0vvo", "content": "Principes animation : timing, spacing, anticipation, follow-through. Keyframes, courbes d'animation, easing. Motion graphics : typographie animée, infographies, transitions. 2D/3D animation, rigging, skinning.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:53:38.244Z", "temperature": 0.7350918906249998, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:53:38.244Z"}, {"id": "mem_1748764418244_36crsghdk", "content": "Physique réaliste : gravité, collisions, friction. Simulation fluides : eau, fumée, feu. Particules : pous<PERSON><PERSON>, étincelles, magie. Cloth simulation, hair dynamics, destruction procédurale. Optimisation calculs temps réel.", "type": "knowledge_transfer", "timestamp": "2025-06-01T07:53:38.244Z", "temperature": 0.7737809374999999, "importance": 0.5, "accessCount": 0, "lastAccess": "2025-06-01T07:53:38.244Z"}], "maxEntries": 500}, "timestamp": "2025-06-01T07:54:32.971Z", "kyberAccelerated": true}