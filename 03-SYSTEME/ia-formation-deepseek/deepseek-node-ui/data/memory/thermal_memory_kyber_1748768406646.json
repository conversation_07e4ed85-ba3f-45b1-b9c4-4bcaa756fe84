{
  "timestamp": "2025-06-01T09:00:06.646Z",
  "zones": {
    "instant": [
      {
        "id": "mem_1748768406544_m0mtaxnav",
        "content": "Test connexion permanente",
        "type": "connection_test",
        "timestamp": "2025-06-01T09:00:06.544Z",
        "temperature": 0.9,
        "importance": 0.5,
        "accessCount": 0,
        "lastAccess": "2025-06-01T09:00:06.544Z"
      }
    ]
  },
  "stats": {
    "totalEntries": 62314,
    "cyclesPerformed": 3372,
    "lastCycle": "2025-06-01T09:00:03.831Z",
    "averageTemperature": 37,
    "kyberOperations": 193294,
    "fluidSaves": 31679,
    "acceleratorUsage": 193906,
    "totalSaves": 31667,
    "totalLoads": 0,
    "lastSave": "2025-06-01T09:00:06.615Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2980.099999998385
  },
  "kyberAccelerated": true
}s": 31667,
    "totalLoads": 0,
    "lastSave": "2025-06-01T09:00:06.615Z",
    "lastLoad": null,
    "totalMemories": 75,
    "zone1Count": 66,
    "zone2Count": 7,
    "zone3Count": 2,
    "zone4Count": 0,
    "zone5Count": 0,
    "zone6Count": 0,
    "creativityBoost": 2980.099999998385
  },
  "kyberAccelerated": true
}