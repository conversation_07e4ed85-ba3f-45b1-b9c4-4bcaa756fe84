const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3005;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Helpers pour les opérations de fichier
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Chemin vers le fichier de configuration
const CONFIG_FILE_PATH = path.join(__dirname, 'config.json');

// Fonction pour charger la configuration (mode local)
async function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = await readFileAsync(CONFIG_FILE_PATH, 'utf8');
      return JSON.parse(configData);
    }
    return {
      localMode: true,
      ollamaUrl: 'http://localhost:11434',
      noApiKeys: true,
      system: {
        name: 'Louna AI V3.0',
        creator: 'Jean-Luc Passave',
        location: 'Sainte-Anne, Guadeloupe'
      }
    };
  } catch (error) {
    console.error('Error loading config:', error);
    return {
      localMode: true,
      ollamaUrl: 'http://localhost:11434',
      noApiKeys: true
    };
  }
}

// Fonction pour sauvegarder la configuration
async function saveConfig(config) {
  try {
    await writeFileAsync(CONFIG_FILE_PATH, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving config:', error);
    return false;
  }
}

// Variables globales (mode local uniquement)
let LOCAL_MODE = true;
let OLLAMA_API_URL = 'http://localhost:11434';

// Charger la configuration au démarrage (mode local)
(async () => {
  const config = await loadConfig();
  console.log('Configuration loaded. Mode: LOCAL ONLY');
  console.log('Ollama URL:', OLLAMA_API_URL);
  console.log('No API keys required');

  // Mettre à jour la configuration pour le mode local
  config.localMode = true;
  config.ollamaUrl = OLLAMA_API_URL;
  config.noApiKeys = true;
  await saveConfig(config);
  console.log('Updated config for local mode');
})();

// Routes
app.get('/', (req, res) => {
  res.render('index', { title: 'DeepSeek r1 Interface' });
});

// Route pour récupérer la configuration (mode local)
app.get('/api/config', async (req, res) => {
  try {
    const config = await loadConfig();
    const localConfig = {
      localMode: true,
      ollamaUrl: config.ollamaUrl || 'http://localhost:11434',
      noApiKeys: true,
      system: config.system || {
        name: 'Louna AI V3.0',
        creator: 'Jean-Luc Passave',
        location: 'Sainte-Anne, Guadeloupe'
      }
    };
    res.json(localConfig);
  } catch (error) {
    res.status(500).json({ error: 'Failed to load configuration' });
  }
});

// Route pour tester Ollama (mode local)
app.get('/api/test-ollama', async (req, res) => {
  try {
    console.log('Testing Ollama connection...');

    try {
      // Tester la connexion à Ollama
      const testResponse = await axios.get(`${OLLAMA_API_URL}/api/tags`, {
        timeout: 5000
      });

      console.log('Ollama test successful');
      console.log('Available models:', testResponse.data);

      return res.json({
        success: true,
        message: 'Ollama is available',
        models: testResponse.data.models || [],
        url: OLLAMA_API_URL
      });
    } catch (ollamaError) {
      console.error('Ollama test failed:', ollamaError.message);

      return res.status(500).json({
        error: 'Ollama not available',
        details: ollamaError.message,
        suggestion: 'Make sure Ollama is running on localhost:11434'
      });
    }
  } catch (error) {
    console.error('Error testing Ollama:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// API pour communiquer avec Ollama (mode local)
app.post('/api/chat', async (req, res) => {
  try {
    const { message, history, model = 'deepseek-r1:7b' } = req.body;

    console.log('Sending message to Ollama...');

    // Construire le prompt avec l'historique
    let fullPrompt = '';
    if (history && history.length > 0) {
      fullPrompt = history.map(msg => `${msg.role}: ${msg.content}`).join('\n') + '\n';
    }
    fullPrompt += `user: ${message}\nassistant: `;

    // Préparer les données pour Ollama
    const requestData = {
      model: model,
      prompt: fullPrompt,
      stream: false,
      options: {
        temperature: 0.7,
        num_predict: 1000
      }
    };

    // Appeler Ollama
    const response = await axios.post(`${OLLAMA_API_URL}/api/generate`, requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });

    // Adapter la réponse au format attendu
    const adaptedResponse = {
      choices: [{
        message: {
          role: 'assistant',
          content: response.data.response
        }
      }],
      model: model,
      usage: {
        prompt_tokens: fullPrompt.length,
        completion_tokens: response.data.response.length,
        total_tokens: fullPrompt.length + response.data.response.length
      }
    };

    return res.json(adaptedResponse);
  } catch (error) {
    console.error('Error calling Ollama:', error.message);
    return res.status(500).json({
      error: 'Failed to communicate with Ollama',
      details: error.message,
      suggestion: 'Make sure Ollama is running and the model is available'
    });
  }
});

// Socket.io pour les communications en temps réel
io.on('connection', (socket) => {
  console.log('New client connected');

  // Envoyer la configuration au client lors de la connexion (mode local)
  (async () => {
    try {
      const config = await loadConfig();
      socket.emit('config', {
        localMode: true,
        ollamaUrl: config.ollamaUrl || 'http://localhost:11434',
        noApiKeys: true,
        system: config.system || {
          name: 'Louna AI V3.0',
          creator: 'Jean-Luc Passave',
          location: 'Sainte-Anne, Guadeloupe'
        }
      });
    } catch (error) {
      console.error('Error sending config to client:', error);
    }
  })();

  // Test de connexion Ollama
  socket.on('test ollama', async () => {
    try {
      const testResponse = await axios.get(`${OLLAMA_API_URL}/api/tags`, {
        timeout: 5000
      });

      socket.emit('ollama test result', {
        success: true,
        message: 'Ollama is available',
        models: testResponse.data.models || [],
        url: OLLAMA_API_URL
      });
    } catch (error) {
      socket.emit('ollama test result', {
        success: false,
        error: 'Ollama not available',
        details: error.message,
        suggestion: 'Make sure Ollama is running on localhost:11434'
      });
    }
  });

  socket.on('chat message', async (data) => {
    try {
      const { message, history, temperature, maxTokens, model = 'deepseek-r1:7b' } = data;

      console.log('Sending message to Ollama...');
      console.log('Ollama URL:', OLLAMA_API_URL);
      console.log('Model:', model);

      // Construire le prompt avec l'historique
      let fullPrompt = '';
      if (history && history.length > 0) {
        fullPrompt = history.map(msg => `${msg.role}: ${msg.content}`).join('\n') + '\n';
      }
      fullPrompt += `user: ${message}\nassistant: `;

      // Préparer les données pour Ollama
      const requestData = {
        model: model,
        prompt: fullPrompt,
        stream: false,
        options: {
          temperature: temperature || 0.7,
          num_predict: maxTokens || 1000
        }
      };

      console.log('Request data:', JSON.stringify(requestData, null, 2));

      // Appeler Ollama
      const response = await axios.post(`${OLLAMA_API_URL}/api/generate`, requestData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      });

      console.log('Response received from Ollama');

      // Adapter la réponse au format attendu
      const adaptedResponse = {
        choices: [{
          message: {
            role: 'assistant',
            content: response.data.response
          }
        }],
        model: model,
        usage: {
          prompt_tokens: fullPrompt.length,
          completion_tokens: response.data.response.length,
          total_tokens: fullPrompt.length + response.data.response.length
        }
      };

      // Envoyer la réponse au client
      socket.emit('chat response', adaptedResponse);
    } catch (error) {
      console.error('Error in socket communication:', error.message);

      // Envoyer des détails plus précis au client
      socket.emit('chat response', {
        error: 'Failed to communicate with Ollama',
        details: error.message,
        suggestion: 'Make sure Ollama is running and the model is available'
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
