Date: 2025-05-18T21:50:29.546Z
Erreur: Unexpected ? at 22, expected END: https://git.new/pathToRegexpError
Stack: TypeError: Unexpected ? at 22, expected END: https://git.new/pathToRegexpError
    at Iter.consume (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:123:15)
    at consume (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:179:16)
    at parse (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:183:20)
    at /Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:294:74
    at Array.map (<anonymous>)
    at pathToRegexp (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:294:25)
    at Object.match (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/path-to-regexp/dist/index.js:264:30)
    at matcher (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/router/lib/layer.js:86:23)
    at new Layer (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/router/lib/layer.js:93:62)
    at Function.route (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/node_modules/router/index.js:428:17)
