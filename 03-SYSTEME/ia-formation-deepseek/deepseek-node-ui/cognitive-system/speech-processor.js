/**
 * Module de traitement vocal pour le système cognitif
 * Permet la reconnaissance vocale et la synthèse vocale
 */

const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const os = require('os');
const EventEmitter = require('events');

// Vérifier les dépendances système
const SYSTEM_DEPENDENCIES = {
  sox: false,         // Pour le traitement audio
  ffmpeg: false,      // Pour la conversion audio
  espeak: false       // Pour la synthèse vocale de base (fallback)
};

class SpeechProcessor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      language: options.language || 'fr-FR',
      voiceName: options.voiceName || 'French', // Pour espeak
      sampleRate: options.sampleRate || 16000,
      channels: options.channels || 1,
      audioFormat: options.audioFormat || 'wav',
      recognitionEngine: options.recognitionEngine || 'vosk', // 'vosk' ou 'whisper'
      synthesisEngine: options.synthesisEngine || 'espeak', // 'espeak', 'pico2wave' ou 'gtts'
      recordingDevice: options.recordingDevice || 'default',
      outputDevice: options.outputDevice || 'default',
      modelPath: options.modelPath || path.join(__dirname, 'models'),
      tempDir: options.tempDir || path.join(os.tmpdir(), 'speech-processor'),
      debug: options.debug || false
    };
    
    // Créer les dossiers nécessaires
    this.ensureDirectories();
    
    // Vérifier les dépendances système
    this.checkSystemDependencies();
    
    // État actuel
    this.state = {
      isRecording: false,
      isSpeaking: false,
      lastRecognitionResult: null,
      lastSynthesisText: null,
      currentAudioFile: null,
      recordingProcess: null,
      speakingProcess: null
    };
    
    console.log(`Processeur vocal initialisé (langue: ${this.options.language})`);
  }
  
  // Créer les dossiers nécessaires
  ensureDirectories() {
    // Dossier temporaire
    if (!fs.existsSync(this.options.tempDir)) {
      fs.mkdirSync(this.options.tempDir, { recursive: true });
    }
    
    // Dossier de modèles
    if (!fs.existsSync(this.options.modelPath)) {
      fs.mkdirSync(this.options.modelPath, { recursive: true });
    }
  }
  
  // Vérifier les dépendances système
  checkSystemDependencies() {
    try {
      // Vérifier SoX
      try {
        const soxVersion = require('child_process').execSync('sox --version', { encoding: 'utf8' });
        if (soxVersion) {
          SYSTEM_DEPENDENCIES.sox = true;
          this.log('SoX détecté:', soxVersion.split('\n')[0]);
        }
      } catch (e) {
        this.log('SoX non détecté. La capture audio pourrait ne pas fonctionner correctement.');
      }
      
      // Vérifier FFmpeg
      try {
        const ffmpegVersion = require('child_process').execSync('ffmpeg -version', { encoding: 'utf8' });
        if (ffmpegVersion) {
          SYSTEM_DEPENDENCIES.ffmpeg = true;
          this.log('FFmpeg détecté:', ffmpegVersion.split('\n')[0]);
        }
      } catch (e) {
        this.log('FFmpeg non détecté. La conversion audio pourrait ne pas fonctionner.');
      }
      
      // Vérifier eSpeak
      try {
        const espeakVersion = require('child_process').execSync('espeak --version', { encoding: 'utf8' });
        if (espeakVersion) {
          SYSTEM_DEPENDENCIES.espeak = true;
          this.log('eSpeak détecté:', espeakVersion.split('\n')[0]);
        }
      } catch (e) {
        this.log('eSpeak non détecté. La synthèse vocale pourrait être limitée.');
      }
    } catch (error) {
      this.log('Erreur lors de la vérification des dépendances:', error);
    }
  }
  
  // Démarrer la reconnaissance vocale
  startListening() {
    if (this.state.isRecording) {
      this.log('Déjà en écoute');
      return false;
    }
    
    try {
      this.state.isRecording = true;
      this.state.currentAudioFile = path.join(this.options.tempDir, `recording_${Date.now()}.${this.options.audioFormat}`);
      
      this.log(`Démarrage de l'écoute (fichier: ${this.state.currentAudioFile})`);
      
      // Utiliser SoX pour enregistrer l'audio
      if (SYSTEM_DEPENDENCIES.sox) {
        const args = [
          '-d', // Périphérique d'entrée par défaut
          '-t', this.options.audioFormat,
          '-c', this.options.channels,
          '-r', this.options.sampleRate,
          this.state.currentAudioFile
        ];
        
        this.state.recordingProcess = spawn('rec', args);
        
        this.state.recordingProcess.stderr.on('data', (data) => {
          this.log(`Enregistrement en cours: ${data}`);
        });
        
        this.state.recordingProcess.on('close', (code) => {
          this.log(`Enregistrement terminé avec le code: ${code}`);
          this.state.isRecording = false;
          
          // Traiter l'audio enregistré
          if (code === 0) {
            this.processRecording();
          }
        });
        
        this.emit('listeningStarted');
        return true;
      } else {
        this.log('SoX non disponible pour l\'enregistrement audio');
        this.state.isRecording = false;
        return false;
      }
    } catch (error) {
      this.log('Erreur lors du démarrage de l\'écoute:', error);
      this.state.isRecording = false;
      return false;
    }
  }
  
  // Arrêter la reconnaissance vocale
  stopListening() {
    if (!this.state.isRecording || !this.state.recordingProcess) {
      this.log('Pas d\'enregistrement en cours');
      return false;
    }
    
    try {
      this.log('Arrêt de l\'écoute');
      
      // Envoyer un signal SIGTERM au processus d'enregistrement
      this.state.recordingProcess.kill();
      
      this.emit('listeningStopped');
      return true;
    } catch (error) {
      this.log('Erreur lors de l\'arrêt de l\'écoute:', error);
      return false;
    }
  }
  
  // Traiter l'enregistrement audio
  processRecording() {
    try {
      this.log('Traitement de l\'enregistrement audio');
      
      // En fonction du moteur de reconnaissance
      switch (this.options.recognitionEngine) {
        case 'vosk':
          // Simulation de reconnaissance vocale avec Vosk (nécessite installation séparée)
          setTimeout(() => {
            const simulatedText = "Ceci est une simulation de reconnaissance vocale.";
            this.state.lastRecognitionResult = simulatedText;
            this.emit('recognitionResult', simulatedText);
          }, 500);
          break;
          
        case 'whisper':
          // Simulation de reconnaissance avec Whisper (nécessite installation séparée)
          setTimeout(() => {
            const simulatedText = "Simulation de reconnaissance avec Whisper.";
            this.state.lastRecognitionResult = simulatedText;
            this.emit('recognitionResult', simulatedText);
          }, 500);
          break;
          
        default:
          // Simulation basique
          setTimeout(() => {
            const simulatedText = "Système de reconnaissance vocale activé.";
            this.state.lastRecognitionResult = simulatedText;
            this.emit('recognitionResult', simulatedText);
          }, 500);
      }
    } catch (error) {
      this.log('Erreur lors du traitement de l\'enregistrement:', error);
    }
  }
  
  // Parler (synthèse vocale)
  speak(text) {
    if (this.state.isSpeaking) {
      this.log('Déjà en train de parler');
      return false;
    }
    
    try {
      this.state.isSpeaking = true;
      this.state.lastSynthesisText = text;
      
      this.log(`Synthèse vocale: "${text}"`);
      
      // En fonction du moteur de synthèse
      switch (this.options.synthesisEngine) {
        case 'espeak':
          if (SYSTEM_DEPENDENCIES.espeak) {
            const args = [
              '-v', this.options.voiceName,
              '-s', '150', // Vitesse de parole
              text
            ];
            
            this.state.speakingProcess = spawn('espeak', args);
            
            this.state.speakingProcess.on('close', (code) => {
              this.log(`Synthèse terminée avec le code: ${code}`);
              this.state.isSpeaking = false;
              this.emit('speakingDone', text);
            });
            
            this.emit('speakingStarted', text);
            return true;
          } else {
            this.log('eSpeak non disponible pour la synthèse vocale');
            this.state.isSpeaking = false;
            return false;
          }
          
        case 'pico2wave':
          // Simulation de synthèse avec Pico2Wave
          this.log('Synthèse avec Pico2Wave simulée');
          setTimeout(() => {
            this.state.isSpeaking = false;
            this.emit('speakingDone', text);
          }, 1000);
          this.emit('speakingStarted', text);
          return true;
          
        case 'gtts':
          // Simulation de synthèse avec Google Text-to-Speech
          this.log('Synthèse avec Google TTS simulée');
          setTimeout(() => {
            this.state.isSpeaking = false;
            this.emit('speakingDone', text);
          }, 1000);
          this.emit('speakingStarted', text);
          return true;
          
        default:
          // Simulation basique
          this.log('Synthèse vocale simulée');
          setTimeout(() => {
            this.state.isSpeaking = false;
            this.emit('speakingDone', text);
          }, 1000);
          this.emit('speakingStarted', text);
          return true;
      }
    } catch (error) {
      this.log('Erreur lors de la synthèse vocale:', error);
      this.state.isSpeaking = false;
      return false;
    }
  }
  
  // Utilitaire de log
  log(message, details = '') {
    if (this.options.debug) {
      console.log(`[SpeechProcessor] ${message}`, details);
    }
  }
}

module.exports = SpeechProcessor;