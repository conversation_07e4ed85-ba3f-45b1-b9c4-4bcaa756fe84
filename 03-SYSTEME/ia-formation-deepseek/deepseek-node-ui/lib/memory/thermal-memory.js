/**
 * 🔥 MÉMOIRE THERMIQUE RÉELLE - LOUNA AI v2.1.0
 * Système de mémoire biologique avec 6 zones thermiques
 */

const fs = require('fs');
const path = require('path');

class ThermalMemory {
  constructor(options = {}) {
    this.config = {
      maxEntries: options.maxEntries || 10000,
      temperatureDecay: options.temperatureDecay || 0.95,
      saveInterval: options.saveInterval || 1000, // 1 seconde - sauvegarde ultra-fluide
      dataDir: options.dataDir || path.join(__dirname, '../../data/memory'),
      kyberAccelerators: options.kyberAccelerators || 16, // Accélérateurs KYBER
      fluidMode: options.fluidMode !== false, // Mode fluide par défaut
      continuousBackup: options.continuousBackup !== false // Sauvegarde continue
    };

    // 6 zones de mémoire thermique
    this.zones = {
      instant: { temperature: 100, entries: [], maxEntries: 100 },
      shortTerm: { temperature: 80, entries: [], maxEntries: 500 },
      working: { temperature: 60, entries: [], maxEntries: 1000 },
      mediumTerm: { temperature: 40, entries: [], maxEntries: 2000 },
      longTerm: { temperature: 20, entries: [], maxEntries: 5000 },
      creative: { temperature: 10, entries: [], maxEntries: 1000 }
    };

    this.stats = {
      totalEntries: 0,
      cyclesPerformed: 0,
      lastCycle: null,
      averageTemperature: 37.0,
      kyberOperations: 0,
      fluidSaves: 0,
      acceleratorUsage: 0,
      totalSaves: 0,
      totalLoads: 0,
      lastSave: null,
      lastLoad: null
    };

    this.isRunning = false;
    this.cycleInterval = null;

    // Accélérateurs KYBER pour sauvegarde fluide avec auto-scaling ILLIMITÉ
    this.kyberAccelerators = {
      active: Array(this.config.kyberAccelerators).fill(false),
      queue: [],
      processing: false,
      throughput: 0,
      lastAcceleration: null,
      maxAccelerators: Infinity, // AUCUNE LIMITE - Extension infinie
      autoScaling: true,
      demandHistory: [],
      lastScaleUp: null,
      lastScaleDown: null,
      scaleThreshold: 5, // Seuil pour déclencher l'auto-scaling
      persistentAccelerators: new Set(), // Accélérateurs qui restent branchés
      unlimitedMode: true // Mode illimité activé
    };

    // Buffer de sauvegarde fluide
    this.fluidBuffer = {
      pending: new Map(),
      lastFlush: Date.now(),
      operations: 0
    };

    // Créer le dossier de données
    this.ensureDataDir();

    // Charger les données existantes
    this.loadFromDisk();

    // Démarrer les cycles thermiques
    this.startThermalCycles();

    // Démarrer les accélérateurs KYBER
    this.startKyberAccelerators();

    // Démarrer la sauvegarde fluide
    this.startFluidBackup();

    console.log(`🔥 Mémoire thermique réelle initialisée avec 6 zones`);
    console.log(`⚡ KYBER: ${this.config.kyberAccelerators} accélérateurs de base`);
    console.log(`🔧 Auto-scaling: ${this.kyberAccelerators.autoScaling ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);
    console.log(`🚀 Limite max: AUCUNE LIMITE - Extension infinie`);
    console.log(`♾️  Mode illimité: ${this.kyberAccelerators.unlimitedMode ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);
    console.log(`💧 Mode fluide: ${this.config.fluidMode ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);
    console.log(`🔄 Sauvegarde continue: ${this.config.continuousBackup ? 'ACTIVÉE' : 'DÉSACTIVÉE'}`);
  }

  ensureDataDir() {
    if (!fs.existsSync(this.config.dataDir)) {
      fs.mkdirSync(this.config.dataDir, { recursive: true });
    }

    // Créer les sous-dossiers pour chaque zone
    Object.keys(this.zones).forEach(zone => {
      const zoneDir = path.join(this.config.dataDir, zone);
      if (!fs.existsSync(zoneDir)) {
        fs.mkdirSync(zoneDir, { recursive: true });
      }
    });
  }

  /**
   * Stocker une nouvelle entrée dans la mémoire thermique avec accélérateurs KYBER
   */
  store(data) {
    const entry = {
      id: `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: data.content || data,
      type: data.type || 'general',
      timestamp: new Date().toISOString(),
      temperature: data.temperature || 1.0,
      importance: data.importance || 0.5,
      accessCount: 0,
      lastAccess: new Date().toISOString()
    };

    // Déterminer la zone basée sur la température
    const zone = this.getZoneByTemperature(entry.temperature);

    // Ajouter à la zone appropriée
    this.zones[zone].entries.push(entry);
    this.stats.totalEntries++;

    // Maintenir la limite d'entrées par zone
    if (this.zones[zone].entries.length > this.zones[zone].maxEntries) {
      this.zones[zone].entries.shift(); // Supprimer la plus ancienne
    }

    // Sauvegarde fluide avec accélérateurs KYBER
    if (this.config.fluidMode) {
      this.addToFluidBuffer(entry, zone);
    }

    console.log(`📝 Mémoire stockée en zone ${zone}: ${entry.content.substring(0, 50)}...`);
    return entry.id;
  }

  /**
   * Récupérer des entrées de la mémoire
   */
  retrieve(query, options = {}) {
    const results = [];
    const limit = options.limit || 10;
    const minTemperature = options.minTemperature || 0;

    // Rechercher dans toutes les zones
    Object.entries(this.zones).forEach(([zoneName, zone]) => {
      zone.entries.forEach(entry => {
        if (entry.temperature >= minTemperature) {
          // Recherche simple par contenu
          if (typeof query === 'string' &&
              entry.content.toLowerCase().includes(query.toLowerCase())) {
            entry.accessCount++;
            entry.lastAccess = new Date().toISOString();
            entry.zone = zoneName;
            results.push(entry);
          }
        }
      });
    });

    // Trier par pertinence (température + importance + accès récent)
    results.sort((a, b) => {
      const scoreA = a.temperature * a.importance * (1 + a.accessCount * 0.1);
      const scoreB = b.temperature * b.importance * (1 + b.accessCount * 0.1);
      return scoreB - scoreA;
    });

    return results.slice(0, limit);
  }

  /**
   * Obtenir les statistiques de la mémoire
   */
  getStats() {
    const zoneStats = {};
    let totalTemp = 0;
    let totalEntries = 0;

    Object.entries(this.zones).forEach(([zoneName, zone]) => {
      zoneStats[zoneName] = {
        entries: zone.entries.length,
        temperature: zone.temperature,
        avgImportance: zone.entries.length > 0 ?
          zone.entries.reduce((sum, e) => sum + e.importance, 0) / zone.entries.length : 0
      };
      totalTemp += zone.temperature;
      totalEntries += zone.entries.length;
    });

    return {
      totalEntries,
      zones: zoneStats,
      averageTemperature: totalTemp / Object.keys(this.zones).length,
      cyclesPerformed: this.stats.cyclesPerformed,
      lastCycle: this.stats.lastCycle
    };
  }

  /**
   * Déterminer la zone basée sur la température
   */
  getZoneByTemperature(temperature) {
    if (temperature >= 0.9) return 'instant';
    if (temperature >= 0.7) return 'shortTerm';
    if (temperature >= 0.5) return 'working';
    if (temperature >= 0.3) return 'mediumTerm';
    if (temperature >= 0.1) return 'longTerm';
    return 'creative';
  }

  /**
   * Cycle thermique - refroidissement et transfert entre zones
   */
  performThermalCycle() {
    console.log('🌡️ Cycle thermique en cours...');

    Object.entries(this.zones).forEach(([zoneName, zone]) => {
      zone.entries.forEach(entry => {
        // Refroidissement naturel
        entry.temperature *= this.config.temperatureDecay;

        // Transfert vers une zone plus froide si nécessaire
        const newZone = this.getZoneByTemperature(entry.temperature);
        if (newZone !== zoneName && this.zones[newZone]) {
          // Transférer l'entrée
          const index = zone.entries.indexOf(entry);
          if (index > -1) {
            zone.entries.splice(index, 1);
            this.zones[newZone].entries.push(entry);
          }
        }
      });
    });

    this.stats.cyclesPerformed++;
    this.stats.lastCycle = new Date().toISOString();

    // Sauvegarder périodiquement
    if (this.stats.cyclesPerformed % 10 === 0) {
      this.saveToDisk();
    }
  }

  /**
   * Démarrer les cycles thermiques automatiques
   */
  startThermalCycles() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.cycleInterval = setInterval(() => {
      this.performThermalCycle();
    }, 10000); // Cycle toutes les 10 secondes

    console.log('🔄 Cycles thermiques démarrés');
  }

  /**
   * Arrêter les cycles thermiques
   */
  stopThermalCycles() {
    if (this.cycleInterval) {
      clearInterval(this.cycleInterval);
      this.cycleInterval = null;
    }
    this.isRunning = false;
    this.saveToDisk();
    console.log('🛑 Cycles thermiques arrêtés');
  }

  /**
   * Sauvegarder sur disque
   */
  saveToDisk() {
    try {
      const data = {
        zones: this.zones,
        stats: this.stats,
        timestamp: new Date().toISOString()
      };

      const filePath = path.join(this.config.dataDir, 'thermal_memory.json');
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      console.log('💾 Mémoire thermique sauvegardée');
    } catch (error) {
      console.error('❌ Erreur sauvegarde mémoire:', error.message);
    }
  }

  /**
   * Charger depuis le disque
   */
  loadFromDisk() {
    try {
      const filePath = path.join(this.config.dataDir, 'thermal_memory.json');
      if (fs.existsSync(filePath)) {
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        this.zones = data.zones || this.zones;
        this.stats = { ...this.stats, ...data.stats };
        console.log('📂 Mémoire thermique chargée depuis le disque');
      }
    } catch (error) {
      console.error('⚠️ Erreur chargement mémoire:', error.message);
    }
  }

  /**
   * Recherche avancée dans la mémoire
   */
  search(query, options = {}) {
    return this.retrieve(query, options);
  }

  /**
   * Ajouter une conversation
   */
  addConversation(conversation) {
    return this.store({
      content: `Q: ${conversation.messages[0]?.content} R: ${conversation.messages[1]?.content}`,
      type: 'conversation',
      temperature: 0.8,
      importance: 0.7
    });
  }

  /**
   * Obtenir les mémoires récentes pour le contexte
   */
  getRecentMemoriesForContext(count = 5) {
    const allMemories = [];

    Object.entries(this.zones).forEach(([zoneName, zone]) => {
      zone.entries.forEach(entry => {
        allMemories.push({ ...entry, zone: zoneName });
      });
    });

    return allMemories
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, count);
  }

  /**
   * Nettoyage et optimisation
   */
  cleanup() {
    Object.entries(this.zones).forEach(([zoneName, zone]) => {
      // Supprimer les entrées très froides et anciennes
      zone.entries = zone.entries.filter(entry => {
        const age = Date.now() - new Date(entry.timestamp).getTime();
        const maxAge = zoneName === 'longTerm' ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000;
        return entry.temperature > 0.01 || age < maxAge;
      });
    });

    console.log('🧹 Nettoyage de la mémoire thermique effectué');
  }

  /**
   * Démarrer les accélérateurs KYBER pour sauvegarde fluide
   */
  startKyberAccelerators() {
    console.log('⚡ Démarrage des accélérateurs KYBER...');

    // Activer les accélérateurs progressivement
    let activatedCount = 0;
    const activationInterval = setInterval(() => {
      if (activatedCount < this.config.kyberAccelerators) {
        this.kyberAccelerators.active[activatedCount] = true;
        activatedCount++;
        console.log(`⚡ Accélérateur KYBER ${activatedCount}/${this.config.kyberAccelerators} activé`);
      } else {
        clearInterval(activationInterval);
        console.log(`⚡ Tous les accélérateurs KYBER activés (${this.config.kyberAccelerators})`);
        this.startKyberProcessing();
      }
    }, 100); // Activation progressive toutes les 100ms
  }

  /**
   * Démarrer le traitement KYBER avec auto-scaling
   */
  startKyberProcessing() {
    setInterval(() => {
      if (this.kyberAccelerators.queue.length > 0 && !this.kyberAccelerators.processing) {
        this.processKyberQueue();
      }

      // Calculer le débit
      this.kyberAccelerators.throughput = this.stats.kyberOperations /
        ((Date.now() - (this.kyberAccelerators.lastAcceleration || Date.now())) / 1000);

      // Auto-scaling basé sur la demande
      this.evaluateAutoScaling();
    }, 50); // Traitement très rapide toutes les 50ms

    // Monitoring de la demande pour auto-scaling
    setInterval(() => {
      this.monitorDemand();
    }, 2000); // Évaluation toutes les 2 secondes
  }

  /**
   * Traiter la queue KYBER
   */
  async processKyberQueue() {
    if (this.kyberAccelerators.processing) return;

    this.kyberAccelerators.processing = true;
    const activeAccelerators = this.kyberAccelerators.active.filter(a => a).length;

    try {
      // Traiter plusieurs opérations en parallèle selon le nombre d'accélérateurs
      const batch = this.kyberAccelerators.queue.splice(0, activeAccelerators);

      await Promise.all(batch.map(async (operation) => {
        try {
          await this.executeKyberOperation(operation);
          this.stats.kyberOperations++;
          this.stats.acceleratorUsage++;
        } catch (error) {
          console.error('❌ Erreur accélérateur KYBER:', error.message);
        }
      }));

      this.kyberAccelerators.lastAcceleration = Date.now();
    } finally {
      this.kyberAccelerators.processing = false;
    }
  }

  /**
   * Exécuter une opération KYBER
   */
  async executeKyberOperation(operation) {
    switch (operation.type) {
      case 'save':
        await this.saveToDiskKyber(operation.data);
        break;
      case 'backup':
        await this.backupZoneKyber(operation.zone, operation.data);
        break;
      case 'compress':
        await this.compressDataKyber(operation.data);
        break;
      default:
        console.log(`⚡ Opération KYBER: ${operation.type}`);
    }
  }

  /**
   * Démarrer la sauvegarde fluide
   */
  startFluidBackup() {
    console.log('💧 Démarrage de la sauvegarde fluide...');

    // Sauvegarde ultra-rapide toutes les secondes
    setInterval(() => {
      if (this.fluidBuffer.pending.size > 0) {
        this.flushFluidBuffer();
      }
    }, this.config.saveInterval);

    // Sauvegarde de sécurité toutes les 5 secondes
    setInterval(() => {
      this.performSecurityBackup();
    }, 5000);
  }

  /**
   * Ajouter au buffer fluide
   */
  addToFluidBuffer(entry, zone) {
    const bufferKey = `${zone}_${Date.now()}`;
    this.fluidBuffer.pending.set(bufferKey, {
      entry,
      zone,
      timestamp: Date.now()
    });

    this.fluidBuffer.operations++;

    // Ajouter à la queue KYBER pour traitement accéléré
    this.kyberAccelerators.queue.push({
      type: 'backup',
      zone,
      data: entry,
      priority: entry.importance || 0.5
    });
  }

  /**
   * Vider le buffer fluide
   */
  async flushFluidBuffer() {
    if (this.fluidBuffer.pending.size === 0) return;

    const startTime = Date.now();
    const operations = Array.from(this.fluidBuffer.pending.values());

    try {
      // Grouper par zone pour optimiser
      const zoneGroups = {};
      operations.forEach(op => {
        if (!zoneGroups[op.zone]) zoneGroups[op.zone] = [];
        zoneGroups[op.zone].push(op.entry);
      });

      // Sauvegarder chaque zone avec accélérateurs KYBER
      await Promise.all(Object.entries(zoneGroups).map(async ([zone, entries]) => {
        this.kyberAccelerators.queue.push({
          type: 'save',
          data: { zone, entries },
          priority: 1.0
        });
      }));

      this.fluidBuffer.pending.clear();
      this.fluidBuffer.lastFlush = Date.now();
      this.stats.fluidSaves++;

      const duration = Date.now() - startTime;
      console.log(`💧 Buffer fluide vidé: ${operations.length} opérations en ${duration}ms`);

    } catch (error) {
      console.error('❌ Erreur vidage buffer fluide:', error.message);
    }
  }

  /**
   * Sauvegarde de sécurité
   */
  async performSecurityBackup() {
    try {
      // Sauvegarder les zones critiques avec priorité maximale
      ['instant', 'shortTerm'].forEach(zone => {
        if (this.zones[zone].entries.length > 0) {
          this.kyberAccelerators.queue.unshift({ // Priorité maximale
            type: 'backup',
            zone,
            data: this.zones[zone],
            priority: 2.0
          });
        }
      });
    } catch (error) {
      console.error('❌ Erreur sauvegarde de sécurité:', error.message);
    }
  }

  /**
   * Sauvegarde accélérée KYBER
   */
  async saveToDiskKyber(data) {
    const filename = `thermal_memory_kyber_${Date.now()}.json`;
    const filepath = path.join(this.config.dataDir, filename);

    const saveData = {
      timestamp: new Date().toISOString(),
      zones: data.zone ? { [data.zone]: data.entries } : this.zones,
      stats: this.stats,
      kyberAccelerated: true
    };

    await fs.promises.writeFile(filepath, JSON.stringify(saveData, null, 2));
    this.stats.totalSaves++;
    this.stats.lastSave = new Date().toISOString();
  }

  /**
   * Sauvegarde de zone accélérée
   */
  async backupZoneKyber(zone, data) {
    const filename = `zone_${zone}_${Date.now()}.json`;
    const filepath = path.join(this.config.dataDir, filename);

    await fs.promises.writeFile(filepath, JSON.stringify({
      zone,
      data,
      timestamp: new Date().toISOString(),
      kyberAccelerated: true
    }, null, 2));
  }

  /**
   * Compression de données KYBER
   */
  async compressDataKyber(data) {
    // Simulation de compression ultra-rapide
    console.log(`⚡ Compression KYBER: ${JSON.stringify(data).length} bytes`);
  }

  /**
   * Monitoring de la demande pour auto-scaling
   */
  monitorDemand() {
    const currentDemand = {
      queueSize: this.kyberAccelerators.queue.length,
      activeAccelerators: this.kyberAccelerators.active.filter(a => a).length,
      throughput: this.kyberAccelerators.throughput || 0,
      timestamp: Date.now()
    };

    // Garder un historique des 30 dernières mesures (1 minute)
    this.kyberAccelerators.demandHistory.push(currentDemand);
    if (this.kyberAccelerators.demandHistory.length > 30) {
      this.kyberAccelerators.demandHistory.shift();
    }

    // Log de monitoring
    if (currentDemand.queueSize > 0) {
      console.log(`📊 Demande KYBER: Queue=${currentDemand.queueSize}, Actifs=${currentDemand.activeAccelerators}, Débit=${currentDemand.throughput.toFixed(2)}`);
    }
  }

  /**
   * Évaluer le besoin d'auto-scaling
   */
  evaluateAutoScaling() {
    if (!this.kyberAccelerators.autoScaling) return;

    const now = Date.now();
    const queueSize = this.kyberAccelerators.queue.length;
    const activeCount = this.kyberAccelerators.active.filter(a => a).length;

    // Conditions pour scale up (ajouter des accélérateurs)
    if (this.shouldScaleUp(queueSize, activeCount, now)) {
      this.scaleUpAccelerators();
    }

    // Conditions pour scale down (réduire les accélérateurs non-persistants)
    else if (this.shouldScaleDown(queueSize, activeCount, now)) {
      this.scaleDownAccelerators();
    }
  }

  /**
   * Déterminer si on doit ajouter des accélérateurs (MODE ILLIMITÉ)
   */
  shouldScaleUp(queueSize, activeCount, now) {
    // Scale up si:
    // 1. Queue trop pleine par rapport aux accélérateurs actifs
    // 2. Pas de scale up récent (éviter l'oscillation)
    // 3. AUCUNE LIMITE MAXIMALE - Extension infinie

    const queueThreshold = activeCount * this.kyberAccelerators.scaleThreshold;
    const cooldownPeriod = 5000; // 5 secondes entre les scale ups
    const lastScaleUp = this.kyberAccelerators.lastScaleUp || 0;

    return queueSize > queueThreshold &&
           (now - lastScaleUp) > cooldownPeriod;
           // AUCUNE LIMITE - Peut s'étendre à l'infini
  }

  /**
   * Déterminer si on doit réduire les accélérateurs
   */
  shouldScaleDown(queueSize, activeCount, now) {
    // Scale down si:
    // 1. Queue vide depuis un moment
    // 2. Plus d'accélérateurs que le minimum
    // 3. Pas de scale down récent
    // 4. Ne pas toucher aux accélérateurs persistants

    const cooldownPeriod = 10000; // 10 secondes entre les scale downs
    const lastScaleDown = this.kyberAccelerators.lastScaleDown || 0;
    const minAccelerators = this.config.kyberAccelerators;

    // Vérifier l'historique de demande
    const recentHistory = this.kyberAccelerators.demandHistory.slice(-5);
    const avgQueueSize = recentHistory.reduce((sum, h) => sum + h.queueSize, 0) / recentHistory.length;

    return avgQueueSize < 1 &&
           activeCount > minAccelerators &&
           (now - lastScaleDown) > cooldownPeriod &&
           activeCount > this.kyberAccelerators.persistentAccelerators.size;
  }

  /**
   * Ajouter des accélérateurs automatiquement (MODE ILLIMITÉ)
   */
  scaleUpAccelerators() {
    const currentActive = this.kyberAccelerators.active.filter(a => a).length;

    // AUCUNE LIMITE - Peut s'étendre à l'infini
    console.log(`⚡ KYBER ILLIMITÉ: Extension en cours (actuellement ${currentActive})`);

    // Ajouter 2-8 accélérateurs selon la demande (plus agressif en mode illimité)
    const queueSize = this.kyberAccelerators.queue.length;
    const toAdd = Math.max(
      Math.ceil(queueSize / this.kyberAccelerators.scaleThreshold),
      2, // Minimum 2 à la fois
      Math.min(8, Math.ceil(currentActive * 0.25)) // Jusqu'à 25% d'augmentation ou 8 max
    );

    // Étendre le tableau si nécessaire (MODE ILLIMITÉ)
    while (this.kyberAccelerators.active.length < currentActive + toAdd) {
      this.kyberAccelerators.active.push(false);
    }

    // Activer les nouveaux accélérateurs
    let added = 0;
    for (let i = 0; i < this.kyberAccelerators.active.length && added < toAdd; i++) {
      if (!this.kyberAccelerators.active[i]) {
        this.kyberAccelerators.active[i] = true;
        this.kyberAccelerators.persistentAccelerators.add(i); // Les marquer comme persistants
        added++;
        console.log(`⚡ KYBER ILLIMITÉ Auto-Scale UP: Accélérateur ${i + 1} ajouté (${currentActive + added}/∞)`);
      }
    }

    this.kyberAccelerators.lastScaleUp = Date.now();
    this.stats.acceleratorUsage += added;

    console.log(`📈 KYBER ILLIMITÉ: ${added} accélérateurs ajoutés automatiquement (Total: ${currentActive + added}/∞)`);
  }

  /**
   * Réduire les accélérateurs non-persistants
   */
  scaleDownAccelerators() {
    const currentActive = this.kyberAccelerators.active.filter(a => a).length;
    const minAccelerators = this.config.kyberAccelerators;
    const persistentCount = this.kyberAccelerators.persistentAccelerators.size;

    if (currentActive <= minAccelerators) {
      return; // Ne pas descendre en dessous du minimum
    }

    // Réduire progressivement les accélérateurs non-persistants
    const toRemove = Math.min(2, currentActive - Math.max(minAccelerators, persistentCount));

    let removed = 0;
    for (let i = this.kyberAccelerators.active.length - 1; i >= 0 && removed < toRemove; i--) {
      if (this.kyberAccelerators.active[i] && !this.kyberAccelerators.persistentAccelerators.has(i)) {
        this.kyberAccelerators.active[i] = false;
        removed++;
        console.log(`⚡ KYBER Auto-Scale DOWN: Accélérateur ${i + 1} désactivé (${currentActive - removed})`);
      }
    }

    if (removed > 0) {
      this.kyberAccelerators.lastScaleDown = Date.now();
      console.log(`📉 KYBER: ${removed} accélérateurs désactivés (Total: ${currentActive - removed})`);
    }
  }

  /**
   * Forcer l'ajout d'accélérateurs persistants (MODE ILLIMITÉ)
   */
  addPersistentAccelerators(count = 1, reason = 'Agent request') {
    const currentActive = this.kyberAccelerators.active.filter(a => a).length;

    // AUCUNE LIMITE - Peut ajouter autant que nécessaire
    console.log(`⚡ KYBER ILLIMITÉ: Ajout de ${count} accélérateurs persistants (actuellement ${currentActive})`);

    const toAdd = count; // Pas de limite

    // Étendre le tableau si nécessaire
    while (this.kyberAccelerators.active.length < currentActive + toAdd) {
      this.kyberAccelerators.active.push(false);
    }

    // Activer les nouveaux accélérateurs persistants (MODE ILLIMITÉ)
    let added = 0;
    for (let i = 0; i < this.kyberAccelerators.active.length && added < toAdd; i++) {
      if (!this.kyberAccelerators.active[i]) {
        this.kyberAccelerators.active[i] = true;
        this.kyberAccelerators.persistentAccelerators.add(i);
        added++;
        console.log(`🔧 KYBER ILLIMITÉ PERSISTANT: Accélérateur ${i + 1} ajouté (${reason})`);
      }
    }

    this.stats.acceleratorUsage += added;
    console.log(`🔧 ${added} accélérateurs persistants ajoutés pour: ${reason} (Total: ${currentActive + added}/∞)`);

    return added > 0;
  }

  /**
   * Obtenir les informations d'auto-scaling
   */
  getAutoScalingInfo() {
    const activeCount = this.kyberAccelerators.active.filter(a => a).length;
    const persistentCount = this.kyberAccelerators.persistentAccelerators.size;

    return {
      autoScaling: this.kyberAccelerators.autoScaling,
      activeAccelerators: activeCount,
      maxAccelerators: this.kyberAccelerators.maxAccelerators,
      persistentAccelerators: persistentCount,
      queueSize: this.kyberAccelerators.queue.length,
      demandHistory: this.kyberAccelerators.demandHistory.slice(-10), // 10 dernières mesures
      lastScaleUp: this.kyberAccelerators.lastScaleUp,
      lastScaleDown: this.kyberAccelerators.lastScaleDown
    };
  }

  /**
   * Obtenir les statistiques de la mémoire thermique avec accélérateurs
   */
  getStats() {
    const totalEntries = Object.values(this.zones).reduce((sum, zone) => sum + zone.entries.length, 0);
    const avgTemp = Object.values(this.zones).reduce((sum, zone) => sum + zone.temperature, 0) / 6;

    return {
      ...this.stats,
      totalEntries,
      averageTemperature: Math.round(avgTemp * 10) / 10,
      kyberAccelerators: {
        active: this.kyberAccelerators.active.filter(a => a).length,
        total: this.config.kyberAccelerators,
        throughput: this.kyberAccelerators.throughput,
        operations: this.stats.kyberOperations,
        queueSize: this.kyberAccelerators.queue.length
      },
      fluidBuffer: {
        pending: this.fluidBuffer.pending.size,
        operations: this.fluidBuffer.operations,
        lastFlush: this.fluidBuffer.lastFlush,
        fluidSaves: this.stats.fluidSaves
      },
      zones: Object.keys(this.zones).map(name => ({
        name,
        temperature: this.zones[name].temperature,
        entries: this.zones[name].entries.length,
        maxEntries: this.zones[name].maxEntries
      }))
    };
  }
}

module.exports = ThermalMemory;
