/**
 * Module d'analyse de livres et de traitement de texte
 * 
 * Ce module fournit des fonctionnalités pour:
 * - Extraire du texte à partir de différents formats de livres (PDF, EPUB, TXT)
 * - Analyser le contenu des livres
 * - Générer des résumés et des insights
 * - Appliquer des techniques de MPC (Model Predictive Control) pour l'analyse prédictive
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const pdf = require('pdf-parse');
const EPub = require('epub');
const natural = require('natural');
const { v4: uuidv4 } = require('uuid');

// Promisify des fonctions de fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Dossier pour stocker les livres et les analyses
const BOOKS_DIR = path.join(__dirname, '../data/books');
const ANALYSIS_DIR = path.join(__dirname, '../data/analysis');

// Initialiser les dossiers nécessaires
async function initDirectories() {
  try {
    if (!await existsAsync(BOOKS_DIR)) {
      await mkdirAsync(BOOKS_DIR, { recursive: true });
    }
    if (!await existsAsync(ANALYSIS_DIR)) {
      await mkdirAsync(ANALYSIS_DIR, { recursive: true });
    }
  } catch (error) {
    console.error('Error initializing directories:', error);
    throw error;
  }
}

/**
 * Extraire le texte d'un fichier PDF
 * @param {string} filePath - Chemin vers le fichier PDF
 * @returns {Promise<string>} - Texte extrait du PDF
 */
async function extractTextFromPDF(filePath) {
  try {
    const dataBuffer = await readFileAsync(filePath);
    const data = await pdf(dataBuffer);
    return data.text;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw error;
  }
}

/**
 * Extraire le texte d'un fichier EPUB
 * @param {string} filePath - Chemin vers le fichier EPUB
 * @returns {Promise<string>} - Texte extrait de l'EPUB
 */
async function extractTextFromEPUB(filePath) {
  return new Promise((resolve, reject) => {
    const epub = new EPub(filePath);
    
    epub.on('error', reject);
    
    epub.on('end', () => {
      let text = '';
      
      // Obtenir le nombre de chapitres
      epub.flow.forEach((chapter, index) => {
        epub.getChapter(chapter.id, (err, chapterText) => {
          if (err) {
            reject(err);
            return;
          }
          
          // Extraire le texte du HTML
          const textOnly = chapterText.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
          text += textOnly + '\n\n';
          
          // Si c'est le dernier chapitre, résoudre la promesse
          if (index === epub.flow.length - 1) {
            resolve(text);
          }
        });
      });
    });
    
    epub.parse();
  });
}

/**
 * Extraire le texte d'un fichier TXT
 * @param {string} filePath - Chemin vers le fichier TXT
 * @returns {Promise<string>} - Texte extrait du TXT
 */
async function extractTextFromTXT(filePath) {
  try {
    const text = await readFileAsync(filePath, 'utf8');
    return text;
  } catch (error) {
    console.error('Error extracting text from TXT:', error);
    throw error;
  }
}

/**
 * Extraire le texte d'un livre, quel que soit son format
 * @param {string} filePath - Chemin vers le fichier
 * @returns {Promise<string>} - Texte extrait du livre
 */
async function extractTextFromBook(filePath) {
  const extension = path.extname(filePath).toLowerCase();
  
  switch (extension) {
    case '.pdf':
      return extractTextFromPDF(filePath);
    case '.epub':
      return extractTextFromEPUB(filePath);
    case '.txt':
      return extractTextFromTXT(filePath);
    default:
      throw new Error(`Unsupported file format: ${extension}`);
  }
}

/**
 * Analyser le texte d'un livre pour en extraire des informations
 * @param {string} text - Texte du livre
 * @returns {Object} - Résultats de l'analyse
 */
function analyzeText(text) {
  // Tokenization
  const tokenizer = new natural.WordTokenizer();
  const tokens = tokenizer.tokenize(text);
  
  // Calcul de statistiques de base
  const wordCount = tokens.length;
  const characterCount = text.length;
  const sentenceCount = text.split(/[.!?]+/).length;
  const paragraphCount = text.split(/\n\s*\n/).length;
  
  // Analyse des fréquences de mots
  const wordFrequency = {};
  tokens.forEach(token => {
    const word = token.toLowerCase();
    wordFrequency[word] = (wordFrequency[word] || 0) + 1;
  });
  
  // Trier les mots par fréquence
  const sortedWords = Object.entries(wordFrequency)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 100);
  
  // Analyse des sentiments
  const analyzer = new natural.SentimentAnalyzer('English', natural.PorterStemmer, 'afinn');
  const sentimentScore = analyzer.getSentiment(tokens);
  
  return {
    statistics: {
      wordCount,
      characterCount,
      sentenceCount,
      paragraphCount,
      averageWordLength: characterCount / wordCount,
      averageWordsPerSentence: wordCount / sentenceCount,
      averageSentencesPerParagraph: sentenceCount / paragraphCount
    },
    topWords: sortedWords,
    sentiment: {
      score: sentimentScore,
      interpretation: interpretSentiment(sentimentScore)
    }
  };
}

/**
 * Interpréter le score de sentiment
 * @param {number} score - Score de sentiment
 * @returns {string} - Interprétation du sentiment
 */
function interpretSentiment(score) {
  if (score > 0.5) return 'Très positif';
  if (score > 0.1) return 'Positif';
  if (score > -0.1) return 'Neutre';
  if (score > -0.5) return 'Négatif';
  return 'Très négatif';
}

/**
 * Générer un résumé du texte en utilisant l'algorithme TextRank
 * @param {string} text - Texte à résumer
 * @param {number} sentenceCount - Nombre de phrases à inclure dans le résumé
 * @returns {string} - Résumé du texte
 */
function generateSummary(text, sentenceCount = 5) {
  // Diviser le texte en phrases
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  if (sentences.length <= sentenceCount) {
    return text;
  }
  
  // Calculer la similarité entre les phrases
  const similarityMatrix = [];
  for (let i = 0; i < sentences.length; i++) {
    similarityMatrix[i] = [];
    for (let j = 0; j < sentences.length; j++) {
      if (i === j) {
        similarityMatrix[i][j] = 0;
      } else {
        similarityMatrix[i][j] = calculateSimilarity(sentences[i], sentences[j]);
      }
    }
  }
  
  // Calculer les scores TextRank
  const scores = calculateTextRankScores(similarityMatrix, 0.85, 100);
  
  // Sélectionner les phrases les plus importantes
  const rankedSentences = scores
    .map((score, index) => ({ index, score, text: sentences[index] }))
    .sort((a, b) => b.score - a.score)
    .slice(0, sentenceCount);
  
  // Trier les phrases par ordre d'apparition dans le texte original
  rankedSentences.sort((a, b) => a.index - b.index);
  
  // Joindre les phrases pour former le résumé
  return rankedSentences.map(s => s.text).join('. ') + '.';
}

/**
 * Calculer la similarité entre deux phrases
 * @param {string} sentence1 - Première phrase
 * @param {string} sentence2 - Deuxième phrase
 * @returns {number} - Score de similarité
 */
function calculateSimilarity(sentence1, sentence2) {
  const tokenizer = new natural.WordTokenizer();
  const tokens1 = tokenizer.tokenize(sentence1.toLowerCase());
  const tokens2 = tokenizer.tokenize(sentence2.toLowerCase());
  
  // Créer des ensembles de mots uniques
  const set1 = new Set(tokens1);
  const set2 = new Set(tokens2);
  
  // Calculer l'intersection
  const intersection = new Set([...set1].filter(x => set2.has(x)));
  
  // Calculer la similarité de Jaccard
  const union = new Set([...set1, ...set2]);
  return intersection.size / union.size;
}

/**
 * Calculer les scores TextRank
 * @param {Array<Array<number>>} similarityMatrix - Matrice de similarité
 * @param {number} dampingFactor - Facteur d'amortissement
 * @param {number} iterations - Nombre d'itérations
 * @returns {Array<number>} - Scores TextRank
 */
function calculateTextRankScores(similarityMatrix, dampingFactor, iterations) {
  const size = similarityMatrix.length;
  let scores = new Array(size).fill(1 / size);
  
  for (let iter = 0; iter < iterations; iter++) {
    const newScores = new Array(size).fill(0);
    
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        if (i !== j && similarityMatrix[j][i] > 0) {
          // Calculer la somme des poids sortants de j
          let weightSum = 0;
          for (let k = 0; k < size; k++) {
            weightSum += similarityMatrix[j][k];
          }
          
          if (weightSum > 0) {
            newScores[i] += (similarityMatrix[j][i] / weightSum) * scores[j];
          }
        }
      }
      
      newScores[i] = (1 - dampingFactor) + dampingFactor * newScores[i];
    }
    
    scores = newScores;
  }
  
  return scores;
}

/**
 * Appliquer des techniques de MPC (Model Predictive Control) pour l'analyse prédictive
 * @param {string} text - Texte à analyser
 * @returns {Object} - Résultats de l'analyse MPC
 */
function applyMPC(text) {
  // Tokenization et préparation des données
  const tokenizer = new natural.WordTokenizer();
  const tokens = tokenizer.tokenize(text.toLowerCase());
  
  // Créer un modèle de Markov pour prédire les séquences de mots
  const markovChain = {};
  const order = 2; // Ordre du modèle de Markov
  
  for (let i = 0; i < tokens.length - order; i++) {
    const key = tokens.slice(i, i + order).join(' ');
    const nextWord = tokens[i + order];
    
    if (!markovChain[key]) {
      markovChain[key] = {};
    }
    
    markovChain[key][nextWord] = (markovChain[key][nextWord] || 0) + 1;
  }
  
  // Calculer les probabilités de transition
  Object.keys(markovChain).forEach(key => {
    const transitions = markovChain[key];
    const total = Object.values(transitions).reduce((sum, count) => sum + count, 0);
    
    Object.keys(transitions).forEach(word => {
      transitions[word] = transitions[word] / total;
    });
  });
  
  // Identifier les motifs récurrents
  const patterns = findRecurringPatterns(tokens, 3, 5);
  
  // Prédire les thèmes émergents
  const themes = predictThemes(tokens, markovChain);
  
  return {
    markovModel: markovChain,
    recurringPatterns: patterns,
    predictedThemes: themes
  };
}

/**
 * Trouver des motifs récurrents dans le texte
 * @param {Array<string>} tokens - Tokens du texte
 * @param {number} minLength - Longueur minimale des motifs
 * @param {number} maxLength - Longueur maximale des motifs
 * @returns {Array<Object>} - Motifs récurrents
 */
function findRecurringPatterns(tokens, minLength, maxLength) {
  const patterns = [];
  
  for (let length = minLength; length <= maxLength; length++) {
    const patternCounts = {};
    
    for (let i = 0; i <= tokens.length - length; i++) {
      const pattern = tokens.slice(i, i + length).join(' ');
      patternCounts[pattern] = (patternCounts[pattern] || 0) + 1;
    }
    
    // Filtrer les motifs qui apparaissent au moins 3 fois
    Object.entries(patternCounts)
      .filter(([_, count]) => count >= 3)
      .forEach(([pattern, count]) => {
        patterns.push({ pattern, length, count });
      });
  }
  
  // Trier par nombre d'occurrences
  return patterns.sort((a, b) => b.count - a.count).slice(0, 10);
}

/**
 * Prédire les thèmes émergents du texte
 * @param {Array<string>} tokens - Tokens du texte
 * @param {Object} markovModel - Modèle de Markov
 * @returns {Array<Object>} - Thèmes prédits
 */
function predictThemes(tokens, markovModel) {
  // Utiliser TF-IDF pour identifier les mots importants
  const tfidf = new natural.TfIdf();
  
  // Diviser le texte en sections
  const sectionSize = Math.ceil(tokens.length / 10);
  for (let i = 0; i < tokens.length; i += sectionSize) {
    const section = tokens.slice(i, i + sectionSize).join(' ');
    tfidf.addDocument(section);
  }
  
  // Identifier les mots clés de chaque section
  const sectionKeywords = [];
  for (let i = 0; i < tfidf.documents.length; i++) {
    const keywords = [];
    tfidf.listTerms(i).slice(0, 5).forEach(item => {
      keywords.push({ term: item.term, tfidf: item.tfidf });
    });
    sectionKeywords.push(keywords);
  }
  
  // Regrouper les mots clés similaires pour identifier les thèmes
  const themes = [];
  const processedTerms = new Set();
  
  sectionKeywords.forEach(section => {
    section.forEach(keyword => {
      if (!processedTerms.has(keyword.term)) {
        processedTerms.add(keyword.term);
        
        // Trouver les termes co-occurrents
        const cooccurringTerms = findCooccurringTerms(keyword.term, tokens, 5);
        
        if (cooccurringTerms.length > 0) {
          themes.push({
            mainTerm: keyword.term,
            relatedTerms: cooccurringTerms,
            strength: keyword.tfidf
          });
        }
      }
    });
  });
  
  // Trier par force du thème
  return themes.sort((a, b) => b.strength - a.strength).slice(0, 5);
}

/**
 * Trouver les termes qui co-occurrent avec un terme donné
 * @param {string} term - Terme à analyser
 * @param {Array<string>} tokens - Tokens du texte
 * @param {number} windowSize - Taille de la fenêtre de contexte
 * @returns {Array<Object>} - Termes co-occurrents
 */
function findCooccurringTerms(term, tokens, windowSize) {
  const cooccurrences = {};
  
  for (let i = 0; i < tokens.length; i++) {
    if (tokens[i] === term) {
      // Examiner les mots dans la fenêtre de contexte
      const start = Math.max(0, i - windowSize);
      const end = Math.min(tokens.length, i + windowSize + 1);
      
      for (let j = start; j < end; j++) {
        if (j !== i && tokens[j].length > 3) {
          cooccurrences[tokens[j]] = (cooccurrences[tokens[j]] || 0) + 1;
        }
      }
    }
  }
  
  // Convertir en tableau et trier
  return Object.entries(cooccurrences)
    .map(([term, count]) => ({ term, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

/**
 * Sauvegarder l'analyse d'un livre
 * @param {string} bookId - ID du livre
 * @param {Object} analysis - Résultats de l'analyse
 * @returns {Promise<void>}
 */
async function saveAnalysis(bookId, analysis) {
  try {
    const filePath = path.join(ANALYSIS_DIR, `${bookId}.json`);
    await writeFileAsync(filePath, JSON.stringify(analysis, null, 2));
  } catch (error) {
    console.error('Error saving analysis:', error);
    throw error;
  }
}

/**
 * Charger l'analyse d'un livre
 * @param {string} bookId - ID du livre
 * @returns {Promise<Object>} - Résultats de l'analyse
 */
async function loadAnalysis(bookId) {
  try {
    const filePath = path.join(ANALYSIS_DIR, `${bookId}.json`);
    const data = await readFileAsync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading analysis:', error);
    throw error;
  }
}

/**
 * Analyser un livre et sauvegarder les résultats
 * @param {string} filePath - Chemin vers le fichier du livre
 * @returns {Promise<Object>} - Résultats de l'analyse
 */
async function analyzeBook(filePath) {
  try {
    await initDirectories();
    
    // Extraire le texte du livre
    const text = await extractTextFromBook(filePath);
    
    // Générer un ID unique pour le livre
    const bookId = uuidv4();
    
    // Analyser le texte
    const textAnalysis = analyzeText(text);
    
    // Générer un résumé
    const summary = generateSummary(text, 10);
    
    // Appliquer MPC
    const mpcResults = applyMPC(text);
    
    // Combiner les résultats
    const analysis = {
      bookId,
      fileName: path.basename(filePath),
      fileSize: fs.statSync(filePath).size,
      fileType: path.extname(filePath).substring(1),
      analysisDate: new Date().toISOString(),
      textAnalysis,
      summary,
      mpcResults
    };
    
    // Sauvegarder l'analyse
    await saveAnalysis(bookId, analysis);
    
    return analysis;
  } catch (error) {
    console.error('Error analyzing book:', error);
    throw error;
  }
}

module.exports = {
  analyzeBook,
  loadAnalysis,
  extractTextFromBook,
  generateSummary,
  applyMPC
};
