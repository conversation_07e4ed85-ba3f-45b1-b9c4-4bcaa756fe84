{"version": 3, "file": "resolve.js", "sources": ["../../../src/js/resolve.ts"], "sourcesContent": ["/**\n * resolve\n */\n\nimport {\n  <PERSON>acheItem,\n  NullObject,\n  createC<PERSON><PERSON><PERSON>,\n  getCache,\n  setCache\n} from './cache';\nimport {\n  convertRgbToHex,\n  resolveColorFunc,\n  resolveColorMix,\n  resolveColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { resolveVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport {\n  ComputedColorChannels,\n  Options,\n  SpecifiedColorChannels\n} from './typedef';\n\n/* constants */\nimport {\n  FN_COLOR,\n  FN_MIX,\n  SYN_FN_CALC,\n  SYN_FN_REL,\n  SYN_FN_VAR,\n  VAL_COMP,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'resolve';\nconst RGB_TRANSPARENT = 'rgba(0, 0, 0, 0)';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve color\n * @param value - CSS color value\n * @param [opt] - options\n * @returns resolved color\n */\nexport const resolveColor = (\n  value: string,\n  opt: Options = {}\n): string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { currentColor = '', format = VAL_COMP, nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolve',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (REG_FN_VAR.test(value)) {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    const resolvedValue = resolveVar(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      switch (format) {\n        case 'hex':\n        case 'hexAlpha': {\n          setCache(cacheKey, resolvedValue);\n          return resolvedValue;\n        }\n        default: {\n          if (nullable) {\n            setCache(cacheKey, resolvedValue);\n            return resolvedValue;\n          }\n          const res = RGB_TRANSPARENT;\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (opt.format !== format) {\n    opt.format = format;\n  }\n  value = value.toLowerCase();\n  if (REG_FN_REL.test(value)) {\n    const resolvedValue = resolveRelativeColor(value, opt);\n    if (format === VAL_COMP) {\n      let res;\n      if (resolvedValue instanceof NullObject) {\n        if (nullable) {\n          res = resolvedValue;\n        } else {\n          res = RGB_TRANSPARENT;\n        }\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (format === VAL_SPEC) {\n      let res = '';\n      if (resolvedValue instanceof NullObject) {\n        res = '';\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (resolvedValue instanceof NullObject) {\n      value = '';\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (REG_FN_CALC.test(value)) {\n    value = cssCalc(value, opt);\n  }\n  let cs = '';\n  let r = NaN;\n  let g = NaN;\n  let b = NaN;\n  let alpha = NaN;\n  if (value === 'transparent') {\n    switch (format) {\n      case VAL_SPEC: {\n        setCache(cacheKey, value);\n        return value;\n      }\n      case 'hex': {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      case 'hexAlpha': {\n        const res = '#00000000';\n        setCache(cacheKey, res);\n        return res;\n      }\n      case VAL_COMP:\n      default: {\n        const res = RGB_TRANSPARENT;\n        setCache(cacheKey, res);\n        return res;\n      }\n    }\n  } else if (value === 'currentcolor') {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    if (currentColor) {\n      let resolvedValue;\n      if (currentColor.startsWith(FN_MIX)) {\n        resolvedValue = resolveColorMix(currentColor, opt);\n      } else if (currentColor.startsWith(FN_COLOR)) {\n        resolvedValue = resolveColorFunc(currentColor, opt);\n      } else {\n        resolvedValue = resolveColorValue(currentColor, opt);\n      }\n      if (resolvedValue instanceof NullObject) {\n        setCache(cacheKey, resolvedValue);\n        return resolvedValue;\n      }\n      [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n    } else if (format === VAL_COMP) {\n      const res = RGB_TRANSPARENT;\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (format === VAL_SPEC) {\n    if (value.startsWith(FN_MIX)) {\n      const res = resolveColorMix(value, opt) as string;\n      setCache(cacheKey, res);\n      return res;\n    } else if (value.startsWith(FN_COLOR)) {\n      const [scs, rr, gg, bb, aa] = resolveColorFunc(\n        value,\n        opt\n      ) as SpecifiedColorChannels;\n      let res = '';\n      if (aa === 1) {\n        res = `color(${scs} ${rr} ${gg} ${bb})`;\n      } else {\n        res = `color(${scs} ${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    } else {\n      const rgb = resolveColorValue(value, opt);\n      if (isString(rgb)) {\n        setCache(cacheKey, rgb);\n        return rgb;\n      }\n      const [scs, rr, gg, bb, aa] = rgb as SpecifiedColorChannels;\n      let res = '';\n      if (scs === 'rgb') {\n        if (aa === 1) {\n          res = `${scs}(${rr}, ${gg}, ${bb})`;\n        } else {\n          res = `${scs}a(${rr}, ${gg}, ${bb}, ${aa})`;\n        }\n      } else if (aa === 1) {\n        res = `${scs}(${rr} ${gg} ${bb})`;\n      } else {\n        res = `${scs}(${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (value.startsWith(FN_MIX)) {\n    if (/currentcolor/.test(value)) {\n      if (currentColor) {\n        value = value.replace(/currentcolor/g, currentColor);\n      }\n    }\n    if (/transparent/.test(value)) {\n      value = value.replace(/transparent/g, RGB_TRANSPARENT);\n    }\n    const resolvedValue = resolveColorMix(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const resolvedValue = resolveColorFunc(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value) {\n    const resolvedValue = resolveColorValue(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  }\n  let res = '';\n  switch (format) {\n    case 'hex': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha) ||\n        alpha === 0\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, 1]);\n      break;\n    }\n    case 'hexAlpha': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha)\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, alpha]);\n      break;\n    }\n    case VAL_COMP:\n    default: {\n      switch (cs) {\n        case 'rgb': {\n          if (alpha === 1) {\n            res = `${cs}(${r}, ${g}, ${b})`;\n          } else {\n            res = `${cs}a(${r}, ${g}, ${b}, ${alpha})`;\n          }\n          break;\n        }\n        case 'lab':\n        case 'lch':\n        case 'oklab':\n        case 'oklch': {\n          if (alpha === 1) {\n            res = `${cs}(${r} ${g} ${b})`;\n          } else {\n            res = `${cs}(${r} ${g} ${b} / ${alpha})`;\n          }\n          break;\n        }\n        // color()\n        default: {\n          if (alpha === 1) {\n            res = `color(${cs} ${r} ${g} ${b})`;\n          } else {\n            res = `color(${cs} ${r} ${g} ${b} / ${alpha})`;\n          }\n        }\n      }\n    }\n  }\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve CSS color\n * @param value\n *   - CSS color value\n *   - system colors are not supported\n * @param [opt] - options\n * @param [opt.currentColor]\n *   - color to use for `currentcolor` keyword\n *   - if omitted, it will be treated as a missing color\n *     i.e. `rgb(none none none / none)`\n * @param [opt.customProperty]\n *   - custom properties\n *   - pair of `--` prefixed property name and value,\n *     e.g. `customProperty: { '--some-color': '#0000ff' }`\n *   - and/or `callback` function to get the value of the custom property,\n *     e.g. `customProperty: { callback: someDeclaration.getPropertyValue }`\n * @param [opt.dimension]\n *   - dimension, convert relative length to pixels\n *   - pair of unit and it's value as a number in pixels,\n *     e.g. `dimension: { em: 12, rem: 16, vw: 10.26 }`\n *   - and/or `callback` function to get the value as a number in pixels,\n *     e.g. `dimension: { callback: convertUnitToPixel }`\n * @param [opt.format]\n *   - output format, one of below\n *   - `computedValue` (default), [computed value][139] of the color\n *   - `specifiedValue`, [specified value][140] of the color\n *   - `hex`, hex color notation, i.e. `rrggbb`\n *   - `hexAlpha`, hex color notation with alpha channel, i.e. `#rrggbbaa`\n * @returns\n *   - one of rgba?(), #rrggbb(aa)?, color-name, '(empty-string)',\n *     color(color-space r g b / alpha), color(color-space x y z / alpha),\n *     lab(l a b / alpha), lch(l c h / alpha), oklab(l a b / alpha),\n *     oklch(l c h / alpha), null\n *   - in `computedValue`, values are numbers, however `rgb()` values are\n *     integers\n *   - in `specifiedValue`, returns `empty string` for unknown and/or invalid\n *     color\n *   - in `hex`, returns `null` for `transparent`, and also returns `null` if\n *     any of `r`, `g`, `b`, `alpha` is not a number\n *   - in `hexAlpha`, returns `#00000000` for `transparent`,\n *     however returns `null` if any of `r`, `g`, `b`, `alpha` is not a number\n */\nexport const resolve = (value: string, opt: Options = {}): string | null => {\n  opt.nullable = false;\n  const resolvedValue = resolveColor(value, opt);\n  if (resolvedValue instanceof NullObject) {\n    return null;\n  }\n  return resolvedValue as string;\n};\n"], "names": ["res"], "mappings": ";;;;;;;AAqCA,MAAM,YAAY;AAClB,MAAM,kBAAkB;AAGxB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,MAAM,eAAe,CAC1B,OACA,MAAe,OACS;AACpB,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,eAAe,IAAI,SAAS,UAAU,WAAW,UAAU;AACnE,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,WAAO,aAAa;AAAA,EAAA;AAElB,MAAA,WAAW,KAAK,KAAK,GAAG;AAC1B,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACjB,aAAA;AAAA,IAAA;AAEH,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,cAAQ,QAAQ;AAAA,QACd,KAAK;AAAA,QACL,KAAK,YAAY;AACf,mBAAS,UAAU,aAAa;AACzB,iBAAA;AAAA,QAAA;AAAA,QAET,SAAS;AACP,cAAI,UAAU;AACZ,qBAAS,UAAU,aAAa;AACzB,mBAAA;AAAA,UAAA;AAET,gBAAMA,OAAM;AACZ,mBAAS,UAAUA,IAAG;AACfA,iBAAAA;AAAAA,QAAA;AAAA,MACT;AAAA,IACF,OACK;AACG,cAAA;AAAA,IAAA;AAAA,EACV;AAEE,MAAA,IAAI,WAAW,QAAQ;AACzB,QAAI,SAAS;AAAA,EAAA;AAEf,UAAQ,MAAM,YAAY;AACtB,MAAA,WAAW,KAAK,KAAK,GAAG;AACpB,UAAA,gBAAgB,qBAAqB,OAAO,GAAG;AACrD,QAAI,WAAW,UAAU;AACnBA,UAAAA;AACJ,UAAI,yBAAyB,YAAY;AACvC,YAAI,UAAU;AACZA,iBAAM;AAAA,QAAA,OACD;AACLA,iBAAM;AAAA,QAAA;AAAA,MACR,OACK;AACLA,eAAM;AAAA,MAAA;AAER,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAET,QAAI,WAAW,UAAU;AACvB,UAAIA,OAAM;AACV,UAAI,yBAAyB,YAAY;AACvCA,eAAM;AAAA,MAAA,OACD;AACLA,eAAM;AAAA,MAAA;AAER,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAET,QAAI,yBAAyB,YAAY;AAC/B,cAAA;AAAA,IAAA,OACH;AACG,cAAA;AAAA,IAAA;AAAA,EACV;AAEE,MAAA,YAAY,KAAK,KAAK,GAAG;AACnB,YAAA,QAAQ,OAAO,GAAG;AAAA,EAAA;AAE5B,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,UAAU,eAAe;AAC3B,YAAQ,QAAQ;AAAA,MACd,KAAK,UAAU;AACb,iBAAS,UAAU,KAAK;AACjB,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,OAAO;AACV,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MAAA;AAAA,MAExB,KAAK,YAAY;AACf,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACfA,eAAAA;AAAAA,MAAA;AAAA,MAET,KAAK;AAAA,MACL,SAAS;AACP,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACfA,eAAAA;AAAAA,MAAA;AAAA,IACT;AAAA,EACF,WACS,UAAU,gBAAgB;AACnC,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACjB,aAAA;AAAA,IAAA;AAET,QAAI,cAAc;AACZ,UAAA;AACA,UAAA,aAAa,WAAW,MAAM,GAAG;AACnB,wBAAA,gBAAgB,cAAc,GAAG;AAAA,MACxC,WAAA,aAAa,WAAW,QAAQ,GAAG;AAC5B,wBAAA,iBAAiB,cAAc,GAAG;AAAA,MAAA,OAC7C;AACW,wBAAA,kBAAkB,cAAc,GAAG;AAAA,MAAA;AAErD,UAAI,yBAAyB,YAAY;AACvC,iBAAS,UAAU,aAAa;AACzB,eAAA;AAAA,MAAA;AAET,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,IAAA,WACd,WAAW,UAAU;AAC9B,YAAMA,OAAM;AACZ,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAAA,EACT,WACS,WAAW,UAAU;AAC1B,QAAA,MAAM,WAAW,MAAM,GAAG;AACtBA,YAAAA,OAAM,gBAAgB,OAAO,GAAG;AACtC,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IACE,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,MACF;AACA,UAAIA,OAAM;AACV,UAAI,OAAO,GAAG;AACZA,eAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAAA,OAC/B;AACLA,eAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA;AAE9C,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA,OACF;AACC,YAAA,MAAM,kBAAkB,OAAO,GAAG;AACpC,UAAA,SAAS,GAAG,GAAG;AACjB,iBAAS,UAAU,GAAG;AACf,eAAA;AAAA,MAAA;AAET,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAC9B,UAAIA,OAAM;AACV,UAAI,QAAQ,OAAO;AACjB,YAAI,OAAO,GAAG;AACZA,iBAAM,GAAG,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA,OAC3B;AACLA,iBAAM,GAAG,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA;AAAA,MAC1C,WACS,OAAO,GAAG;AACnBA,eAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAAA,OACzB;AACLA,eAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA;AAExC,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAAA,EAEA,WAAA,MAAM,WAAW,MAAM,GAAG;AAC/B,QAAA,eAAe,KAAK,KAAK,GAAG;AAC9B,UAAI,cAAc;AACR,gBAAA,MAAM,QAAQ,iBAAiB,YAAY;AAAA,MAAA;AAAA,IACrD;AAEE,QAAA,cAAc,KAAK,KAAK,GAAG;AACrB,cAAA,MAAM,QAAQ,gBAAgB,eAAe;AAAA,IAAA;AAEjD,UAAA,gBAAgB,gBAAgB,OAAO,GAAG;AAChD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AACzB,aAAA;AAAA,IAAA;AAET,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACd,WAAA,MAAM,WAAW,QAAQ,GAAG;AAC/B,UAAA,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AACzB,aAAA;AAAA,IAAA;AAET,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,aACd,OAAO;AACV,UAAA,gBAAgB,kBAAkB,OAAO,GAAG;AAClD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AACzB,aAAA;AAAA,IAAA;AAET,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA;AAEzB,MAAI,MAAM;AACV,UAAQ,QAAQ;AAAA,IACd,KAAK,OAAO;AACV,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,KAClB,UAAU,GACV;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MAAA;AAExB,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAClC;AAAA,IAAA;AAAA,IAEF,KAAK,YAAY;AACf,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,GAClB;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MAAA;AAExB,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtC;AAAA,IAAA;AAAA,IAEF,KAAK;AAAA,IACL,SAAS;AACP,cAAQ,IAAI;AAAA,QACV,KAAK,OAAO;AACV,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAAA,UAAA,OACvB;AACC,kBAAA,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK;AAAA,UAAA;AAEzC;AAAA,QAAA;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,SAAS;AACZ,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAAA,OACrB;AACC,kBAAA,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UAAA;AAEvC;AAAA,QAAA;AAAA;AAAA,QAGF,SAAS;AACP,cAAI,UAAU,GAAG;AACf,kBAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAAA,OAC3B;AACC,kBAAA,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UAAA;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEF,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AA4CO,MAAM,UAAU,CAAC,OAAe,MAAe,OAAsB;AAC1E,MAAI,WAAW;AACT,QAAA,gBAAgB,aAAa,OAAO,GAAG;AAC7C,MAAI,yBAAyB,YAAY;AAChC,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;"}