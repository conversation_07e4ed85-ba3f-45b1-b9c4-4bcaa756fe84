import{TokenType as e,NumberType as a,isTokenIdent as n,isTokenPercentage as r,isTokenNumber as o,isTokenDelim as t,isTokenNumeric as l,isTokenComma as s,isTokenDimension as u,isTokenHash as c}from"@csstools/css-tokenizer";import{XYZ_D50_to_XYZ_D65 as i,XYZ_D50_to_XYZ_D50 as h,XYZ_D50_to_OKLab as m,XYZ_D50_to_OKLCH as N,XYZ_D50_to_LCH as p,XYZ_D50_to_Lab as b,XYZ_D50_to_HWB as v,XYZ_D50_to_HSL as g,XYZ_D50_to_a98_RGB as f,XYZ_D50_to_ProPhoto as d,XYZ_D50_to_rec_2020 as y,XYZ_D50_to_P3 as _,XYZ_D50_to_lin_sRGB as C,XYZ_D50_to_sRGB as w,XYZ_D65_to_XYZ_D50 as x,OKLCH_to_XYZ_D50 as H,LCH_to_XYZ_D50 as L,OKLab_to_XYZ_D50 as k,Lab_to_XYZ_D50 as P,HWB_to_XYZ_D50 as S,HSL_to_XYZ_D50 as z,ProPhoto_RGB_to_XYZ_D50 as D,a98_RGB_to_XYZ_D50 as F,rec_2020_to_XYZ_D50 as Z,P3_to_XYZ_D50 as M,lin_sRGB_to_XYZ_D50 as R,sRGB_to_XYZ_D50 as B,namedColors as G,inGamut as T,clip as V,gam_sRGB as A,mapGamutRayTrace as X,OKLCH_to_OKLab as Y,OKLab_to_XYZ as K,XYZ_to_lin_sRGB as I,lin_sRGB_to_XYZ as O,XYZ_to_OKLab as E,OKLab_to_OKLCH as W,contrast_ratio_wcag_2_1 as U,gam_P3 as $,XYZ_to_lin_P3 as j,lin_P3_to_XYZ as q}from"@csstools/color-helpers";import{isWhitespaceNode as J,isCommentNode as Q,isTokenNode as ee,isFunctionNode as ae,TokenNode as ne,FunctionNode as re,WhitespaceNode as oe}from"@csstools/css-parser-algorithms";import{mathFunctionNames as te,calcFromComponentValues as le}from"@csstools/css-calc";var se,ue;function convertNaNToZero(e){return[Number.isNaN(e[0])?0:e[0],Number.isNaN(e[1])?0:e[1],Number.isNaN(e[2])?0:e[2]]}function colorData_to_XYZ_D50(e){switch(e.colorNotation){case se.HEX:case se.RGB:case se.sRGB:return{...e,colorNotation:se.XYZ_D50,channels:B(convertNaNToZero(e.channels))};case se.Linear_sRGB:return{...e,colorNotation:se.XYZ_D50,channels:R(convertNaNToZero(e.channels))};case se.Display_P3:return{...e,colorNotation:se.XYZ_D50,channels:M(convertNaNToZero(e.channels))};case se.Rec2020:return{...e,colorNotation:se.XYZ_D50,channels:Z(convertNaNToZero(e.channels))};case se.A98_RGB:return{...e,colorNotation:se.XYZ_D50,channels:F(convertNaNToZero(e.channels))};case se.ProPhoto_RGB:return{...e,colorNotation:se.XYZ_D50,channels:D(convertNaNToZero(e.channels))};case se.HSL:return{...e,colorNotation:se.XYZ_D50,channels:z(convertNaNToZero(e.channels))};case se.HWB:return{...e,colorNotation:se.XYZ_D50,channels:S(convertNaNToZero(e.channels))};case se.Lab:return{...e,colorNotation:se.XYZ_D50,channels:P(convertNaNToZero(e.channels))};case se.OKLab:return{...e,colorNotation:se.XYZ_D50,channels:k(convertNaNToZero(e.channels))};case se.LCH:return{...e,colorNotation:se.XYZ_D50,channels:L(convertNaNToZero(e.channels))};case se.OKLCH:return{...e,colorNotation:se.XYZ_D50,channels:H(convertNaNToZero(e.channels))};case se.XYZ_D50:return{...e,colorNotation:se.XYZ_D50,channels:h(convertNaNToZero(e.channels))};case se.XYZ_D65:return{...e,colorNotation:se.XYZ_D50,channels:x(convertNaNToZero(e.channels))};default:throw new Error("Unsupported color notation")}}!function(e){e.A98_RGB="a98-rgb",e.Display_P3="display-p3",e.HEX="hex",e.HSL="hsl",e.HWB="hwb",e.LCH="lch",e.Lab="lab",e.Linear_sRGB="srgb-linear",e.OKLCH="oklch",e.OKLab="oklab",e.ProPhoto_RGB="prophoto-rgb",e.RGB="rgb",e.sRGB="srgb",e.Rec2020="rec2020",e.XYZ_D50="xyz-d50",e.XYZ_D65="xyz-d65"}(se||(se={})),function(e){e.ColorKeyword="color-keyword",e.HasAlpha="has-alpha",e.HasDimensionValues="has-dimension-values",e.HasNoneKeywords="has-none-keywords",e.HasNumberValues="has-number-values",e.HasPercentageAlpha="has-percentage-alpha",e.HasPercentageValues="has-percentage-values",e.HasVariableAlpha="has-variable-alpha",e.Hex="hex",e.LegacyHSL="legacy-hsl",e.LegacyRGB="legacy-rgb",e.NamedColor="named-color",e.RelativeColorSyntax="relative-color-syntax",e.ColorMix="color-mix",e.ContrastColor="contrast-color",e.Experimental="experimental"}(ue||(ue={}));const ce=new Set([se.A98_RGB,se.Display_P3,se.HEX,se.Linear_sRGB,se.ProPhoto_RGB,se.RGB,se.sRGB,se.Rec2020,se.XYZ_D50,se.XYZ_D65]);function colorDataTo(e,a){const n={...e};if(e.colorNotation!==a){const e=colorData_to_XYZ_D50(n);switch(a){case se.HEX:case se.RGB:n.colorNotation=se.RGB,n.channels=w(e.channels);break;case se.sRGB:n.colorNotation=se.sRGB,n.channels=w(e.channels);break;case se.Linear_sRGB:n.colorNotation=se.Linear_sRGB,n.channels=C(e.channels);break;case se.Display_P3:n.colorNotation=se.Display_P3,n.channels=_(e.channels);break;case se.Rec2020:n.colorNotation=se.Rec2020,n.channels=y(e.channels);break;case se.ProPhoto_RGB:n.colorNotation=se.ProPhoto_RGB,n.channels=d(e.channels);break;case se.A98_RGB:n.colorNotation=se.A98_RGB,n.channels=f(e.channels);break;case se.HSL:n.colorNotation=se.HSL,n.channels=g(e.channels);break;case se.HWB:n.colorNotation=se.HWB,n.channels=v(e.channels);break;case se.Lab:n.colorNotation=se.Lab,n.channels=b(e.channels);break;case se.LCH:n.colorNotation=se.LCH,n.channels=p(e.channels);break;case se.OKLCH:n.colorNotation=se.OKLCH,n.channels=N(e.channels);break;case se.OKLab:n.colorNotation=se.OKLab,n.channels=m(e.channels);break;case se.XYZ_D50:n.colorNotation=se.XYZ_D50,n.channels=h(e.channels);break;case se.XYZ_D65:n.colorNotation=se.XYZ_D65,n.channels=i(e.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=convertNaNToZero(e.channels);if(a===e.colorNotation)n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);else if(ce.has(a)&&ce.has(e.colorNotation))n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);else switch(a){case se.HSL:switch(e.colorNotation){case se.HWB:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case se.Lab:case se.OKLab:n.channels=carryForwardMissingComponents(e.channels,[2],n.channels,[0]);break;case se.LCH:case se.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[2,1,0])}break;case se.HWB:switch(e.colorNotation){case se.HSL:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case se.LCH:case se.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2])}break;case se.Lab:case se.OKLab:switch(e.colorNotation){case se.HSL:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2]);break;case se.Lab:case se.OKLab:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);break;case se.LCH:case se.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0])}break;case se.LCH:case se.OKLCH:switch(e.colorNotation){case se.HSL:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[2,1,0]);break;case se.HWB:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2]);break;case se.Lab:case se.OKLab:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case se.LCH:case se.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=convertPowerlessComponentsToMissingComponents(n.channels,a),n}function convertPowerlessComponentsToMissingComponents(e,a){const n=[...e];switch(a){case se.HSL:!Number.isNaN(n[1])&&reducePrecision(n[1],4)<=0&&(n[0]=Number.NaN);break;case se.HWB:Math.max(0,reducePrecision(n[1],4))+Math.max(0,reducePrecision(n[2],4))>=100&&(n[0]=Number.NaN);break;case se.LCH:!Number.isNaN(n[1])&&reducePrecision(n[1],4)<=0&&(n[2]=Number.NaN);break;case se.OKLCH:!Number.isNaN(n[1])&&reducePrecision(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function convertPowerlessComponentsToZeroValuesForDisplay(e,a){const n=[...e];switch(a){case se.HSL:(reducePrecision(n[2])<=0||reducePrecision(n[2])>=100)&&(n[0]=Number.NaN,n[1]=Number.NaN),reducePrecision(n[1])<=0&&(n[0]=Number.NaN);break;case se.HWB:Math.max(0,reducePrecision(n[1]))+Math.max(0,reducePrecision(n[2]))>=100&&(n[0]=Number.NaN);break;case se.Lab:(reducePrecision(n[0])<=0||reducePrecision(n[0])>=100)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case se.LCH:reducePrecision(n[1])<=0&&(n[2]=Number.NaN),(reducePrecision(n[0])<=0||reducePrecision(n[0])>=100)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case se.OKLab:(reducePrecision(n[0])<=0||reducePrecision(n[0])>=1)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case se.OKLCH:reducePrecision(n[1])<=0&&(n[2]=Number.NaN),(reducePrecision(n[0])<=0||reducePrecision(n[0])>=1)&&(n[1]=Number.NaN,n[2]=Number.NaN)}return n}function carryForwardMissingComponents(e,a,n,r){const o=[...n];for(const n of a)Number.isNaN(e[a[n]])&&(o[r[n]]=Number.NaN);return o}function normalizeRelativeColorDataChannels(e){const a=new Map;switch(e.colorNotation){case se.RGB:case se.HEX:a.set("r",dummyNumberToken(255*e.channels[0])),a.set("g",dummyNumberToken(255*e.channels[1])),a.set("b",dummyNumberToken(255*e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.HSL:a.set("h",dummyNumberToken(e.channels[0])),a.set("s",dummyNumberToken(e.channels[1])),a.set("l",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.HWB:a.set("h",dummyNumberToken(e.channels[0])),a.set("w",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.Lab:case se.OKLab:a.set("l",dummyNumberToken(e.channels[0])),a.set("a",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.LCH:case se.OKLCH:a.set("l",dummyNumberToken(e.channels[0])),a.set("c",dummyNumberToken(e.channels[1])),a.set("h",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.sRGB:case se.A98_RGB:case se.Display_P3:case se.Rec2020:case se.Linear_sRGB:case se.ProPhoto_RGB:a.set("r",dummyNumberToken(e.channels[0])),a.set("g",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case se.XYZ_D50:case se.XYZ_D65:a.set("x",dummyNumberToken(e.channels[0])),a.set("y",dummyNumberToken(e.channels[1])),a.set("z",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha))}return a}function noneToZeroInRelativeColorDataChannels(e){const a=new Map(e);for(const[n,r]of e)Number.isNaN(r[4].value)&&a.set(n,dummyNumberToken(0));return a}function dummyNumberToken(n){return Number.isNaN(n)?[e.Number,"none",-1,-1,{value:Number.NaN,type:a.Number}]:[e.Number,n.toString(),-1,-1,{value:n,type:a.Number}]}function reducePrecision(e,a=7){if(Number.isNaN(e))return 0;const n=Math.pow(10,a);return Math.round(e*n)/n}function colorDataFitsRGB_Gamut(e){const a={...e,channels:[...e.channels]};a.channels=convertPowerlessComponentsToZeroValuesForDisplay(a.channels,a.colorNotation);return!colorDataTo(a,se.RGB).channels.find((e=>e<-1e-5||e>1.00001))}function colorDataFitsDisplayP3_Gamut(e){const a={...e,channels:[...e.channels]};a.channels=convertPowerlessComponentsToZeroValuesForDisplay(a.channels,a.colorNotation);return!colorDataTo(a,se.Display_P3).channels.find((e=>e<-1e-5||e>1.00001))}function normalize(e,a,n,r){return Math.min(Math.max(e/a,n),r)}const ie=/[A-Z]/g;function toLowerCaseAZ(e){return e.replace(ie,(e=>String.fromCharCode(e.charCodeAt(0)+32)))}function normalize_Color_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,100,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,1,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}const he=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function color$1(e,a){const r=[],s=[],u=[],c=[];let i,h,m=!1,N=!1;const p={colorNotation:se.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let b=r;for(let o=0;o<e.value.length;o++){let v=e.value[o];if(J(v)||Q(v))for(;J(e.value[o+1])||Q(e.value[o+1]);)o++;else if(b===r&&r.length&&(b=s),b===s&&s.length&&(b=u),ee(v)&&t(v.value)&&"/"===v.value[4].value){if(b===c)return!1;b=c}else{if(ae(v)){if(b===c&&"var"===toLowerCaseAZ(v.getName())){p.syntaxFlags.add(ue.HasVariableAlpha),b.push(v);continue}if(!te.has(toLowerCaseAZ(v.getName())))return!1;const[[e]]=le([[v]],{censorIntoStandardRepresentableValues:!0,globals:h,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),v=e}if(b===r&&0===r.length&&ee(v)&&n(v.value)&&he.has(toLowerCaseAZ(v.value[4].value))){if(m)return!1;m=toLowerCaseAZ(v.value[4].value),p.colorNotation=colorSpaceNameToColorNotation(m),N&&(N.colorNotation!==p.colorNotation&&(N=colorDataTo(N,p.colorNotation)),i=normalizeRelativeColorDataChannels(N),h=noneToZeroInRelativeColorDataChannels(i))}else if(b===r&&0===r.length&&ee(v)&&n(v.value)&&"from"===toLowerCaseAZ(v.value[4].value)){if(N)return!1;if(m)return!1;for(;J(e.value[o+1])||Q(e.value[o+1]);)o++;if(o++,v=e.value[o],N=a(v),!1===N)return!1;N.syntaxFlags.has(ue.Experimental)&&p.syntaxFlags.add(ue.Experimental),p.syntaxFlags.add(ue.RelativeColorSyntax)}else{if(!ee(v))return!1;if(n(v.value)&&i&&i.has(toLowerCaseAZ(v.value[4].value))){b.push(new ne(i.get(toLowerCaseAZ(v.value[4].value))));continue}b.push(v)}}}if(!m)return!1;if(1!==b.length)return!1;if(1!==r.length||1!==s.length||1!==u.length)return!1;if(!ee(r[0])||!ee(s[0])||!ee(u[0]))return!1;if(i&&!i.has("alpha"))return!1;const v=normalize_Color_ChannelValues(r[0].value,0,p);if(!v||!o(v))return!1;const g=normalize_Color_ChannelValues(s[0].value,1,p);if(!g||!o(g))return!1;const f=normalize_Color_ChannelValues(u[0].value,2,p);if(!f||!o(f))return!1;const d=[v,g,f];if(1===c.length)if(p.syntaxFlags.add(ue.HasAlpha),ee(c[0])){const e=normalize_Color_ChannelValues(c[0].value,3,p);if(!e||!o(e))return!1;d.push(e)}else p.alpha=c[0];else if(i&&i.has("alpha")){const e=normalize_Color_ChannelValues(i.get("alpha"),3,p);if(!e||!o(e))return!1;d.push(e)}return p.channels=[d[0][4].value,d[1][4].value,d[2][4].value],4===d.length&&(p.alpha=d[3][4].value),p}function colorSpaceNameToColorNotation(e){switch(e){case"srgb":return se.sRGB;case"srgb-linear":return se.Linear_sRGB;case"display-p3":return se.Display_P3;case"a98-rgb":return se.A98_RGB;case"prophoto-rgb":return se.ProPhoto_RGB;case"rec2020":return se.Rec2020;case"xyz":case"xyz-d65":return se.XYZ_D65;case"xyz-d50":return se.XYZ_D50;default:throw new Error("Unknown color space name: "+e)}}const me=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Ne=new Set(["hsl","hwb","lch","oklch"]),pe=new Set(["shorter","longer","increasing","decreasing"]);function colorMix(e,a){let r=null,o=null,t=null,l=!1;for(let u=0;u<e.value.length;u++){const c=e.value[u];if(!J(c)&&!Q(c)){if(ee(c)&&n(c.value)){if(!r&&"in"===toLowerCaseAZ(c.value[4].value)){r=c;continue}if(r&&!o){o=toLowerCaseAZ(c.value[4].value);continue}if(r&&o&&!t&&Ne.has(o)){t=toLowerCaseAZ(c.value[4].value);continue}if(r&&o&&t&&!l&&"hue"===toLowerCaseAZ(c.value[4].value)){l=!0;continue}return!1}return!(!ee(c)||!s(c.value))&&(!!o&&(t||l?!!(o&&t&&l&&Ne.has(o)&&pe.has(t))&&colorMixPolar(o,t,colorMixComponents(e.value.slice(u+1),a)):me.has(o)?colorMixRectangular(o,colorMixComponents(e.value.slice(u+1),a)):!!Ne.has(o)&&colorMixPolar(o,"shorter",colorMixComponents(e.value.slice(u+1),a))))}}return!1}function colorMixComponents(e,a){const n=[];let o=1,t=!1,u=!1;for(let o=0;o<e.length;o++){let c=e[o];if(!J(c)&&!Q(c)){if(!ee(c)||!s(c.value)){if(!t){const e=a(c);if(e){t=e;continue}}if(!u){if(ae(c)&&te.has(toLowerCaseAZ(c.getName()))){if([[c]]=le([[c]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!c||!ee(c)||!l(c.value))return!1;Number.isNaN(c.value[4].value)&&(c.value[4].value=0)}if(ee(c)&&r(c.value)&&c.value[4].value>=0){u=c.value[4].value;continue}}return!1}if(!t)return!1;n.push({color:t,percentage:u}),t=!1,u=!1}}if(t&&n.push({color:t,percentage:u}),2!==n.length)return!1;let c=n[0].percentage,i=n[1].percentage;return(!1===c||!(c<0||c>100))&&((!1===i||!(i<0||i>100))&&(!1===c&&!1===i?(c=50,i=50):!1!==c&&!1===i?i=100-c:!1===c&&!1!==i&&(c=100-i),(0!==c||0!==i)&&(!1!==c&&!1!==i&&(c+i>100&&(c=c/(c+i)*100,i=i/(c+i)*100),c+i<100&&(o=(c+i)/100,c=c/(c+i)*100,i=i/(c+i)*100),{a:{color:n[0].color,percentage:c},b:{color:n[1].color,percentage:i},alphaMultiplier:o}))))}function colorMixRectangular(e,a){if(!a)return!1;const n=a.a.color,r=a.b.color,o=a.a.percentage/100;let t=n.channels,l=r.channels,s=se.RGB,u=n.alpha;if("number"!=typeof u)return!1;let c=r.alpha;if("number"!=typeof c)return!1;switch(u=Number.isNaN(u)?c:u,c=Number.isNaN(c)?u:c,e){case"srgb":s=se.RGB;break;case"srgb-linear":s=se.Linear_sRGB;break;case"display-p3":s=se.Display_P3;break;case"a98-rgb":s=se.A98_RGB;break;case"prophoto-rgb":s=se.ProPhoto_RGB;break;case"rec2020":s=se.Rec2020;break;case"lab":s=se.Lab;break;case"oklab":s=se.OKLab;break;case"xyz-d50":s=se.XYZ_D50;break;case"xyz":case"xyz-d65":s=se.XYZ_D65}t=colorDataTo(n,s).channels,l=colorDataTo(r,s).channels,t[0]=fillInMissingComponent(t[0],l[0]),l[0]=fillInMissingComponent(l[0],t[0]),t[1]=fillInMissingComponent(t[1],l[1]),l[1]=fillInMissingComponent(l[1],t[1]),t[2]=fillInMissingComponent(t[2],l[2]),l[2]=fillInMissingComponent(l[2],t[2]),t[0]=premultiply(t[0],u),t[1]=premultiply(t[1],u),t[2]=premultiply(t[2],u),l[0]=premultiply(l[0],c),l[1]=premultiply(l[1],c),l[2]=premultiply(l[2],c);const i=interpolate(u,c,o),h={colorNotation:s,channels:[un_premultiply(interpolate(t[0],l[0],o),i),un_premultiply(interpolate(t[1],l[1],o),i),un_premultiply(interpolate(t[2],l[2],o),i)],alpha:i*a.alphaMultiplier,syntaxFlags:new Set([ue.ColorMix])};return(a.a.color.syntaxFlags.has(ue.Experimental)||a.b.color.syntaxFlags.has(ue.Experimental))&&h.syntaxFlags.add(ue.Experimental),h}function colorMixPolar(e,a,n){if(!n)return!1;const r=n.a.color,o=n.b.color,t=n.a.percentage/100;let l=r.channels,s=o.channels,u=0,c=0,i=0,h=0,m=0,N=0,p=se.RGB,b=r.alpha;if("number"!=typeof b)return!1;let v=o.alpha;if("number"!=typeof v)return!1;switch(b=Number.isNaN(b)?v:b,v=Number.isNaN(v)?b:v,e){case"hsl":p=se.HSL;break;case"hwb":p=se.HWB;break;case"lch":p=se.LCH;break;case"oklch":p=se.OKLCH}switch(l=colorDataTo(r,p).channels,s=colorDataTo(o,p).channels,e){case"hsl":case"hwb":u=l[0],c=s[0],i=l[1],h=s[1],m=l[2],N=s[2];break;case"lch":case"oklch":i=l[0],h=s[0],m=l[1],N=s[1],u=l[2],c=s[2]}u=fillInMissingComponent(u,c),Number.isNaN(u)&&(u=0),c=fillInMissingComponent(c,u),Number.isNaN(c)&&(c=0),i=fillInMissingComponent(i,h),h=fillInMissingComponent(h,i),m=fillInMissingComponent(m,N),N=fillInMissingComponent(N,m);const g=c-u;switch(a){case"shorter":g>180?u+=360:g<-180&&(c+=360);break;case"longer":-180<g&&g<180&&(g>0?u+=360:c+=360);break;case"increasing":g<0&&(c+=360);break;case"decreasing":g>0&&(u+=360);break;default:throw new Error("Unknown hue interpolation method")}i=premultiply(i,b),m=premultiply(m,b),h=premultiply(h,v),N=premultiply(N,v);let f=[0,0,0];const d=interpolate(b,v,t);switch(e){case"hsl":case"hwb":f=[interpolate(u,c,t),un_premultiply(interpolate(i,h,t),d),un_premultiply(interpolate(m,N,t),d)];break;case"lch":case"oklch":f=[un_premultiply(interpolate(i,h,t),d),un_premultiply(interpolate(m,N,t),d),interpolate(u,c,t)]}const y={colorNotation:p,channels:f,alpha:d*n.alphaMultiplier,syntaxFlags:new Set([ue.ColorMix])};return(n.a.color.syntaxFlags.has(ue.Experimental)||n.b.color.syntaxFlags.has(ue.Experimental))&&y.syntaxFlags.add(ue.Experimental),y}function fillInMissingComponent(e,a){return Number.isNaN(e)?a:e}function interpolate(e,a,n){return e*n+a*(1-n)}function premultiply(e,a){return Number.isNaN(a)?e:Number.isNaN(e)?Number.NaN:e*a}function un_premultiply(e,a){return 0===a||Number.isNaN(a)?e:Number.isNaN(e)?Number.NaN:e/a}function hex(e){const a=toLowerCaseAZ(e[4].value);if(a.match(/[^a-f0-9]/))return!1;const n={colorNotation:se.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([ue.Hex])},r=a.length;if(3===r){const e=a[0],r=a[1],o=a[2];return n.channels=[parseInt(e+e,16)/255,parseInt(r+r,16)/255,parseInt(o+o,16)/255],n}if(6===r){const e=a[0]+a[1],r=a[2]+a[3],o=a[4]+a[5];return n.channels=[parseInt(e,16)/255,parseInt(r,16)/255,parseInt(o,16)/255],n}if(4===r){const e=a[0],r=a[1],o=a[2],t=a[3];return n.channels=[parseInt(e+e,16)/255,parseInt(r+r,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(t+t,16)/255,n.syntaxFlags.add(ue.HasAlpha),n}if(8===r){const e=a[0]+a[1],r=a[2]+a[3],o=a[4]+a[5],t=a[6]+a[7];return n.channels=[parseInt(e,16)/255,parseInt(r,16)/255,parseInt(o,16)/255],n.alpha=parseInt(t,16)/255,n.syntaxFlags.add(ue.HasAlpha),n}return!1}function normalizeHue(n){if(o(n))return n[4].value=n[4].value%360,n[1]=n[4].value.toString(),n;if(u(n)){let r=n[4].value;switch(toLowerCaseAZ(n[4].unit)){case"deg":break;case"rad":r=180*n[4].value/Math.PI;break;case"grad":r=.9*n[4].value;break;case"turn":r=360*n[4].value;break;default:return!1}return r%=360,[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_legacy_HSL_ChannelValues(n,t,l){if(0===t){const e=normalizeHue(n);return!1!==e&&(u(n)&&l.syntaxFlags.add(ue.HasDimensionValues),e)}if(r(n)){3===t?l.syntaxFlags.add(ue.HasPercentageAlpha):l.syntaxFlags.add(ue.HasPercentageValues);let r=normalize(n[4].value,1,0,100);return 3===t&&(r=normalize(n[4].value,100,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}if(o(n)){if(3!==t)return!1;let r=normalize(n[4].value,1,0,100);return 3===t&&(r=normalize(n[4].value,1,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_modern_HSL_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(0===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ue.HasDimensionValues),e)}if(r(t)){3===l?s.syntaxFlags.add(ue.HasPercentageAlpha):s.syntaxFlags.add(ue.HasPercentageValues);let n=t[4].value;return 3===l?n=normalize(t[4].value,100,0,1):1===l&&(n=normalize(t[4].value,1,0,2147483647)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=t[4].value;return 3===l?n=normalize(t[4].value,1,0,1):1===l&&(n=normalize(t[4].value,1,0,2147483647)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function threeChannelLegacySyntax(e,a,n,r){const t=[],u=[],c=[],i=[],h={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let m=t;for(let a=0;a<e.value.length;a++){let n=e.value[a];if(!J(n)&&!Q(n)){if(ee(n)&&s(n.value)){if(m===t){m=u;continue}if(m===u){m=c;continue}if(m===c){m=i;continue}if(m===i)return!1}if(ae(n)){if(m===i&&"var"===n.getName().toLowerCase()){h.syntaxFlags.add(ue.HasVariableAlpha),m.push(n);continue}if(!te.has(n.getName().toLowerCase()))return!1;const[[e]]=le([[n]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),n=e}if(!ee(n))return!1;m.push(n)}}if(1!==m.length)return!1;if(1!==t.length||1!==u.length||1!==c.length)return!1;if(!ee(t[0])||!ee(u[0])||!ee(c[0]))return!1;const N=a(t[0].value,0,h);if(!N||!o(N))return!1;const p=a(u[0].value,1,h);if(!p||!o(p))return!1;const b=a(c[0].value,2,h);if(!b||!o(b))return!1;const v=[N,p,b];if(1===i.length)if(h.syntaxFlags.add(ue.HasAlpha),ee(i[0])){const e=a(i[0].value,3,h);if(!e||!o(e))return!1;v.push(e)}else h.alpha=i[0];return h.channels=[v[0][4].value,v[1][4].value,v[2][4].value],4===v.length&&(h.alpha=v[3][4].value),h}function threeChannelSpaceSeparated(e,a,r,s,u){const c=[],i=[],h=[],m=[];let N,p,b=!1;const v={colorNotation:r,channels:[0,0,0],alpha:1,syntaxFlags:new Set(s)};let g=c;for(let a=0;a<e.value.length;a++){let o=e.value[a];if(J(o)||Q(o))for(;J(e.value[a+1])||Q(e.value[a+1]);)a++;else if(g===c&&c.length&&(g=i),g===i&&i.length&&(g=h),ee(o)&&t(o.value)&&"/"===o.value[4].value){if(g===m)return!1;g=m}else{if(ae(o)){if(g===m&&"var"===o.getName().toLowerCase()){v.syntaxFlags.add(ue.HasVariableAlpha),g.push(o);continue}if(!te.has(o.getName().toLowerCase()))return!1;const[[e]]=le([[o]],{censorIntoStandardRepresentableValues:!0,globals:p,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),o=e}if(g===c&&0===c.length&&ee(o)&&n(o.value)&&"from"===o.value[4].value.toLowerCase()){if(b)return!1;for(;J(e.value[a+1])||Q(e.value[a+1]);)a++;if(a++,o=e.value[a],b=u(o),!1===b)return!1;b.syntaxFlags.has(ue.Experimental)&&v.syntaxFlags.add(ue.Experimental),v.syntaxFlags.add(ue.RelativeColorSyntax),b.colorNotation!==r&&(b=colorDataTo(b,r)),N=normalizeRelativeColorDataChannels(b),p=noneToZeroInRelativeColorDataChannels(N)}else{if(!ee(o))return!1;if(n(o.value)&&N){const e=o.value[4].value.toLowerCase();if(N.has(e)){g.push(new ne(N.get(e)));continue}}g.push(o)}}}if(1!==g.length)return!1;if(1!==c.length||1!==i.length||1!==h.length)return!1;if(!ee(c[0])||!ee(i[0])||!ee(h[0]))return!1;if(N&&!N.has("alpha"))return!1;const f=a(c[0].value,0,v);if(!f||!o(f))return!1;const d=a(i[0].value,1,v);if(!d||!o(d))return!1;const y=a(h[0].value,2,v);if(!y||!o(y))return!1;const _=[f,d,y];if(1===m.length)if(v.syntaxFlags.add(ue.HasAlpha),ee(m[0])){const e=a(m[0].value,3,v);if(!e||!o(e))return!1;_.push(e)}else v.alpha=m[0];else if(N&&N.has("alpha")){const e=a(N.get("alpha"),3,v);if(!e||!o(e))return!1;_.push(e)}return v.channels=[_[0][4].value,_[1][4].value,_[2][4].value],4===_.length&&(v.alpha=_[3][4].value),v}function hsl(e,a){if(e.value.some((e=>ee(e)&&s(e.value)))){const a=hslCommaSeparated(e);if(!1!==a)return a}{const n=hslSpaceSeparated(e,a);if(!1!==n)return n}return!1}function hslCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_HSL_ChannelValues,se.HSL,[ue.LegacyHSL])}function hslSpaceSeparated(e,a){return threeChannelSpaceSeparated(e,normalize_modern_HSL_ChannelValues,se.HSL,[],a)}function normalize_HWB_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(0===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ue.HasDimensionValues),e)}if(r(t)){3===l?s.syntaxFlags.add(ue.HasPercentageAlpha):s.syntaxFlags.add(ue.HasPercentageValues);let n=t[4].value;return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=t[4].value;return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function normalize_Lab_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,1,0,100);return 1===l||2===l?n=normalize(t[4].value,.8,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,1,0,100);return 1===l||2===l?n=normalize(t[4].value,1,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function lab(e,a){return threeChannelSpaceSeparated(e,normalize_Lab_ChannelValues,se.Lab,[],a)}function normalize_LCH_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(2===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ue.HasDimensionValues),e)}if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,1,0,100);return 1===l?n=normalize(t[4].value,100/150,0,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,1,0,100);return 1===l?n=normalize(t[4].value,1,0,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function lch(e,a){return threeChannelSpaceSeparated(e,normalize_LCH_ChannelValues,se.LCH,[],a)}const be=new Map;for(const[e,a]of Object.entries(G))be.set(e,a);function namedColor(e){const a=be.get(toLowerCaseAZ(e));return!!a&&{colorNotation:se.RGB,channels:[a[0]/255,a[1]/255,a[2]/255],alpha:1,syntaxFlags:new Set([ue.ColorKeyword,ue.NamedColor])}}function normalize_OKLab_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,100,0,1);return 1===l||2===l?n=normalize(t[4].value,250,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,1,0,1);return 1===l||2===l?n=normalize(t[4].value,1,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function oklab(e,a){return threeChannelSpaceSeparated(e,normalize_OKLab_ChannelValues,se.OKLab,[],a)}function normalize_OKLCH_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(2===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ue.HasDimensionValues),e)}if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,100,0,1);return 1===l?n=normalize(t[4].value,250,0,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,1,0,1);return 1===l?n=normalize(t[4].value,1,0,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function oklch(e,a){return threeChannelSpaceSeparated(e,normalize_OKLCH_ChannelValues,se.OKLCH,[],a)}function normalize_legacy_sRGB_ChannelValues(n,t,l){if(r(n)){3===t?l.syntaxFlags.add(ue.HasPercentageAlpha):l.syntaxFlags.add(ue.HasPercentageValues);const r=normalize(n[4].value,100,0,1);return[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}if(o(n)){3!==t&&l.syntaxFlags.add(ue.HasNumberValues);let r=normalize(n[4].value,255,0,1);return 3===t&&(r=normalize(n[4].value,1,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_modern_sRGB_ChannelValues(t,l,s){if(n(t)&&"none"===t[4].value.toLowerCase())return s.syntaxFlags.add(ue.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ue.HasPercentageValues);let n=normalize(t[4].value,100,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ue.HasNumberValues);let n=normalize(t[4].value,255,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function rgb(e,a){if(e.value.some((e=>ee(e)&&s(e.value)))){const a=rgbCommaSeparated(e);if(!1!==a)return(!a.syntaxFlags.has(ue.HasNumberValues)||!a.syntaxFlags.has(ue.HasPercentageValues))&&a}else{const n=rgbSpaceSeparated(e,a);if(!1!==n)return n}return!1}function rgbCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_sRGB_ChannelValues,se.RGB,[ue.LegacyRGB])}function rgbSpaceSeparated(e,a){return threeChannelSpaceSeparated(e,normalize_modern_sRGB_ChannelValues,se.RGB,[],a)}function XYZ_D50_to_sRGB_Gamut(e){const a=w(e);if(T(a))return V(a);let n=e;return n=N(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),A(X(n,oklch_to_lin_srgb,lin_srgb_to_oklch))}function oklch_to_lin_srgb(e){return e=Y(e),e=K(e),I(e)}function lin_srgb_to_oklch(e){return e=O(e),e=E(e),W(e)}function contrastColor(e,a){let n=!1;for(let r=0;r<e.value.length;r++){const o=e.value[r];if(!J(o)&&!Q(o)&&(n||(n=a(o),!n)))return!1}if(!n)return!1;n.channels=convertNaNToZero(n.channels),n.channels=XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels),n.colorNotation=se.sRGB;const r={colorNotation:se.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([ue.ContrastColor,ue.Experimental])},o=U(n.channels,[1,1,1]),t=U(n.channels,[0,0,0]);return r.channels=o>t?[1,1,1]:[0,0,0],r}function XYZ_D50_to_P3_Gamut(e){const a=_(e);if(T(a))return V(a);let n=e;return n=N(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),$(X(n,oklch_to_lin_p3,lin_p3_to_oklch))}function oklch_to_lin_p3(e){return e=Y(e),e=K(e),j(e)}function lin_p3_to_oklch(e){return e=q(e),e=E(e),W(e)}function toPrecision(e,a=7){e=+e,a=+a;const n=(Math.floor(Math.abs(e))+"").length;if(a>n)return+e.toFixed(a-n);{const r=10**(n-a);return Math.round(e/r)*r}}function serializeWithAlpha(n,r,o,t){const l=[e.CloseParen,")",-1,-1,void 0];if("number"==typeof n.alpha){const s=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(s,4)?new re(r,l,t):new re(r,l,[...t,new oe([o]),new ne([e.Delim,"/",-1,-1,{value:"/"}]),new oe([o]),new ne([e.Number,toPrecision(s,4).toString(),-1,-1,{value:n.alpha,type:a.Integer}])])}return new re(r,l,[...t,new oe([o]),new ne([e.Delim,"/",-1,-1,{value:"/"}]),new oe([o]),n.alpha])}function serializeP3(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));r?o=XYZ_D50_to_P3_Gamut(colorData_to_XYZ_D50(n).channels):n.colorNotation!==se.Display_P3&&(o=_(colorData_to_XYZ_D50(n).channels));const t=r?Math.min(1,Math.max(0,toPrecision(o[0],6))):toPrecision(o[0],6),l=r?Math.min(1,Math.max(0,toPrecision(o[1],6))):toPrecision(o[1],6),s=r?Math.min(1,Math.max(0,toPrecision(o[2],6))):toPrecision(o[2],6),u=[e.Function,"color(",-1,-1,{value:"color"}],c=[e.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(n,u,c,[new ne([e.Ident,"display-p3",-1,-1,{value:"display-p3"}]),new oe([c]),new ne([e.Number,t.toString(),-1,-1,{value:o[0],type:a.Number}]),new oe([c]),new ne([e.Number,l.toString(),-1,-1,{value:o[1],type:a.Number}]),new oe([c]),new ne([e.Number,s.toString(),-1,-1,{value:o[2],type:a.Number}])])}function serializeRGB(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));o=r?XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels):w(colorData_to_XYZ_D50(n).channels);const t=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[0])))),l=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[1])))),s=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[2])))),u=[e.CloseParen,")",-1,-1,void 0],c=[e.Whitespace," ",-1,-1,void 0],i=[e.Comma,",",-1,-1,void 0],h=[new ne([e.Number,t.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[0])),type:a.Integer}]),new ne(i),new oe([c]),new ne([e.Number,l.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[1])),type:a.Integer}]),new ne(i),new oe([c]),new ne([e.Number,s.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[2])),type:a.Integer}])];if("number"==typeof n.alpha){const r=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(r,4)?new re([e.Function,"rgb(",-1,-1,{value:"rgb"}],u,h):new re([e.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...h,new ne(i),new oe([c]),new ne([e.Number,toPrecision(r,4).toString(),-1,-1,{value:n.alpha,type:a.Number}])])}return new re([e.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...h,new ne(i),new oe([c]),n.alpha])}function serializeHSL(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));o=g(r?B(XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels)):colorData_to_XYZ_D50(n).channels),o=o.map((e=>Number.isNaN(e)?0:e));const t=Math.min(360,Math.max(0,Math.round(toPrecision(o[0])))),l=Math.min(100,Math.max(0,Math.round(toPrecision(o[1])))),s=Math.min(100,Math.max(0,Math.round(toPrecision(o[2])))),u=[e.CloseParen,")",-1,-1,void 0],c=[e.Whitespace," ",-1,-1,void 0],i=[e.Comma,",",-1,-1,void 0],h=[new ne([e.Number,t.toString(),-1,-1,{value:o[0],type:a.Integer}]),new ne(i),new oe([c]),new ne([e.Percentage,l.toString()+"%",-1,-1,{value:o[1]}]),new ne(i),new oe([c]),new ne([e.Percentage,s.toString()+"%",-1,-1,{value:o[2]}])];if("number"==typeof n.alpha){const r=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(r,4)?new re([e.Function,"hsl(",-1,-1,{value:"hsl"}],u,h):new re([e.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...h,new ne(i),new oe([c]),new ne([e.Number,toPrecision(r,4).toString(),-1,-1,{value:n.alpha,type:a.Number}])])}return new re([e.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...h,new ne(i),new oe([c]),n.alpha])}function serializeOKLCH(n){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let r=n.channels.map((e=>Number.isNaN(e)?0:e));n.colorNotation!==se.OKLCH&&(r=N(colorData_to_XYZ_D50(n).channels));const o=toPrecision(r[0],6),t=toPrecision(r[1],6),l=toPrecision(r[2],6),s=[e.Function,"oklch(",-1,-1,{value:"oklch"}],u=[e.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(n,s,u,[new ne([e.Number,o.toString(),-1,-1,{value:r[0],type:a.Number}]),new oe([u]),new ne([e.Number,t.toString(),-1,-1,{value:r[1],type:a.Number}]),new oe([u]),new ne([e.Number,l.toString(),-1,-1,{value:r[2],type:a.Number}])])}function color(e){if(ae(e)){switch(toLowerCaseAZ(e.getName())){case"rgb":case"rgba":return rgb(e,color);case"hsl":case"hsla":return hsl(e,color);case"hwb":return a=color,threeChannelSpaceSeparated(e,normalize_HWB_ChannelValues,se.HWB,[],a);case"lab":return lab(e,color);case"lch":return lch(e,color);case"oklab":return oklab(e,color);case"oklch":return oklch(e,color);case"color":return color$1(e,color);case"color-mix":return colorMix(e,color);case"contrast-color":return contrastColor(e,color)}}var a;if(ee(e)){if(c(e.value))return hex(e.value);if(n(e.value)){const a=namedColor(e.value[4].value);return!1!==a?a:"transparent"===toLowerCaseAZ(e.value[4].value)&&{colorNotation:se.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([ue.ColorKeyword])}}}return!1}export{se as ColorNotation,ue as SyntaxFlag,color,colorDataFitsDisplayP3_Gamut,colorDataFitsRGB_Gamut,serializeHSL,serializeOKLCH,serializeP3,serializeRGB};
