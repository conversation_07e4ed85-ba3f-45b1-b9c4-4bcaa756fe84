/**
 * Routes pour l'éditeur de code Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Route pour la page de l'éditeur de code
router.get('/', (req, res) => {
  res.render('luna-code', {
    title: 'Luna - Éditeur de Code',
    page: 'code'
  });
});

// API pour lister les fichiers de code
router.get('/api/files', (req, res) => {
  try {
    const codeDir = path.join(__dirname, '../code');
    if (!fs.existsSync(codeDir)) {
      fs.mkdirSync(codeDir, { recursive: true });
      return res.json({ success: true, files: [] });
    }

    const files = fs.readdirSync(codeDir);
    const codeFiles = files.filter(file => 
      file.endsWith('.js') || file.endsWith('.html') || 
      file.endsWith('.css') || file.endsWith('.json')
    ).map(file => {
      const filePath = path.join(codeDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        modified: stats.mtime
      };
    });

    res.json({ success: true, files: codeFiles });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour lire un fichier
router.get('/api/file/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../code', filename);
    
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      res.json({ success: true, content });
    } else {
      res.json({ success: false, error: 'Fichier non trouvé' });
    }
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour sauvegarder un fichier
router.post('/api/save', (req, res) => {
  try {
    const { filename, content } = req.body;
    
    if (!filename || content === undefined) {
      return res.json({ success: false, error: 'Nom de fichier et contenu requis' });
    }

    const filePath = path.join(__dirname, '../code', filename);
    fs.writeFileSync(filePath, content, 'utf8');
    
    res.json({ success: true, message: 'Fichier sauvegardé avec succès' });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Initialiser les gestionnaires de socket
router.initSocketHandlers = function(io) {
  io.on('connection', (socket) => {
    socket.on('code-edit', (data) => {
      // Diffuser les modifications de code en temps réel
      socket.broadcast.emit('code-update', data);
    });
  });
};

module.exports = router;
