/**
 * Routes pour la gestion des médias Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Route pour la page des médias
router.get('/', (req, res) => {
  res.render('luna-media', {
    title: 'Luna - Médias',
    page: 'media'
  });
});

// API pour lister les médias
router.get('/api/list', (req, res) => {
  try {
    const mediaDir = path.join(__dirname, '../media');
    if (!fs.existsSync(mediaDir)) {
      fs.mkdirSync(mediaDir, { recursive: true });
      return res.json({ success: true, media: [] });
    }

    const files = fs.readdirSync(mediaDir);
    const mediaFiles = files.filter(file => 
      file.match(/\.(jpg|jpeg|png|gif|mp4|mp3|wav)$/i)
    ).map(file => {
      const filePath = path.join(mediaDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        type: path.extname(file).toLowerCase(),
        size: stats.size,
        created: stats.birthtime
      };
    });

    res.json({ success: true, media: mediaFiles });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour générer des médias
router.post('/api/generate', (req, res) => {
  try {
    const { type, prompt, options } = req.body;
    
    // Simulation de génération de média
    res.json({
      success: true,
      message: `Génération de ${type} démarrée`,
      taskId: Date.now().toString()
    });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Initialiser le générateur de média
router.initMediaGenerator = function(io, mcpSystem) {
  return {
    generateImage: async (prompt) => {
      // Logique de génération d'image
      return { success: true, url: '/media/generated-image.png' };
    },
    generateVideo: async (prompt) => {
      // Logique de génération de vidéo
      return { success: true, url: '/media/generated-video.mp4' };
    }
  };
};

// Initialiser les gestionnaires de socket
router.initSocketHandlers = function(io, thermalMemory) {
  io.on('connection', (socket) => {
    socket.on('media-generate', (data) => {
      // Traiter la génération de média
      socket.emit('media-progress', { progress: 50 });
      setTimeout(() => {
        socket.emit('media-complete', { url: '/media/result.png' });
      }, 3000);
    });
  });
};

module.exports = router;
