/**
 * Routes pour la gestion de la mémoire de Luna
 */
const express = require('express');
const router = express.Router();

// Référence à la mémoire thermique
let thermalMemory;

// Initialiser avec la référence à la mémoire thermique
router.init = function(memoryReference) {
  thermalMemory = memoryReference;
};

// Route pour effacer la mémoire
router.post('/clear', async (req, res) => {
  try {
    if (!thermalMemory) {
      return res.status(404).json({ 
        success: false, 
        error: "Le système de mémoire n'est pas disponible" 
      });
    }
    
    // Réinitialiser la mémoire si disponible
    if (typeof thermalMemory.resetMemory === 'function') {
      await thermalMemory.resetMemory();
      console.log('🧹 Mémoire thermique réinitialisée');
    } else {
      console.log('⚠️ Fonction resetMemory non disponible');
      // Alternative: vider les niveaux de mémoire individuellement
      if (thermalMemory.levels) {
        Object.keys(thermalMemory.levels).forEach(level => {
          if (thermalMemory.levels[level].items) {
            thermalMemory.levels[level].items = [];
          }
        });
        console.log('🧹 Niveaux de mémoire vidés manuellement');
      }
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Erreur lors de la réinitialisation de la mémoire:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || "Erreur lors de l'effacement de la mémoire" 
    });
  }
});

// Route pour récupérer l'état actuel de la mémoire
router.get('/status', (req, res) => {
  if (!thermalMemory) {
    return res.status(404).json({ error: "Système de mémoire non disponible" });
  }
  
  try {
    const memoryStats = {
      active: true,
      totalItems: 0,
      zones: {}
    };
    
    // Collecter des statistiques si la structure est disponible
    if (thermalMemory.levels) {
      Object.keys(thermalMemory.levels).forEach(level => {
        const levelData = thermalMemory.levels[level];
        const itemCount = levelData.items ? levelData.items.length : 0;
        memoryStats.totalItems += itemCount;
        
        memoryStats.zones[level] = {
          name: levelData.name || `Zone ${level}`,
          itemCount: itemCount,
          temperature: levelData.temperature || 0,
          capacity: levelData.capacity || 100,
          active: levelData.active !== undefined ? levelData.active : true
        };
      });
    }
    
    res.json(memoryStats);
  } catch (error) {
    console.error('Erreur lors de la récupération des stats de mémoire:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
