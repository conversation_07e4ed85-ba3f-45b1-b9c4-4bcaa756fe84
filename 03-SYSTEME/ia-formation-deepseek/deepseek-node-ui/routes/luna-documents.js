/**
 * Routes pour la gestion des documents Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const multer = require('multer');

// Configuration de multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../documents');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Route pour la page des documents
router.get('/', (req, res) => {
  res.render('luna-documents', {
    title: 'Luna - Documents',
    page: 'documents'
  });
});

// API pour lister les documents
router.get('/api/list', (req, res) => {
  try {
    const documentsDir = path.join(__dirname, '../documents');
    if (!fs.existsSync(documentsDir)) {
      return res.json({ success: true, documents: [] });
    }

    const files = fs.readdirSync(documentsDir);
    const documents = files.map(file => {
      const filePath = path.join(documentsDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    });

    res.json({ success: true, documents });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour uploader un document
router.post('/api/upload', upload.single('document'), (req, res) => {
  try {
    if (!req.file) {
      return res.json({ success: false, error: 'Aucun fichier fourni' });
    }

    res.json({
      success: true,
      message: 'Document uploadé avec succès',
      file: {
        name: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour supprimer un document
router.delete('/api/delete/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../documents', filename);
    
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      res.json({ success: true, message: 'Document supprimé avec succès' });
    } else {
      res.json({ success: false, error: 'Fichier non trouvé' });
    }
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

module.exports = router;
