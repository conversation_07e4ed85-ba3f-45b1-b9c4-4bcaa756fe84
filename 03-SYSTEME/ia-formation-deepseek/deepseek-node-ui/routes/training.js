/**
 * Routes pour l'entraînement Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Variables globales
let thermalMemory = null;
let io = null;
let mcpSystem = null;

// Route pour la page d'entraînement
router.get('/training', (req, res) => {
  res.render('luna-training', {
    title: 'Luna - Entraînement',
    page: 'training'
  });
});

// API pour obtenir les sessions d'entraînement
router.get('/training/sessions', (req, res) => {
  try {
    const sessionsFile = path.join(__dirname, '../data/training-sessions.json');
    let sessions = [];
    
    if (fs.existsSync(sessionsFile)) {
      sessions = JSON.parse(fs.readFileSync(sessionsFile, 'utf8'));
    }

    res.json({ success: true, sessions });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour créer une nouvelle session d'entraînement
router.post('/training/create', (req, res) => {
  try {
    const { name, type, data } = req.body;
    
    const session = {
      id: Date.now().toString(),
      name,
      type: type || 'general',
      data,
      created: new Date().toISOString(),
      status: 'pending',
      progress: 0
    };

    const sessionsFile = path.join(__dirname, '../data/training-sessions.json');
    let sessions = [];
    
    if (fs.existsSync(sessionsFile)) {
      sessions = JSON.parse(fs.readFileSync(sessionsFile, 'utf8'));
    }
    
    sessions.push(session);
    
    // Créer le dossier data s'il n'existe pas
    const dataDir = path.dirname(sessionsFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(sessionsFile, JSON.stringify(sessions, null, 2));

    res.json({ success: true, session });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour démarrer l'entraînement
router.post('/training/start/:sessionId', (req, res) => {
  try {
    const { sessionId } = req.params;
    
    // Simulation de démarrage d'entraînement
    res.json({
      success: true,
      message: 'Entraînement démarré',
      sessionId
    });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour obtenir les statistiques d'entraînement
router.get('/training/stats', (req, res) => {
  try {
    const stats = {
      totalSessions: 15,
      completedSessions: 12,
      averageAccuracy: 94.5,
      totalTrainingTime: 1250, // minutes
      lastTraining: new Date().toISOString()
    };

    res.json({ success: true, stats });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Fonction d'initialisation de la mémoire thermique
function initThermalMemory(memory, socketIo, mcp) {
  thermalMemory = memory;
  io = socketIo;
  mcpSystem = mcp;
  
  console.log('Système d\'entraînement initialisé avec la mémoire thermique');
}

module.exports = { router, initThermalMemory };
