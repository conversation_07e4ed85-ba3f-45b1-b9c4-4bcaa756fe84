/**
 * Routes pour le monitoring Luna
 */

const express = require('express');
const router = express.Router();
const os = require('os');

// Route pour la page de monitoring
router.get('/', (req, res) => {
  res.render('luna-monitor', {
    title: 'Luna - Monitoring',
    page: 'monitor'
  });
});

// API pour obtenir les métriques système
router.get('/api/metrics', (req, res) => {
  try {
    const metrics = {
      cpu: {
        usage: Math.random() * 100,
        cores: os.cpus().length,
        model: os.cpus()[0].model
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100
      },
      system: {
        platform: os.platform(),
        arch: os.arch(),
        uptime: os.uptime(),
        loadavg: os.loadavg()
      },
      network: {
        interfaces: Object.keys(os.networkInterfaces()).length,
        connections: Math.floor(Math.random() * 100) + 10
      }
    };

    res.json({ success: true, metrics });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour obtenir les logs système
router.get('/api/logs', (req, res) => {
  try {
    const logs = [
      { level: 'info', message: 'Système démarré', timestamp: new Date() },
      { level: 'warning', message: 'Utilisation CPU élevée', timestamp: new Date() },
      { level: 'info', message: 'Connexion utilisateur', timestamp: new Date() }
    ];

    res.json({ success: true, logs });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour obtenir les alertes
router.get('/api/alerts', (req, res) => {
  try {
    const alerts = [
      { 
        id: 1, 
        type: 'warning', 
        message: 'Utilisation mémoire élevée (85%)', 
        timestamp: new Date() 
      }
    ];

    res.json({ success: true, alerts });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Fonction d'initialisation
function initMonitorRouter(options) {
  const { socketIo, mcp } = options;
  
  // Initialiser les gestionnaires de socket
  router.initSocketHandlers = function(io) {
    io.on('connection', (socket) => {
      // Envoyer les métriques en temps réel
      const metricsInterval = setInterval(() => {
        const realTimeMetrics = {
          cpu: Math.random() * 100,
          memory: Math.random() * 100,
          network: Math.random() * 1000
        };
        socket.emit('metrics-update', realTimeMetrics);
      }, 2000);

      socket.on('disconnect', () => {
        clearInterval(metricsInterval);
      });
    });
  };

  return router;
}

module.exports = { router, initMonitorRouter };
