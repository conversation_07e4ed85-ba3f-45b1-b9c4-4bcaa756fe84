const express = require('express');
const router = express.Router();
const axios = require('axios');
const { exec } = require('child_process');

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434';

// Fonction utilitaire pour vérifier si Ollama est en cours d'exécution
async function isOllamaRunning() {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/api/tags`, { timeout: 2000 });
    return true;
  } catch (error) {
    console.error('Ollama n\'est pas accessible:', error.message);
    return false;
  }
}

// Endpoint pour vérifier le statut d'Ollama
router.get('/ollama/status', async (req, res) => {
  try {
    const isRunning = await isOllamaRunning();
    
    res.json({
      success: isRunning,
      message: isRunning ? 'Ollama est opérationnel' : 'Ollama n\'est pas en cours d\'exécution',
      activeModel: global.selectedOllamaModel || 'deepseek-r1:7b'
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Endpoint pour récupérer la liste des modèles disponibles
router.get('/models', async (req, res) => {
  try {
    console.log('Tentative de récupération des modèles disponibles dans Ollama...');
    
    // Vérifier d'abord qu'Ollama est en cours d'exécution
    if (!await isOllamaRunning()) {
      console.error('Impossible de récupérer les modèles : Ollama n\'est pas en cours d\'exécution');
      return res.json({
        success: false,
        error: 'Ollama n\'est pas en cours d\'exécution',
        models: []
      });
    }
    
    // Récupérer la liste des modèles depuis l'API Ollama avec timeout
    const response = await axios.get(`${OLLAMA_API_URL}/api/tags`, {
      timeout: 5000 // 5 secondes de timeout
    });
    
    // Vérifier que la réponse contient bien les modèles
    if (response.data && response.data.models && Array.isArray(response.data.models)) {
      const models = response.data.models.map(model => model.name).filter(Boolean);
      
      console.log(`${models.length} modèles récupérés depuis Ollama:`, models);
      
      // Si aucun modèle n'est disponible, envoyer une liste vide
      if (models.length === 0) {
        console.warn('Aucun modèle n\'a été détecté dans Ollama');
      }
      
      res.json({
        success: models.length > 0,
        models,
        activeModel: models.includes(global.selectedOllamaModel) ? 
                    global.selectedOllamaModel : 
                    (models.length > 0 ? models[0] : 'deepseek-r1:7b')
      });
    } else {
      console.error('Format de réponse inattendu de l\'API Ollama:', response.data);
      res.json({
        success: false,
        error: 'Format de réponse inattendu de l\'API Ollama',
        models: []
      });
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des modèles:', error.message);
    res.json({
      success: false,
      error: `Erreur: ${error.message}`,
      models: []
    });
  }
});

// Endpoint pour sélectionner un modèle
router.post('/models/select', (req, res) => {
  const { model } = req.body;
  
  if (!model) {
    return res.json({
      success: false,
      error: 'Aucun modèle spécifié'
    });
  }
  
  // Mettre à jour le modèle global
  global.selectedOllamaModel = model;
  console.log(`Modèle actif changé pour: ${model}`);
  
  res.json({
    success: true,
    model
  });
});

// Endpoint pour installer un nouveau modèle
router.post('/models/install', (req, res) => {
  const { model } = req.body;
  
  if (!model) {
    return res.json({
      success: false,
      error: 'Aucun modèle spécifié'
    });
  }
  
  // Exécuter la commande Ollama pour installer le modèle
  console.log(`Installation du modèle: ${model}`);
  
  const command = `ollama pull ${model}`;
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Erreur d'installation: ${error.message}`);
      return res.json({
        success: false,
        error: error.message
      });
    }
    
    if (stderr) {
      console.error(`Stderr: ${stderr}`);
      // On continue car stderr peut contenir des infos de progression
    }
    
    console.log(`Installation réussie: ${stdout}`);
    res.json({
      success: true,
      model,
      message: 'Modèle installé avec succès'
    });
  });
});

// Endpoint pour supprimer un modèle installé
router.delete('/models/remove', (req, res) => {
  const { model } = req.body;
  
  if (!model) {
    return res.json({
      success: false,
      error: 'Aucun modèle spécifié'
    });
  }
  
  // Vérifier si le modèle est actuellement sélectionné
  if (global.selectedOllamaModel === model) {
    return res.json({
      success: false,
      error: 'Impossible de supprimer le modèle actuellement utilisé'
    });
  }
  
  // Exécuter la commande Ollama pour supprimer le modèle
  console.log(`Suppression du modèle: ${model}`);
  
  const command = `ollama rm ${model}`;
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Erreur de suppression: ${error.message}`);
      return res.json({
        success: false,
        error: error.message
      });
    }
    
    if (stderr) {
      console.error(`Stderr: ${stderr}`);
      // On continue car stderr peut contenir des infos
    }
    
    console.log(`Suppression réussie`);
    res.json({
      success: true,
      model,
      message: 'Modèle supprimé avec succès'
    });
  });
});

module.exports = router;
