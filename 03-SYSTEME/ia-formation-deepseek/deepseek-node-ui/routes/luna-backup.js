/**
 * Routes pour la sauvegarde Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Route pour la page de sauvegarde
router.get('/', (req, res) => {
  res.render('luna-backup', {
    title: 'Luna - Sauvegarde',
    page: 'backup'
  });
});

// API pour lister les sauvegardes
router.get('/api/list', (req, res) => {
  try {
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      return res.json({ success: true, backups: [] });
    }

    const files = fs.readdirSync(backupDir);
    const backups = files.filter(file => file.endsWith('.bak')).map(file => {
      const filePath = path.join(backupDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        created: stats.birthtime
      };
    });

    res.json({ success: true, backups });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour créer une sauvegarde
router.post('/api/create', (req, res) => {
  try {
    const { name, type } = req.body;
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const backupName = `${name || 'backup'}-${timestamp}.bak`;
    
    // Simulation de création de sauvegarde
    res.json({
      success: true,
      message: 'Sauvegarde créée avec succès',
      backup: backupName
    });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour restaurer une sauvegarde
router.post('/api/restore', (req, res) => {
  try {
    const { backupName } = req.body;
    
    // Simulation de restauration
    res.json({
      success: true,
      message: `Sauvegarde ${backupName} restaurée avec succès`
    });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Fonction d'initialisation
function initBackupRouter(options) {
  const { socketIo, mcp } = options;
  
  // Initialiser les gestionnaires de socket
  router.initSocketHandlers = function(io) {
    io.on('connection', (socket) => {
      socket.on('backup-create', (data) => {
        socket.emit('backup-progress', { progress: 33 });
        setTimeout(() => socket.emit('backup-progress', { progress: 66 }), 1000);
        setTimeout(() => socket.emit('backup-complete', { name: data.name }), 2000);
      });
    });
  };

  return router;
}

module.exports = { router, initBackupRouter };
