/**
 * Routes pour la sécurité Luna
 */

const express = require('express');
const router = express.Router();

// Route pour la page de sécurité
router.get('/', (req, res) => {
  res.render('luna-security', {
    title: 'Luna - Sécurité',
    page: 'security'
  });
});

// API pour obtenir le statut de sécurité
router.get('/api/status', (req, res) => {
  try {
    const securityStatus = {
      firewall: {
        status: 'active',
        blocked: Math.floor(Math.random() * 1000) + 500,
        allowed: Math.floor(Math.random() * 5000) + 2000
      },
      antivirus: {
        status: 'active',
        threats: Math.floor(Math.random() * 10),
        lastScan: new Date().toISOString()
      },
      encryption: {
        status: 'active',
        level: 'AES-256'
      },
      vpn: {
        status: 'active',
        location: 'Guadeloupe, FR'
      }
    };

    res.json({ success: true, security: securityStatus });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour scanner les menaces
router.post('/api/scan', (req, res) => {
  try {
    // Simulation d'un scan de sécurité
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Scan de sécurité terminé',
        threats: Math.floor(Math.random() * 5),
        scannedFiles: Math.floor(Math.random() * 10000) + 5000
      });
    }, 1000);
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Fonction d'initialisation
function initSecurityRouter(options) {
  const { socketIo, mcp } = options;
  
  // Initialiser les gestionnaires de socket
  router.initSocketHandlers = function(io) {
    io.on('connection', (socket) => {
      socket.on('security-scan', () => {
        socket.emit('scan-progress', { progress: 25 });
        setTimeout(() => socket.emit('scan-progress', { progress: 50 }), 1000);
        setTimeout(() => socket.emit('scan-progress', { progress: 75 }), 2000);
        setTimeout(() => socket.emit('scan-complete', { threats: 0 }), 3000);
      });
    });
  };

  return router;
}

module.exports = { router, initSecurityRouter };
