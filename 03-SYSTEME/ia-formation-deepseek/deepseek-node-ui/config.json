{"system": {"name": "Louna AI V3.0", "version": "3.0.0", "creator": "<PERSON><PERSON><PERSON>", "location": "Sainte-Anne, Guadeloupe", "mode": "LOCAL_ONLY"}, "ai": {"provider": "ollama", "model": "deepseek-r1:7b", "fallbackModel": "llama3.2:3b", "temperature": 0.7, "maxTokens": 4000, "timeout": 60000, "localOnly": true, "noApiKeys": true}, "ollama": {"url": "http://localhost:11434", "apiPath": "/api", "timeout": 60000, "retries": 3}, "memory": {"type": "thermal", "maxEntries": 10000, "temperatureRange": {"min": 36.0, "max": 42.0}, "zones": {"working": {"temperature": 37.0, "capacity": 1000}, "shortTerm": {"temperature": 37.5, "capacity": 5000}, "longTerm": {"temperature": 36.5, "capacity": 50000}}}, "kyber": {"enabled": true, "unlimited": true, "autoInstall": true, "maxAccelerators": "unlimited", "persistentMode": true}, "security": {"localOnly": true, "noExternalApis": true, "encryptMemory": false, "monitoring": true}, "evolution": {"enabled": true, "autoEvolution": true, "safeMode": true, "maxEvolutions": "unlimited"}, "interface": {"port": 3001, "host": "localhost", "cors": true, "websockets": true}, "logging": {"level": "info", "console": true, "file": false}}