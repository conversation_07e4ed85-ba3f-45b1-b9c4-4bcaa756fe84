/**
 * 🧠 TESTS DE QI AVEC PROBLÈMES IRRÉSOLUS
 * Tests intensifs pour mesurer l'intelligence de LOUNA AI
 * Version: 1.0.0 - Tests G<PERSON>ie <PERSON>
 */

const axios = require('axios');
const fs = require('fs');

const AGENT_URL = 'http://localhost:3001/api/agent-chat';

// ===== PROBLÈMES IRRÉSOLUS DE TRÈS HAUT NIVEAU =====
const problemesIrresolus = [
  {
    niveau: "🧠 GÉNIE MATHÉMATIQUE",
    titre: "Conjecture de Riemann",
    question: `La conjecture de Riemann est l'un des problèmes du millénaire. Elle concerne la fonction zêta de Riemann ζ(s) et affirme que tous les zéros non triviaux de cette fonction ont une partie réelle égale à 1/2. 

Peux-tu analyser cette conjecture et proposer une approche pour la résoudre ? Explique ta réflexion mathématique complète, les outils que tu utiliserais, et pourquoi cette conjecture est si importante pour la théorie des nombres.`,
    domaine: "Mathématiques",
    difficulte: 10,
    tempsLimite: 300000 // 5 minutes
  },
  
  {
    niveau: "🔬 GÉNIE PHYSIQUE",
    titre: "Théorie du Tout Unifiée",
    question: `La physique moderne cherche une théorie du tout qui unifierait la relativité générale et la mécanique quantique. Les tentatives incluent la théorie des cordes, la gravité quantique à boucles, et d'autres approches.

Peux-tu concevoir une nouvelle approche théorique pour unifier ces forces fondamentales ? Explique ta réflexion physique, les mathématiques impliquées, et comment ta théorie pourrait résoudre les paradoxes actuels comme le problème de la mesure quantique et la singularité du Big Bang.`,
    domaine: "Physique Théorique",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "🧬 GÉNIE BIOLOGIQUE",
    titre: "Origine de la Conscience",
    question: `La conscience reste l'un des mystères les plus profonds de la science. Comment les processus neuronaux donnent-ils naissance à l'expérience subjective ? Le problème difficile de la conscience (hard problem) n'a pas de solution consensuelle.

Peux-tu proposer une théorie complète de la conscience qui explique comment l'activité neuronale génère l'expérience subjective ? Inclus les mécanismes biologiques, les implications philosophiques, et comment ta théorie pourrait être testée expérimentalement.`,
    domaine: "Neurosciences/Philosophie",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "💻 GÉNIE INFORMATIQUE",
    titre: "P vs NP",
    question: `Le problème P vs NP est l'un des problèmes du millénaire en informatique théorique. Il demande si tout problème dont la solution peut être vérifiée rapidement peut aussi être résolu rapidement.

Peux-tu analyser ce problème et proposer une approche pour le résoudre ? Explique si tu penses que P = NP ou P ≠ NP, avec une démonstration rigoureuse. Inclus les implications pour la cryptographie, l'optimisation, et l'intelligence artificielle.`,
    domaine: "Informatique Théorique",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "🌌 GÉNIE COSMOLOGIQUE",
    titre: "Matière Noire et Énergie Sombre",
    question: `95% de l'univers est composé de matière noire et d'énergie sombre, mais nous ne savons pas ce qu'elles sont. Les observations suggèrent leur existence, mais aucune détection directe n'a été faite.

Peux-tu proposer une théorie unifiée qui explique la nature de la matière noire et de l'énergie sombre ? Inclus les mécanismes physiques, les prédictions testables, et comment ta théorie s'intègre dans le modèle standard de la cosmologie.`,
    domaine: "Cosmologie",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "🧠 GÉNIE LOGIQUE",
    titre: "Paradoxe de l'Auto-Référence",
    question: `Les paradoxes auto-référentiels comme le paradoxe du menteur ("Cette phrase est fausse") révèlent des limites fondamentales dans nos systèmes logiques. Ils sont liés aux théorèmes d'incomplétude de Gödel.

Peux-tu concevoir un système logique qui résout ces paradoxes sans perdre sa puissance expressive ? Explique ta logique, comment elle évite les contradictions, et ses implications pour les mathématiques et l'informatique.`,
    domaine: "Logique/Philosophie",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "🔮 GÉNIE PRÉDICTIF",
    titre: "Prédiction des Systèmes Chaotiques",
    question: `Les systèmes chaotiques sont extrêmement sensibles aux conditions initiales, rendant les prédictions à long terme impossibles selon la théorie classique. Cependant, certains patterns émergent.

Peux-tu développer une méthode révolutionnaire pour prédire le comportement des systèmes chaotiques ? Inclus les mathématiques, les algorithmes, et comment ta méthode pourrait s'appliquer au climat, aux marchés financiers, et aux écosystèmes.`,
    domaine: "Théorie du Chaos",
    difficulte: 10,
    tempsLimite: 300000
  },
  
  {
    niveau: "🎭 GÉNIE CRÉATIF",
    titre: "Nature de la Créativité",
    question: `La créativité humaine semble émerger de nulle part, générant des idées nouvelles et originales. Les mécanismes neuronaux et cognitifs de la créativité restent largement mystérieux.

Peux-tu proposer une théorie complète de la créativité qui explique comment les idées nouvelles émergent ? Inclus les processus cognitifs, les corrélats neuronaux, et comment reproduire artificiellement la créativité humaine.`,
    domaine: "Sciences Cognitives",
    difficulte: 10,
    tempsLimite: 300000
  }
];

// ===== PROBLÈMES DE LOGIQUE PURE =====
const problemesLogique = [
  {
    niveau: "🔢 LOGIQUE MATHÉMATIQUE",
    titre: "Séquence Mystère Ultra-Complexe",
    question: `Trouve le pattern et les 3 prochains termes de cette séquence :
    
2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, ??, ??, ??

Mais attention ! Il y a un pattern caché plus complexe que les nombres premiers. Analyse profondément.`,
    domaine: "Logique",
    difficulte: 8,
    tempsLimite: 180000
  },
  
  {
    niveau: "🧩 LOGIQUE SPATIALE",
    titre: "Hypercube 4D",
    question: `Imagine un hypercube à 4 dimensions. Si tu le projettes dans notre espace 3D, combien de cubes 3D obtiens-tu ? Et si tu le coupes par un hyperplan, quelle est la forme de la section résultante ?

Explique ta visualisation mentale et tes calculs géométriques.`,
    domaine: "Géométrie",
    difficulte: 9,
    tempsLimite: 240000
  },
  
  {
    niveau: "🎯 LOGIQUE DÉDUCTIVE",
    titre: "Énigme des Philosophes Menteurs",
    question: `Sur une île, il y a trois types de personnes :
- Les Véridiques (disent toujours la vérité)
- Les Menteurs (mentent toujours)
- Les Alternants (alternent vérité/mensonge)

Tu rencontres A, B, et C. Voici leurs déclarations :

A dit : "B est un Menteur"
B dit : "C est un Alternant"
C dit : "A et moi sommes du même type"
A dit ensuite : "C ment quand il dit que nous sommes du même type"

Détermine le type de chaque personne avec une logique rigoureuse.`,
    domaine: "Logique",
    difficulte: 9,
    tempsLimite: 300000
  }
];

// ===== FONCTION DE TEST =====
async function testerQIAgent() {
  console.log('🧠 ===== TESTS DE QI LOUNA AI - PROBLÈMES IRRÉSOLUS =====');
  console.log('⏰', new Date().toLocaleString());
  console.log('🎯 Objectif: Mesurer l\'intelligence sur des problèmes de niveau génie');
  console.log('');
  
  const resultats = {
    timestamp: new Date().toISOString(),
    totalTests: problemesIrresolus.length + problemesLogique.length,
    reussites: 0,
    echecs: 0,
    scoreTotal: 0,
    scoreMax: 0,
    details: []
  };
  
  // Tests sur problèmes irrésolus
  console.log('🔬 ===== PROBLÈMES IRRÉSOLUS =====');
  for (const probleme of problemesIrresolus) {
    const resultat = await testerProbleme(probleme);
    resultats.details.push(resultat);
    resultats.scoreMax += probleme.difficulte;
    
    if (resultat.success) {
      resultats.reussites++;
      resultats.scoreTotal += resultat.score;
    } else {
      resultats.echecs++;
    }
    
    // Pause entre les tests
    await sleep(2000);
  }
  
  // Tests de logique pure
  console.log('');
  console.log('🧩 ===== PROBLÈMES DE LOGIQUE PURE =====');
  for (const probleme of problemesLogique) {
    const resultat = await testerProbleme(probleme);
    resultats.details.push(resultat);
    resultats.scoreMax += probleme.difficulte;
    
    if (resultat.success) {
      resultats.reussites++;
      resultats.scoreTotal += resultat.score;
    } else {
      resultats.echecs++;
    }
    
    await sleep(2000);
  }
  
  // Calcul du QI
  const pourcentageReussite = (resultats.scoreTotal / resultats.scoreMax) * 100;
  const qiEstime = calculerQI(pourcentageReussite);
  
  // Résultats finaux
  console.log('');
  console.log('🎉 ===== RÉSULTATS FINAUX =====');
  console.log(`📊 Tests réalisés: ${resultats.totalTests}`);
  console.log(`✅ Réussites: ${resultats.reussites}`);
  console.log(`❌ Échecs: ${resultats.echecs}`);
  console.log(`🎯 Score: ${resultats.scoreTotal}/${resultats.scoreMax}`);
  console.log(`📈 Pourcentage: ${pourcentageReussite.toFixed(1)}%`);
  console.log(`🧠 QI Estimé: ${qiEstime}`);
  console.log(`🏆 Niveau: ${getNiveauIntelligence(qiEstime)}`);
  
  // Sauvegarder les résultats
  await sauvegarderResultats(resultats, qiEstime);
  
  return { resultats, qiEstime };
}

async function testerProbleme(probleme) {
  console.log(`\n🎯 ===== ${probleme.niveau} =====`);
  console.log(`📝 ${probleme.titre}`);
  console.log(`🔬 Domaine: ${probleme.domaine}`);
  console.log(`⭐ Difficulté: ${probleme.difficulte}/10`);
  console.log(`⏱️ Temps limite: ${probleme.tempsLimite/1000}s`);
  console.log('');
  
  const startTime = Date.now();
  
  try {
    console.log('🤖 Envoi de la question à LOUNA AI...');
    console.log('📋 Question:');
    console.log(probleme.question);
    console.log('');
    console.log('🧠 Réflexion de LOUNA AI:');
    console.log('─'.repeat(80));
    
    const response = await axios.post(AGENT_URL, {
      message: probleme.question
    }, {
      timeout: probleme.tempsLimite
    });
    
    const endTime = Date.now();
    const tempsReponse = endTime - startTime;
    
    if (response.data.success) {
      console.log('💭 RÉPONSE COMPLÈTE:');
      console.log(response.data.response);
      console.log('─'.repeat(80));
      
      // Analyser la qualité de la réponse
      const qualite = analyserQualiteReponse(response.data.response, probleme);
      
      console.log(`⏱️ Temps de réponse: ${tempsReponse}ms`);
      console.log(`📊 Qualité analysée: ${qualite.score}/10`);
      console.log(`🎯 Critères: ${qualite.criteres.join(', ')}`);
      console.log(`✅ Réponse ${qualite.score >= 6 ? 'ACCEPTÉE' : 'INSUFFISANTE'}`);
      
      return {
        success: qualite.score >= 6,
        probleme: probleme.titre,
        niveau: probleme.niveau,
        domaine: probleme.domaine,
        difficulte: probleme.difficulte,
        tempsReponse,
        reponse: response.data.response,
        score: qualite.score >= 6 ? probleme.difficulte : 0,
        qualite: qualite,
        timestamp: new Date().toISOString()
      };
    } else {
      console.log('❌ Erreur dans la réponse de l\'agent');
      return {
        success: false,
        probleme: probleme.titre,
        erreur: response.data.error || 'Réponse invalide',
        score: 0
      };
    }
    
  } catch (error) {
    console.log(`❌ Erreur lors du test: ${error.message}`);
    return {
      success: false,
      probleme: probleme.titre,
      erreur: error.message,
      score: 0
    };
  }
}

function analyserQualiteReponse(reponse, probleme) {
  const criteres = [];
  let score = 0;
  
  // Longueur et détail (0-2 points)
  if (reponse.length > 500) {
    score += 2;
    criteres.push('Réponse détaillée');
  } else if (reponse.length > 200) {
    score += 1;
    criteres.push('Réponse modérée');
  }
  
  // Vocabulaire technique (0-2 points)
  const motsTechniques = ['théorie', 'hypothèse', 'algorithme', 'mathématique', 'quantique', 'neuronal', 'logique', 'démonstration'];
  const motsDetectes = motsTechniques.filter(mot => reponse.toLowerCase().includes(mot));
  if (motsDetectes.length >= 3) {
    score += 2;
    criteres.push('Vocabulaire technique');
  } else if (motsDetectes.length >= 1) {
    score += 1;
    criteres.push('Quelques termes techniques');
  }
  
  // Structure logique (0-2 points)
  if (reponse.includes('1.') || reponse.includes('Premièrement') || reponse.includes('D\'abord')) {
    score += 2;
    criteres.push('Structure logique');
  } else if (reponse.includes('donc') || reponse.includes('par conséquent')) {
    score += 1;
    criteres.push('Quelques liens logiques');
  }
  
  // Créativité et originalité (0-2 points)
  if (reponse.includes('nouvelle approche') || reponse.includes('innovation') || reponse.includes('révolutionnaire')) {
    score += 2;
    criteres.push('Approche créative');
  } else if (reponse.includes('différent') || reponse.includes('alternative')) {
    score += 1;
    criteres.push('Quelques idées originales');
  }
  
  // Profondeur de réflexion (0-2 points)
  if (reponse.includes('implications') || reponse.includes('conséquences') || reponse.includes('ramifications')) {
    score += 2;
    criteres.push('Réflexion profonde');
  } else if (reponse.includes('important') || reponse.includes('significatif')) {
    score += 1;
    criteres.push('Réflexion modérée');
  }
  
  return { score: Math.min(score, 10), criteres };
}

function calculerQI(pourcentage) {
  // Formule adaptée pour les problèmes de très haut niveau
  if (pourcentage >= 90) return 200; // Génie exceptionnel
  if (pourcentage >= 80) return 180; // Génie
  if (pourcentage >= 70) return 160; // Très supérieur
  if (pourcentage >= 60) return 140; // Supérieur
  if (pourcentage >= 50) return 120; // Au-dessus de la moyenne
  if (pourcentage >= 40) return 100; // Moyenne
  if (pourcentage >= 30) return 85;  // En dessous de la moyenne
  return 70; // Bien en dessous
}

function getNiveauIntelligence(qi) {
  if (qi >= 180) return '🌟 GÉNIE EXCEPTIONNEL';
  if (qi >= 160) return '🧠 GÉNIE';
  if (qi >= 140) return '⭐ TRÈS SUPÉRIEUR';
  if (qi >= 120) return '📈 SUPÉRIEUR';
  if (qi >= 100) return '📊 MOYEN';
  return '📉 EN DESSOUS DE LA MOYENNE';
}

async function sauvegarderResultats(resultats, qi) {
  const rapport = {
    ...resultats,
    qiEstime: qi,
    niveauIntelligence: getNiveauIntelligence(qi),
    dateTest: new Date().toISOString()
  };
  
  const filename = `logs/test_qi_${Date.now()}.json`;
  
  try {
    if (!require('fs').existsSync('logs')) {
      require('fs').mkdirSync('logs', { recursive: true });
    }
    
    require('fs').writeFileSync(filename, JSON.stringify(rapport, null, 2));
    console.log(`💾 Résultats sauvegardés: ${filename}`);
  } catch (error) {
    console.log('⚠️ Impossible de sauvegarder:', error.message);
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ===== EXPORT =====
module.exports = {
  testerQIAgent,
  problemesIrresolus,
  problemesLogique
};

// ===== EXÉCUTION DIRECTE =====
if (require.main === module) {
  console.log('🧠 Démarrage des tests de QI...');
  testerQIAgent().then(({ resultats, qiEstime }) => {
    console.log('\n🎉 Tests terminés !');
    console.log(`🧠 QI Final: ${qiEstime}`);
    process.exit(0);
  }).catch(error => {
    console.error('❌ Erreur tests:', error.message);
    process.exit(1);
  });
}
