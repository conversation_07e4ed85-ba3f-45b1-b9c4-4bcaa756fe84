🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
⚡ Accélérateur KYBER 8/16 activé
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-05-31T23:51:54.477Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-05-31T23:51:54.477Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:51:54.476Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-05-31T23:52:04.476Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-05-31T23:52:04.476Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:04.476Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Application avec agent: /presentation -> ../02-APPLICATIONS/presentation/interface-revolutionnaire.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Application avec agent: /thermal -> ../02-APPLICATIONS/memoire/thermal-memory-interface.html
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:52:14.477Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:52:14.477Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:14.477Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Application avec agent: /kyber -> ../02-APPLICATIONS/kyber/kyber-control-panel.html
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-05-31T23:52:24.478Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-05-31T23:52:24.478Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:24.478Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-05-31T23:52:34.479Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-05-31T23:52:34.479Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:34.479Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-05-31T23:52:44.479Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-05-31T23:52:44.479Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:44.479Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Application avec agent: /presentation -> ../02-APPLICATIONS/presentation/interface-revolutionnaire.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-05-31T23:52:54.480Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-05-31T23:52:54.480Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:52:54.480Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-05-31T23:53:04.481Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-05-31T23:53:04.481Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:04.481Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 83%' ],
  timestamp: '2025-05-31T23:53:14.482Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 83%' ],
    timestamp: '2025-05-31T23:53:14.482Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:14.482Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-05-31T23:53:24.483Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-05-31T23:53:24.483Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:24.482Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-05-31T23:53:34.484Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-05-31T23:53:34.484Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:34.484Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:53:44.485Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:53:44.485Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:44.485Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-05-31T23:53:54.485Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-05-31T23:53:54.485Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:53:54.485Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:54:04.486Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:54:04.486Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:04.486Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-05-31T23:54:14.486Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-05-31T23:54:14.486Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:14.486Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-05-31T23:54:24.486Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-05-31T23:54:24.486Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:24.486Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-05-31T23:54:34.486Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-05-31T23:54:34.486Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:34.486Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-05-31T23:54:44.486Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-05-31T23:54:44.486Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:44.486Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-05-31T23:54:54.487Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-05-31T23:54:54.487Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:54:54.487Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-05-31T23:55:04.487Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-05-31T23:55:04.487Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:04.487Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-05-31T23:55:14.488Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-05-31T23:55:14.488Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:14.487Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-05-31T23:55:24.489Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-05-31T23:55:24.489Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:24.488Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-05-31T23:55:34.490Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-05-31T23:55:34.490Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:34.489Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-05-31T23:55:44.491Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-05-31T23:55:44.491Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:44.491Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-05-31T23:55:54.491Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-05-31T23:55:54.491Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:55:54.491Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-05-31T23:56:04.491Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-05-31T23:56:04.491Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:04.491Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 94%' ],
  timestamp: '2025-05-31T23:56:14.492Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 94%' ],
    timestamp: '2025-05-31T23:56:14.492Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:14.491Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-05-31T23:56:24.493Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-05-31T23:56:24.493Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:24.493Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-05-31T23:56:34.494Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-05-31T23:56:34.494Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:34.494Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-05-31T23:56:44.496Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-05-31T23:56:44.496Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:44.496Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-05-31T23:56:54.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-05-31T23:56:54.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:56:54.497Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-05-31T23:57:04.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-05-31T23:57:04.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:04.497Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-05-31T23:57:14.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-05-31T23:57:14.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:14.497Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-05-31T23:57:24.498Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-05-31T23:57:24.498Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:24.497Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-05-31T23:57:34.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-05-31T23:57:34.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:34.497Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-05-31T23:57:44.496Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-05-31T23:57:44.496Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:44.496Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-05-31T23:57:54.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-05-31T23:57:54.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:57:54.497Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-05-31T23:58:04.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-05-31T23:58:04.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:04.497Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-05-31T23:58:14.497Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-05-31T23:58:14.497Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:14.497Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-05-31T23:58:24.501Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-05-31T23:58:24.501Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:24.501Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:58:34.502Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:58:34.502Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:34.502Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 94%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:58:44.502Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 94%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:58:44.502Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:44.502Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-05-31T23:58:54.503Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-05-31T23:58:54.503Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:58:54.503Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-05-31T23:59:04.504Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-05-31T23:59:04.504Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:04.504Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-05-31T23:59:14.504Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-05-31T23:59:14.504Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:14.504Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 96%' ],
  timestamp: '2025-05-31T23:59:24.505Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 96%' ],
    timestamp: '2025-05-31T23:59:24.505Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:24.504Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-05-31T23:59:34.505Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-05-31T23:59:34.505Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:34.504Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-05-31T23:59:44.504Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-05-31T23:59:44.504Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:44.504Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-05-31T23:59:54.504Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-05-31T23:59:54.504Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-05-31T23:59:54.504Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:00:04.509Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:00:04.509Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:04.509Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 94%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:00:14.510Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 94%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:00:14.510Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:14.510Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-06-01T00:00:24.510Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-06-01T00:00:24.510Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:24.510Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
Error: Cannot find module 'systeminformation'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Function._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:938:14
    at Layer.handle [as handle_request] (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/node_modules/express/lib/router/layer.js:95:5)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:00:34.510Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:00:34.510Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:34.510Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:00:44.510Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:00:44.510Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:44.510Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 94%' ],
  timestamp: '2025-06-01T00:00:54.511Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 94%' ],
    timestamp: '2025-06-01T00:00:54.511Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:00:54.511Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-06-01T00:01:04.513Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-06-01T00:01:04.513Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:01:04.512Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 93%' ],
  timestamp: '2025-06-01T00:01:14.514Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 93%' ],
    timestamp: '2025-06-01T00:01:14.514Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:01:14.514Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
