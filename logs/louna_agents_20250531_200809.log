⚠️ Agent Claude non disponible: Cannot find module '../03-SYSTEME/ia-principale-claude/claude-agent-connector'
Require stack:
- /Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js
🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
⚡ Accélérateur KYBER 8/16 activé
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:08:20.112Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:08:20.112Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:08:20.112Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:08:30.112Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:08:30.112Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:08:30.112Z',
  overallRisk: 0.8,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Application avec agent: /presentation -> ../02-APPLICATIONS/presentation/interface-revolutionnaire.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🔌 Client connecté: KAOYgMOUlVvbVSXhAAAB
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
✅ Vérification de sécurité OK: {
  timestamp: '2025-06-01T00:08:40.111Z',
  overallRisk: 0,
  alertCount: 0,
  emergencyMode: false,
  systemStable: true
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:08:50.112Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:08:50.112Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:08:50.112Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:09:00.112Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:09:00.112Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:00.112Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:09:10.111Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:09:10.111Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:10.111Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:09:20.111Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:09:20.111Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:20.111Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:09:30.112Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:09:30.112Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:30.112Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:09:40.113Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:09:40.113Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:40.113Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:09:50.114Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:09:50.114Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:09:50.114Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:10:00.115Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:10:00.115Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:00.114Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:10:10.115Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:10:10.115Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:10.115Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:10:20.116Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:10:20.116Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:20.116Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:10:30.117Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:10:30.117Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:30.117Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:10:40.116Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:10:40.116Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:40.116Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:10:50.117Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:10:50.117Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:10:50.117Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:11:00.117Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:11:00.117Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:00.117Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:11:10.117Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:11:10.117Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:10.117Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:11:20.118Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:11:20.118Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:20.118Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:11:30.122Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:11:30.122Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:30.122Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:11:40.123Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:11:40.123Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:40.123Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:11:50.124Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:11:50.124Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:11:50.124Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:12:00.125Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:12:00.125Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:00.125Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:12:10.125Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:12:10.125Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:10.125Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:12:20.125Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:12:20.125Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:20.125Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:12:30.125Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:12:30.125Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:30.125Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:12:40.126Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:12:40.126Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:40.126Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-06-01T00:12:50.127Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-06-01T00:12:50.127Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:12:50.127Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-06-01T00:13:00.126Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-06-01T00:13:00.126Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:00.126Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:13:10.127Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:13:10.127Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:10.127Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:13:20.128Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:13:20.128Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:20.128Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:13:30.128Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:13:30.128Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:30.128Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:13:40.127Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:13:40.127Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:40.127Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:13:50.128Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:13:50.128Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:13:50.128Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:14:00.129Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:14:00.129Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:14:00.129Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:14:10.130Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 93%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:14:10.130Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:14:10.130Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
