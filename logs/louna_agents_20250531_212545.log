🤖 ClaudeAgentConnector initialisé (MODE LOCAL)
📡 Modèle: claude-local-simulation
🔑 Mode: LOCAL - Aucune clé API requise
✅ Agent Claude connecté
🤖 Agent Formateur DeepSeek initialisé
📡 Modèle: deepseek-r1:7b (VRAI - 4.68 Go)
🎯 Rôle: Formateur et ami de l'agent principal
✅ Agent Formateur DeepSeek connecté
💾 Système de Sauvegarde Renforcé initialisé
📁 Répertoire: ./SAUVEGARDES-LOUNA-AI
⚡ Sauvegarde instantanée: ACTIVÉE
🔗 Intégration KYBER: ACTIVÉE
✅ Système de Sauvegarde Renforcé connecté
🛡️ Système de Sécurité Renforcé Louna AI initialisé
🔒 Mode: LOCAL UNIQUEMENT
🚫 Connexions externes: BLOQUÉES
✅ Système de Sécurité Renforcé connecté
🧬 Système d'Évolution Naturelle de l'Agent initialisé
🌱 Mode: Évolution autonome et naturelle
🎯 Objectif: Mise de côté du codage pour évolution pure
✅ Système d'Évolution Naturelle connecté
🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
🔌 Client connecté: fJkhv3QWjoTRmT5UAAAB
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
⚡ Accélérateur KYBER 8/16 activé
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🚀 Système de Sécurité Renforcé démarré
🛡️ Protection active contre toutes les vulnérabilités
👁️ Surveillance continue activée
🛡️ Système de Sécurité Renforcé démarré
🔒 Mode local uniquement - Connexions externes bloquées
🚫 Validation des entrées activée
🔐 Chiffrement des données sensibles activé
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent cognitif activé 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Utilisation du VRAI modèle deepseek-r1:7b (4.68 Go)
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
[SpeechProcessor] Synthèse vocale: "LOUNA activé. Système sensoriel et vocal initialisés." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T01:25:56.245Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T01:25:56.245Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T01:25:56.244Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
📁 Répertoires de sauvegarde créés
🚀 Système de Sauvegarde Renforcé démarré
🧠 MODE CERVEAU HUMAIN - Sauvegarde instantanée continue
🧠 Mode cerveau activé - Sauvegarde continue instantanée
⚡ Sauvegarde instantanée activée
🧠 Mode cerveau : Sauvegarde continue sans interruption
💾 Système de Sauvegarde Renforcé démarré
🧠 MODE CERVEAU HUMAIN - Sauvegarde instantanée continue
⚡ Sauvegarde toutes les 100ms (réactivité neuronale)
💾 Aucune perte de données possible
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Utilisation du VRAI modèle deepseek-r1:7b (4.68 Go)
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
🧠 Sauvegarde cerveau - Sécurité
💾 Sauvegarde générale complète effectuée
🧠 Sauvegarde cerveau - Sécurité
🧠 Sauvegarde cerveau - Sécurité
💾 Sauvegarde générale complète effectuée
🧠 Sauvegarde cerveau - Sécurité
🧠 Sauvegarde cerveau - Sécurité
💾 Sauvegarde générale complète effectuée
🧠 Sauvegarde cerveau - Sécurité
🧠 Sauvegarde cerveau - Sécurité
💾 Sauvegarde générale complète effectuée
🧠 Sauvegarde cerveau - Sécurité
🧠 Sauvegarde cerveau - Sécurité
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T01:26:06.244Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T01:26:06.244Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T01:26:06.244Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Mémoire thermique sauvegardée: memoire-thermique-2025-06-01T01-26-06-250Z.json
/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:2708
        if (kyberAccelerators && kyberAccelerators.length > 0) {
        ^

ReferenceError: kyberAccelerators is not defined
    at Timeout._onTimeout (/Volumes/T7/LOUNA-AI-PRODUCTION-OFFICIELLE/04-SERVEURS/louna-ai-vrais-agents.js:2708:9)

Node.js v23.11.0
