🤖 ClaudeAgentConnector initialisé
📡 Modèle: claude-3-sonnet-20240229
🔑 Clé API: NON CONFIGURÉE
✅ Agent Claude connecté
🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
⚡ Accélérateur KYBER 8/16 activé
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
🔌 Client connecté: oL-qJFD8HpgQVNEQAAAB
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:14:37.196Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:14:37.196Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:14:37.196Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 82%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:14:47.198Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 82%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:14:47.198Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:14:47.198Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Application avec agent: /presentation -> ../02-APPLICATIONS/presentation/interface-revolutionnaire.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🔌 Client connecté: hCwcKfl8WQhCqew_AAAD
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:14:57.197Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:14:57.197Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:14:57.197Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🔌 Client déconnecté: oL-qJFD8HpgQVNEQAAAB
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:15:07.197Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:15:07.197Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:07.197Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 82%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:15:17.198Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 82%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:15:17.198Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:17.198Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Application avec agent: /qi -> ../02-APPLICATIONS/intelligence/qi-test-simple.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:15:27.199Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:15:27.199Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:27.199Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Application avec agent: /presentation -> ../02-APPLICATIONS/presentation/interface-revolutionnaire.html
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🔌 Client connecté: QckshZwYgnR4_kQTAAAF
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:15:37.198Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:15:37.198Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:37.198Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:15:47.199Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:15:47.199Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:47.199Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Application avec agent: /face -> ../02-APPLICATIONS/web/face-recognition.html
🔌 Client déconnecté: QckshZwYgnR4_kQTAAAF
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🔌 Client connecté: OwLcdbWf1xbsRsa5AAAH
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:15:57.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:15:57.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:15:57.199Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Application avec agent: /search -> ../02-APPLICATIONS/web/web-search.html
🔌 Client déconnecté: OwLcdbWf1xbsRsa5AAAH
🔌 Client connecté: 5IXoBWXtbdl4qPBbAAAJ
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:16:07.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:16:07.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:07.200Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:16:17.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:16:17.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:17.200Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:16:27.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:16:27.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:27.199Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:16:37.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:16:37.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:37.200Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:16:47.200Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:16:47.200Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:47.200Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:16:57.201Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:16:57.201Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:16:57.201Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:17:07.202Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:17:07.202Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:07.202Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:17:17.203Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:17:17.203Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:17.203Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:17:27.205Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:17:27.205Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:27.205Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:17:37.206Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:17:37.206Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:37.205Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
💾 Mémoire thermique sauvegardée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:17:47.206Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:17:47.206Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:47.206Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:17:57.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:17:57.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:17:57.207Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:18:07.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:18:07.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:07.207Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:18:17.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:18:17.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:17.208Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:18:27.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:18:27.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:27.208Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-06-01T00:18:37.207Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-06-01T00:18:37.207Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:37.207Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:18:47.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:18:47.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:47.207Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:18:57.208Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:18:57.208Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:18:57.208Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:19:07.207Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:19:07.207Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:19:07.207Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
🌡️ Cycle thermique en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:19:17.207Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:19:17.207Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:19:17.207Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
