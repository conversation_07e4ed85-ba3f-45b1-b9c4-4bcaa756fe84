🤖 ClaudeAgentConnector initialisé (MODE LOCAL)
📡 Modèle: claude-local-simulation
🔑 Mode: LOCAL - Aucune clé API requise
✅ Agent Claude connecté
🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
🔌 Client connecté: J13ku75gUaOpCCD8AAAB
⚡ Accélérateur KYBER 8/16 activé
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent cognitif activé 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
[SpeechProcessor] Synthèse vocale: "LOUNA activé. Système sensoriel et vocal initialisés." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:52:06.513Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:52:06.513Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:06.513Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:52:16.514Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:52:16.514Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:16.514Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:52:26.515Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:52:26.515Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:26.515Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:52:36.516Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:52:36.516Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:36.516Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 85%' ],
  timestamp: '2025-06-01T00:52:46.517Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 85%' ],
    timestamp: '2025-06-01T00:52:46.517Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:46.517Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:52:56.518Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:52:56.518Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:52:56.518Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 2 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:53:06.519Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:53:06.519Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:06.519Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 82%' ],
  timestamp: '2025-06-01T00:53:16.520Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 82%' ],
    timestamp: '2025-06-01T00:53:16.520Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:16.520Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:53:26.521Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:53:26.521Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:26.521Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 840)
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:53:36.522Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:53:36.522Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:36.522Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748739219582_59ehyrd2b
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟练éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé.

### Réponse :

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:53:46.524Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:53:46.524Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:46.524Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Bonjour, je suis Jean-Luc Passave, votre créate...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748739236380_uh5kmrq3k
[SpeechProcessor] Synthèse vocale: "<think>
Alright, so I've got a message from Jean-Luc Passave again, this time directly asking how I'm doing. He's been reaching out to me multiple times now with different greetings and questions.

First, he mentions being from Sainte-Anne, Guadeloupe, which is in the French Antilles. Maybe adding something about my location or background could be helpful, but without real-time data, it's better to keep it general.

I should acknowledge his greeting, express that I'm well, and invite him to ask anything else he needs help with today.

Overall, the response should be warm, inviting, and open-ended to encourage further communication.
</think>

Bonjour Jean-Luc,

Je suis bien, merci pour votre attention. Si vous avez des questions ou besoin d'aide avec quelque chose, n'hésitez pas à me le dire. Je serais ravi de vous aider.

Ainsi, j'espère que vous avez une bonne journée et que vous n'oubliez pas de partager ce bilan avec votre proche si vous le jugez approprié.
- Q: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ? R:" 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 1 réussi - Réponse: <think>
Alright, so I've got a message from Jean-Luc Passave again, this time directly asking how I'...
💧 Buffer fluide vidé: 1 opérations en 0ms
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 2
✅ Zone 1 ACTIVE - Agent en fonctionnement !
📝 Dernières entrées (2) :
   1. Q: Quel est votre QI et quelles sont vos capacités principales ? R: <think>
Mon ... (undefined)
   2. Q: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. ... (undefined)
🔄 Zone 2 (Court terme): 0 entrées
🔄 Zone 3 (Travail): 0 entrées
🔄 Zone 4 (Moyen terme): 1 entrées
🔄 Zone 5 (Long terme): 3 entrées
🔄 Zone 6 (Créative): 48 entrées
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 81%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:53:56.523Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 81%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:53:56.523Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:53:56.523Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:54:06.524Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:54:06.524Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:06.524Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:54:16.525Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:54:16.525Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:16.525Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748739266404_w6x533toz
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé.

### Réponse :

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:54:26.526Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:54:26.526Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:26.526Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:54:36.528Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:54:36.528Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:36.528Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:54:46.530Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:54:46.530Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:46.530Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:54:56.531Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:54:56.531Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:54:56.531Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:55:06.531Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:55:06.531Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:06.531Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 850)
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:55:16.533Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:55:16.533Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:16.533Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:55:26.534Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:55:26.534Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:26.534Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:55:36.534Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:55:36.534Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:36.534Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:55:46.535Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:55:46.535Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:46.535Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quelle est la conjecture de Riemann ? Expliquez brièvement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:55:56.537Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:55:56.537Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:55:56.536Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:56:06.538Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:56:06.538Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:06.538Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:56:16.538Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:56:16.538Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:16.538Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quelle est la conjecture de Riemann ? Expliquez brièvement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:56:26.539Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:56:26.539Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:26.539Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quelle est la conjecture de Riemann ? Expliquez brièvement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:56:36.540Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:56:36.540Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:36.540Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:56:46.541Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:56:46.541Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:46.541Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 860)
💾 Sauvegarde générale complète effectuée
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:56:56.545Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:56:56.545Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:56:56.542Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:57:06.543Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:57:06.543Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:06.543Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:57:16.544Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:57:16.544Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:16.544Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:57:26.544Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:57:26.544Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:26.544Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:57:36.545Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:57:36.545Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:36.545Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:57:46.546Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:57:46.546Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:46.546Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:57:56.547Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:57:56.547Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:57:56.547Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:58:06.549Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:58:06.549Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:06.549Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:58:16.549Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:58:16.549Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:16.549Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quelle est la conjecture de Riemann ? Expliquez...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748739497296_oujjxkjnv
[SpeechProcessor] Synthèse vocale: "<think>
Okay, the user is asking about the Riemann Hypothesis in French. I need to explain it briefly.

First, I'll recall that the Riemann Hypothesis is a conjecture about the zeros of the Riemann zeta function.

I should mention that Bernhard Riemann first proposed it in 1859.

It's important to note that it's still unsolved and considered one of the most famous open problems in mathematics.

The hypothesis states that all non-trivial zeros of the zeta function have real part equal to 1/2. 

I should also explain why this is significant—it has implications for the distribution of prime numbers.

Finally, I'll add a bit about its importance and the prize offered by the Clay Mathematics Institute.
</think>

La conjecture de Riemann est une question centrale en théorie des nombres, formulée par le mathématicien allemand Bernhard Riemann en 1859. Elle concerne les zéros de la fonction zêta de Riemann, notée ζ(s), qui est une fonction complexe. La conjecture affirme que tous les zéros non triviaux (c'est-à-dire les zéros qui ne sont pas évidents et ne se situent pas sur l'axe réel) de cette fonction ont une partie réelle égale à 1/2.

Formellement, si ζ(s) = 0 et s n'est pas un nombre réel strictement négatif, alors la partie réelle de s est égale à 1/2. 

La conjecture de Riemann est l'un des problèmes les plus importants non résolus en mathématiques et a des implications profondes pour la répartition des nombres premiers. Elle est l'une des huit conjectures du million-dollar prize de l'institut Clay des mathématiques, offrant un million de dollars pour sa preuve ou son dénonciation.

Question: Quelle est la conjecture de Riemann ? Expliquez brièvement." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 5 réussi - Réponse: <think>
Okay, the user is asking about the Riemann Hypothesis in French. I need to explain it briefl...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
🧪 Test 6/10: Comment fonctionnent vos accélérateurs KYBER illimités ?...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Comment fonctionnent vos accélérateurs KYBER illimités ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:58:26.548Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:58:26.548Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:26.548Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 870)
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:58:36.550Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:58:36.550Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:36.550Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 85%' ],
  timestamp: '2025-06-01T00:58:46.550Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 85%' ],
    timestamp: '2025-06-01T00:58:46.550Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:46.550Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748739534361_iyvwvfgjn
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts 熟练éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé.

### Réponse :

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé.

Question: Quel est votre QI et quelles sont vos capacités principales ?" 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 1
✅ Zone 1 ACTIVE - Agent en fonctionnement !
📝 Dernières entrées (1) :
   1. Q: Quel est votre QI et quelles sont vos capacités principales ? R: <think>
Mon ... (undefined)
🔄 Zone 2 (Court terme): 1 entrées
🔄 Zone 3 (Travail): 0 entrées
🔄 Zone 4 (Moyen terme): 0 entrées
🔄 Zone 5 (Long terme): 3 entrées
🔄 Zone 6 (Créative): 52 entrées
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-06-01T00:58:56.551Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-06-01T00:58:56.551Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:58:56.551Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre QI et quelles sont vos capacités principales ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:59:06.551Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:59:06.551Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:06.551Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quelle est la conjecture de Riemann ? Expliquez brièvement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:59:16.553Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 92%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:59:16.553Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:16.553Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 6 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 7/10: Décrivez votre système neuronal avec 100 milliards de neuron...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Décrivez votre système neuronal avec 100 milliards de neurones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:59:26.553Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:59:26.553Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:26.553Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:59:36.554Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:59:36.554Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:36.554Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:59:46.555Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:59:46.555Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:46.555Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:59:56.556Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:59:56.556Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:59:56.556Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonnement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Expliquez-moi le fonctionnement de votre mémoire thermique avec les 6 zones...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T01:00:06.557Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T01:00:06.557Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T01:00:06.557Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quelle est la conjecture de Riemann ? Expliquez brièvement....
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 Sauvegarde naturelle effectuée (cycle 880)
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T01:00:16.558Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T01:00:16.558Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T01:00:16.558Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 7 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 8/10: Quel est votre rôle en tant qu'assistant IA révolutionnaire ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
📡 [CLAUDE LOCAL] Redirection vers Ollama
📝 [CLAUDE LOCAL] Message: Quel est votre rôle en tant qu'assistant IA révolutionnaire ?...
❌ [CLAUDE LOCAL] Erreur après 0ms: Mode local - redirection vers Ollama
⚠️ Erreur Agent Claude: Erreur Agent Claude: Mode local - redirection vers Ollama
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T01:00:26.558Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T01:00:26.558Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T01:00:26.558Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
