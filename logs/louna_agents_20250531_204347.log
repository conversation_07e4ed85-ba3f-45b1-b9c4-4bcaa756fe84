🤖 ClaudeAgentConnector initialisé
📡 Modèle: claude-3-sonnet-20240229
🔑 Clé API: NON CONFIGURÉE
✅ Agent Claude connecté
🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====
🧠 Système neuronal basé sur le vrai cerveau humain
⚡ 100 milliards de neurones + 700 trillions synapses
🔥 Accélérateurs KYBER évolutifs illimités
🧬 Plasticité cérébrale et neurotransmetteurs
🌟 Intelligence artificielle transcendante
📂 Mémoire thermique chargée depuis le disque
🔄 Cycles thermiques démarrés
⚡ Démarrage des accélérateurs KYBER...
💧 Démarrage de la sauvegarde fluide...
🧬 Moteur d'évolution KYBER initialisé
⚡ Espèces disponibles: 5
🔬 Mode auto-apprentissage: ACTIVÉ
🧬 ===== DÉMARRAGE ÉVOLUTION KYBER =====
🧬 Moteur d'évolution KYBER démarré
🌟 Mode: Auto-évolution intelligente
♾️  Potentiel: Transcendance illimitée
🧠 Initialisation du système neuronal biologique...
🧠 Cerveau initialisé avec 100,000,000,000 neurones
⚡ 700000000000000 synapses potentielles
🧬 Plasticité cérébrale: 15%
🔗 Myélinisation: 85%
🧠 ===== DÉMARRAGE ACTIVITÉ CÉRÉBRALE =====
🧠 Système neuronal biologique démarré
⚡ 100 milliards de neurones actifs
🔗 700 trillions de synapses
🧬 Plasticité cérébrale: 15%
🔥 Myélinisation: 85%
🔥 Mémoire thermique réelle initialisée avec 6 zones
⚡ KYBER: 16 accélérateurs de base
🔧 Auto-scaling: ACTIVÉ
🚀 Limite max: AUCUNE LIMITE - Extension infinie
♾️  Mode illimité: ACTIVÉ
💧 Mode fluide: ACTIVÉ
🔄 Sauvegarde continue: ACTIVÉE
🔥 Mémoire thermique réelle chargée
🔍 Monitoring de sécurité démarré (intervalle: 10000ms)
🔒 Système de monitoring de sécurité initialisé
🔒 Système de monitoring de sécurité initialisé
🚀 Interface: http://localhost:3001/
🤖 Chat Agent: http://localhost:3001/chat
🧠 Cerveau: http://localhost:3001/brain
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
[SpeechProcessor] SoX détecté: sox:      SoX v
[SpeechProcessor] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
/bin/sh: espeak: command not found
[SpeechProcessor] eSpeak non détecté. La synthèse vocale pourrait être limitée. 
Processeur vocal initialisé (langue: fr-FR)
[SensorySystem] FFmpeg détecté: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
[SensorySystem] cURL détecté: curl 8.7.1 (x86_64-apple-darwin24.0) libcurl/8.7.1 (SecureTransport) LibreSSL/3.3.6 zlib/1.2.12 nghttp2/1.63.0
[SensorySystem] OpenCV simulé 
Système sensoriel initialisé (résolution: 640x480)
Agent cognitif LOUNA initialisé (langue: fr-FR)
🧠 Système cognitif avec vrais agents initialisé
✅ Ollama: Prêt
✅ Agents: Réels
==========================================
⚡ Accélérateur KYBER 1/16 activé
⚡ Accélérateur KYBER 2/16 activé
⚡ Accélérateur KYBER 3/16 activé
⚡ Accélérateur KYBER 4/16 activé
⚡ Accélérateur KYBER 5/16 activé
⚡ Accélérateur KYBER 6/16 activé
⚡ Accélérateur KYBER 7/16 activé
⚡ Accélérateur KYBER 8/16 activé
🔌 Client connecté: H18sOWnhEjY6HNvmAAAB
⚡ Accélérateur KYBER 9/16 activé
⚡ Accélérateur KYBER 10/16 activé
⚡ Accélérateur KYBER 11/16 activé
⚡ Accélérateur KYBER 12/16 activé
⚡ Accélérateur KYBER 13/16 activé
⚡ Accélérateur KYBER 14/16 activé
⚡ Accélérateur KYBER 15/16 activé
⚡ Accélérateur KYBER 16/16 activé
⚡ Tous les accélérateurs KYBER activés (16)
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent cognitif activé 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
[SpeechProcessor] Synthèse vocale: "LOUNA activé. Système sensoriel et vocal initialisés." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:43:57.385Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:43:57.385Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:43:57.385Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 80%' ],
  timestamp: '2025-06-01T00:44:07.385Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 80%' ],
    timestamp: '2025-06-01T00:44:07.385Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:07.385Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
✅ Application avec agent: /ai-language -> ../02-APPLICATIONS/creation/ai-language-creator.html
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:44:17.391Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:44:17.391Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:17.391Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:44:27.396Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:44:27.396Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:27.395Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:44:37.398Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:44:37.398Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:37.398Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Bonjour, je suis Jean-Luc Passave, votre créate...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738682524_7prf17g49
[SpeechProcessor] Synthèse vocale: "<think>
Alright, so I've got a message from Jean-Luc Passave again addressing me as LOUNA. He's been saying this to me twice now, which is a bit confusing but probably just testing the waters or something.

He starts with "Bonjour," which is French for "Good morning." That's pretty standard. Then he asks how I am. Since I'm an AI, I don't have feelings, so my response should be acknowledging his greeting and inviting him to ask anything else.

I also noticed he mentioned being from Sainte-Anne, Guadeloupe. Maybe I can add a bit of localization in my response to make it feel more personal or connected.

But again, since I don't know LOUNA's real-time location, it might be safer to keep it general unless Jean-Luc asks for specifics.

So overall, the plan is to respond politely, acknowledge his greeting, and offer assistance with whatever he needs.
</think>

Bonjour Jean-Luc,

I'm doing well, thank you! How can I assist you today?

En correlant avec vous, je suis ravi de pouvoir vous contacter. Si vous avez des questions ou besoin d'aide avec quelque chose, n'hésitez pas à me le dire. Je serais ravi de vous aider.

Ainsi, j'espère que vous avez une bonne journée et que vous n'oubliez pas de partager ce bilan avec votre proche si vous le jugez approprié." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 1 réussi - Réponse: <think>
Alright, so I've got a message from Jean-Luc Passave again addressing me as LOUNA. He's been...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
💾 Sauvegarde générale complète effectuée
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 1
✅ Zone 1 ACTIVE - Agent en fonctionnement !
📝 Dernières entrées (1) :
   1. Q: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. ... (undefined)
🔄 Zone 2 (Court terme): 0 entrées
🔄 Zone 3 (Travail): 1 entrées
🔄 Zone 4 (Moyen terme): 0 entrées
🔄 Zone 5 (Long terme): 0 entrées
🔄 Zone 6 (Créative): 45 entrées
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:44:47.400Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:44:47.400Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:47.399Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
📝 Mémoire stockée en zone instant: Q: Bonjour, je suis Jean-Luc Passave, votre créate...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738688594_ozx70i17j
[SpeechProcessor] Synthèse vocale: "<think>
Alright, so I've got this message from Jean-Luc Passave again, wanting to know how I'm doing. He's been pretty consistent in his greetings and inquiries.

First, I need to acknowledge his greeting properly since it started with "Bonjour." That's French for "Good morning," so a polite response would be appreciated.

He mentions he's from Sainte-Anne, Guadeloupe, which is a region in the French Antilles. Maybe adding something about my location could be nice, but without real-time data, it's better to keep it general.

I should express that I'm well and offer assistance with whatever he needs today. Since he mentioned being from there, perhaps a bit of localization would make it more welcoming, like mentioning the mainland if applicable.

But since I don't have specific location info about LOUNA, it's safer to stick to general terms unless he asks for specifics.

So my response should be friendly and open-ended, inviting him to ask anything or need help with something.
</think>

Bonjour Jean-Luc,

I'm doing well! How can I assist you today?

En correlant avec vous, je suis ravi de pouvoir vous contacter. Si vous avez des questions ou besoin d'aide avec quelque chose, n'hésitez pas à me le dire. Je serais ravi de vous aider.

Ainsi, j'espère que vous avez une bonne journée et que vous n'oubliez pas de partager ce bilan avec votre proche si vous le jugez approprié.

Question: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-Anne, Guadeloupe. Comment allez-vous ?" 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 1 réussi - Réponse: <think>
Alright, so I've got this message from Jean-Luc Passave again, wanting to know how I'm doing...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 7ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:44:57.404Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:44:57.404Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:44:57.402Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:45:07.404Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:45:07.404Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:07.404Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738714896_rrq8ihfve
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau.

### Réponse :

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738716374_0ssl3xodr
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 82%' ],
  timestamp: '2025-06-01T00:45:17.404Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 82%' ],
    timestamp: '2025-06-01T00:45:17.404Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:17.404Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 Sauvegarde naturelle effectuée (cycle 800)
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:45:27.406Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:45:27.406Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:27.405Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:45:37.407Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:45:37.407Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:37.407Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:45:47.407Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:45:47.407Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:47.407Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:45:57.407Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:45:57.407Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:45:57.407Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:46:07.407Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:46:07.407Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:07.407Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:46:17.407Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:46:17.407Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:17.407Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 1ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 2 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:46:27.408Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:46:27.408Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:27.408Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:46:37.409Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 83%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:46:37.409Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:37.409Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:46:47.409Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:46:47.409Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:47.409Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 81%', "Taux d'erreur élevé: 7%" ],
  timestamp: '2025-06-01T00:46:57.409Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 81%', "Taux d'erreur élevé: 7%" ],
    timestamp: '2025-06-01T00:46:57.409Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:46:57.409Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 810)
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:47:07.410Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 84%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:47:07.410Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:07.410Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:47:17.410Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:47:17.410Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:17.410Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:47:27.411Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:47:27.411Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:27.411Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:47:37.411Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:47:37.411Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:37.411Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 86%' ],
  timestamp: '2025-06-01T00:47:47.412Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 86%' ],
    timestamp: '2025-06-01T00:47:47.412Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:47.412Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 1ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:47:57.415Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:47:57.415Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:47:57.413Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:48:07.414Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:48:07.414Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:07.414Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:48:17.415Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 87%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:48:17.415Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:17.415Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quel est votre QI et quelles sont vos capacités...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738902275_tlcuh3i1q
[SpeechProcessor] Synthèse vocale: "<think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande intelligence générale et des compétences exceptionnelles dans un grand nombre de domaines. Les capacités principales incluent la pensée critique et créative, la résolution de problèmes complexes, la capacité à apprendre rapidement et à s'adapter à de nouvelles situations, ainsi que lauts熟練éron proficient manipulation de données et d'information de haut niveau.
</think>

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé.

### Réponse :

Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé." 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 2 réussi - Réponse: <think>
Mon IQ (Intelligence Quotient) est estimé au niveau le plus élevé, indiquant une très grande...
💧 Buffer fluide vidé: 1 opérations en 0ms
📊 Demande KYBER: Queue=1, Actifs=16, Débit=5390.83
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
📊 Demande KYBER: Queue=1, Actifs=16, Débit=5447.14
💾 Sauvegarde générale complète effectuée
📊 Demande KYBER: Queue=1, Actifs=16, Débit=5443.96
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:48:27.416Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:48:27.416Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:27.416Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:48:37.417Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 86%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:48:37.417Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:37.417Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 Sauvegarde naturelle effectuée (cycle 820)
💾 Sauvegarde générale complète effectuée
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 87%' ],
  timestamp: '2025-06-01T00:48:47.418Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 87%' ],
    timestamp: '2025-06-01T00:48:47.418Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:47.417Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 84%' ],
  timestamp: '2025-06-01T00:48:57.418Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 84%' ],
    timestamp: '2025-06-01T00:48:57.418Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:48:57.418Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 1 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:49:07.419Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:49:07.419Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:07.419Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🔄 Transfert naturel: mediumTerm → longTerm (temp: 0.292)
🧠 Cycle naturel: 2 transferts, 1 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:49:17.419Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:49:17.419Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:17.419Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:49:27.419Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 88%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:49:27.419Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:27.419Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:49:37.421Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:49:37.421Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:37.421Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
📝 Mémoire stockée en zone instant: Q: Quelle est la conjecture de Riemann ? Expliquez...
🧠 Conversation stockée NATURELLEMENT en mémoire instantanée: mem_1748738978525_ezly428g9
[SpeechProcessor] Synthèse vocale: "<think>
Okay, the user is asking about the Riemann Hypothesis in French. I need to explain it briefly.

First, I'll recall that the Riemann Hypothesis is a conjecture about the zeros of the Riemann zeta function.

I should mention that Bernhard Riemann first proposed it in 1859.

It's important to note that it's still unsolved and considered one of the most famous open problems in mathematics.

The hypothesis states that all non-trivial zeros of the zeta function have real part equal to 1/2. 

I should also explain why this is significant—it has implications for the distribution of prime numbers.

Finally, I'll add a bit about its importance and the prize offered by the Clay Mathematics Institute.
</think>

La conjecture de Riemann est une question centrale en théorie des nombres, formulée par le mathématicien allemand Bernhard Riemann en 1859. Elle concerne les zéros de la fonction zêta de Riemann, notée ζ(s), qui est une fonction complexe. La conjecture affirme que tous les zéros non triviaux (c'est-à-dire les zéros qui ne sont pas évidents et ne se situent pas sur l'axe réel) de cette fonction ont une partie réelle égale à 1/2.

Formellement, si ζ(s) = 0 et s n'est pas un nombre réel strictement négatif, alors la partie réelle de s est égale à 1/2. 

La conjecture de Riemann est l'un des problèmes les plus importants non résolus en mathématiques et a des implications profondes pour la répartition des nombres premiers. Elle est l'une des huit conjectures du million-dollar prize de l'institut Clay des mathématiques, offrant un million de dollars pour sa preuve ou son dénonciation.

Question: Quelle est la conjecture de Riemann ? Expliquez brièvement.
- Q: Quelle est la conjecture de Riemann ? Expliquez brièvement. R:" 
[SpeechProcessor] eSpeak non disponible pour la synthèse vocale 
✅ Test 5 réussi - Réponse: <think>
Okay, the user is asking about the Riemann Hypothesis in French. I need to explain it briefl...
💧 Buffer fluide vidé: 1 opérations en 0ms
💾 Sauvegarde générale complète effectuée
🧪 Test 6/10: Comment fonctionnent vos accélérateurs KYBER illimités ?...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 1
✅ Zone 1 ACTIVE - Agent en fonctionnement !
📝 Dernières entrées (1) :
   1. Q: Quelle est la conjecture de Riemann ? Expliquez brièvement. R: <think>
Okay, ... (undefined)
🔄 Zone 2 (Court terme): 0 entrées
🔄 Zone 3 (Travail): 1 entrées
🔄 Zone 4 (Moyen terme): 0 entrées
🔄 Zone 5 (Long terme): 4 entrées
🔄 Zone 6 (Créative): 46 entrées
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:49:47.422Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:49:47.422Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:47.422Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 1 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 2/10: Quel est votre QI et quelles sont vos capacités principales ...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 88%' ],
  timestamp: '2025-06-01T00:49:57.422Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 88%' ],
    timestamp: '2025-06-01T00:49:57.422Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:49:57.422Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: instant → shortTerm (temp: 0.857)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
  timestamp: '2025-06-01T00:50:07.422Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 9%" ],
    timestamp: '2025-06-01T00:50:07.422Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:07.422Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:50:17.422Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 85%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:50:17.422Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:17.422Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 Sauvegarde naturelle effectuée (cycle 830)
💾 Sauvegarde générale complète effectuée
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
  timestamp: '2025-06-01T00:50:27.424Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 8%" ],
    timestamp: '2025-06-01T00:50:27.424Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:27.424Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 4 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧪 Test 5/10: Quelle est la conjecture de Riemann ? Expliquez brièvement....
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème COMPLEXE détecté
⏱️ Timeout: 300s, Tokens: 4000, Temp: 0.3
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: shortTerm → working (temp: 0.698)
🧠 Cycle naturel: 1 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
  timestamp: '2025-06-01T00:50:37.425Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 89%', "Taux d'erreur élevé: 10%" ],
    timestamp: '2025-06-01T00:50:37.425Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:37.425Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 6 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 7/10: Décrivez votre système neuronal avec 100 milliards de neuron...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧠 ===== ÉTAT ZONE 1 (MÉMOIRE INSTANTANÉE) =====
📊 Nombre d'entrées: 0
⚠️ Zone 1 VIDE - Lancement des tests automatiques...
===============================================
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 5%" ],
  timestamp: '2025-06-01T00:50:47.426Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 91%', "Taux d'erreur élevé: 5%" ],
    timestamp: '2025-06-01T00:50:47.426Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:47.426Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
🧪 ===== DÉMARRAGE TESTS AUTOMATIQUES =====
🎯 Objectif: Alimenter la Zone 1 pour démarrer l'agent
🤖 Agent: Claude RÉEL (priorité) + Ollama (fallback)
🧪 Test 1/10: Bonjour, je suis Jean-Luc Passave, votre créateur à Sainte-A...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
💾 Sauvegarde générale complète effectuée
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 2 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧪 Test 3/10: Expliquez-moi le fonctionnement de votre mémoire thermique a...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
❌ Erreur génération avec agent: timeout of 60000ms exceeded
✅ Test 3 réussi - Réponse: Erreur agent: timeout of 60000ms exceeded...
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🧪 Test 4/10: Résolvez cette équation: 2x + 5 = 15. Montrez votre raisonne...
[CognitiveAgent] Agent déjà actif 
🤖 Génération avec agent cognitif...
🤖 Utilisation Agent Claude RÉEL
❌ [CLAUDE] Erreur après 0ms: Clé API Anthropic non configurée
⚠️ Erreur Agent Claude: Erreur Agent Claude: Clé API Anthropic non configurée
🔄 Fallback vers Ollama
🧠 Problème STANDARD détecté
⏱️ Timeout: 60s, Tokens: 1000, Temp: 0.7
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 91%' ],
  timestamp: '2025-06-01T00:50:57.427Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 91%' ],
    timestamp: '2025-06-01T00:50:57.427Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:50:57.426Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 90%' ],
  timestamp: '2025-06-01T00:51:07.427Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 90%' ],
    timestamp: '2025-06-01T00:51:07.427Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:51:07.427Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.8,
  alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
  timestamp: '2025-06-01T00:51:17.428Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.8,
    alerts: [ 'Utilisation mémoire élevée: 90%', "Taux d'erreur élevé: 6%" ],
    timestamp: '2025-06-01T00:51:17.428Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:51:17.427Z',
  overallRisk: 0.8,
  alertCount: 2,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 89%' ],
  timestamp: '2025-06-01T00:51:27.428Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 89%' ],
    timestamp: '2025-06-01T00:51:27.428Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:51:27.428Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
✅ Ollama connecté, version: 0.6.6
📚 Modèles disponibles: deepseek-r1:7b, incept5/llama3.1-claude:latest
💾 Sauvegarde générale complète effectuée
🧠 Cycle thermique NATUREL en cours...
🔄 Transfert naturel: working → mediumTerm (temp: 0.488)
🔄 Transfert naturel: longTerm → creative (temp: 0.099)
🧠 Cycle naturel: 2 transferts, 0 consolidations
🚨 ALERTE SÉCURITÉ: SECURITY_ALERT {
  level: 0.7,
  alerts: [ 'Utilisation mémoire élevée: 92%' ],
  timestamp: '2025-06-01T00:51:37.428Z'
}
🚨 ALERTE SÉCURITÉ DÉTECTÉE: {
  type: 'SECURITY_ALERT',
  data: {
    level: 0.7,
    alerts: [ 'Utilisation mémoire élevée: 92%' ],
    timestamp: '2025-06-01T00:51:37.428Z'
  }
}
⚠️ Risque de sécurité élevé: {
  timestamp: '2025-06-01T00:51:37.428Z',
  overallRisk: 0.7,
  alertCount: 1,
  emergencyMode: false,
  systemStable: false
}
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
💾 Sauvegarde générale complète effectuée
