{"name": "louna-ai-production", "version": "2.1.0", "description": "LOUNA AI - Intelligence Artificielle Avancée avec QI 235", "main": "04-SERVEURS/louna-ai-complet-real.js", "scripts": {"start": "node 04-SERVEURS/louna-ai-complet-real.js", "start-simple": "node 04-SERVEURS/louna-test-complet.js", "start-real": "./🤖-LANCER-IA-REELLE.sh", "start-quick": "./⚡-LANCEMENT-RAPIDE.sh", "install-deps": "npm install", "test": "echo \"Tests LOUNA AI\" && exit 0"}, "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "ejs": "^3.1.9", "express": "^4.18.2", "express-fileupload": "^1.4.3", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "socket.io": "^4.7.4", "systeminformation": "^5.27.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["ai", "intelligence-artificielle", "louna", "deepseek", "ollama", "memoire-thermique", "kyber", "qi-235"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jean-luc-passave/louna-ai-production.git"}, "engines": {"node": ">=16.0.0"}}