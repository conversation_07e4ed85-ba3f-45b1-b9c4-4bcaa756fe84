#!/bin/bash

# 🎯 TESTER L'AGENT LOUNA AI À FOND
# Tests intensifs avec exercices de très haut niveau

clear

echo "🎯 =================================================="
echo "🧠     TESTS INTENSIFS AGENT LOUNA AI"
echo "🎯     Exercices de Très Haut Niveau"
echo "⚡     Auto-Scaling KYBER Intelligent"
echo "🔍     Détection de Manques Système"
echo "🚀     Performance Maximale"
echo "🎯 =================================================="
echo ""

# Obtenir le répertoire du script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Vérification que le serveur agent est en cours
echo "🔍 Vérification du serveur agent..."
if ! curl -s http://localhost:3001/api/agent-status &> /dev/null; then
    echo "❌ Serveur agent non accessible sur le port 3001"
    echo "💡 Veuillez d'abord lancer: ./🧠-LANCER-VRAIS-AGENTS.sh"
    exit 1
fi

echo "✅ Serveur agent détecté"

# Vérification des accélérateurs KYBER
echo "⚡ Vérification des accélérateurs KYBER..."
KYBER_STATUS=$(curl -s http://localhost:3001/api/kyber-status)
if echo "$KYBER_STATUS" | grep -q '"success":true'; then
    ACTIVE_ACCELERATORS=$(echo "$KYBER_STATUS" | grep -o '"active":[0-9]*' | cut -d':' -f2)
    PERSISTENT_ACCELERATORS=$(echo "$KYBER_STATUS" | grep -o '"persistent":[0-9]*' | cut -d':' -f2)
    echo "✅ Accélérateurs KYBER: $ACTIVE_ACCELERATORS actifs, $PERSISTENT_ACCELERATORS persistants"
else
    echo "⚠️  Accélérateurs KYBER non disponibles"
fi

echo ""

# Menu de choix
echo "🎯 Choisissez le type de test :"
echo "1) 🔥 Test rapide (1 exercice complexe)"
echo "2) 🚀 Test complet (tous les exercices)"
echo "3) 🧠 Test extrême (exercices + monitoring)"
echo "4) 📊 Statut actuel seulement"
echo ""

read -p "Votre choix (1-4) : " CHOICE

case $CHOICE in
    1)
        echo "🔥 ===== TEST RAPIDE ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🚀 Lancement du test rapide..."
        node "$SCRIPT_DIR/🎯-EXERCICES-HAUT-NIVEAU-AGENT.js" --rapide
        
        echo ""
        echo "📊 Statut final des accélérateurs :"
        curl -s http://localhost:3001/api/kyber-status | jq '.kyber' 2>/dev/null || curl -s http://localhost:3001/api/kyber-status
        ;;
        
    2)
        echo "🚀 ===== TEST COMPLET ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🎯 Lancement de tous les exercices de haut niveau..."
        echo "⚠️  Cela peut prendre plusieurs minutes..."
        echo ""
        
        # Créer un fichier de log
        LOG_FILE="$SCRIPT_DIR/logs/test_complet_$(date +%Y%m%d_%H%M%S).log"
        mkdir -p "$SCRIPT_DIR/logs"
        
        echo "📝 Logs sauvegardés dans: $LOG_FILE"
        echo ""
        
        node "$SCRIPT_DIR/🎯-EXERCICES-HAUT-NIVEAU-AGENT.js" 2>&1 | tee "$LOG_FILE"
        ;;
        
    3)
        echo "🧠 ===== TEST EXTRÊME AVEC MONITORING ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🔍 Monitoring en temps réel des accélérateurs KYBER..."
        echo "🎯 Test avec surveillance continue..."
        echo ""
        
        # Démarrer le monitoring en arrière-plan
        (
            while true; do
                TIMESTAMP=$(date +"%H:%M:%S")
                STATUS=$(curl -s http://localhost:3001/api/kyber-status | jq -r '.kyber | "[\(.active)/\(.max)] Persistants: \(.persistent)"' 2>/dev/null || echo "Status indisponible")
                echo "[$TIMESTAMP] ⚡ KYBER: $STATUS"
                sleep 5
            done
        ) &
        MONITOR_PID=$!
        
        # Lancer les tests
        LOG_FILE="$SCRIPT_DIR/logs/test_extreme_$(date +%Y%m%d_%H%M%S).log"
        mkdir -p "$SCRIPT_DIR/logs"
        
        node "$SCRIPT_DIR/🎯-EXERCICES-HAUT-NIVEAU-AGENT.js" 2>&1 | tee "$LOG_FILE"
        
        # Arrêter le monitoring
        kill $MONITOR_PID 2>/dev/null
        
        echo ""
        echo "📊 Analyse finale des performances..."
        curl -s http://localhost:3001/api/kyber-autoscaling | jq '.autoScaling' 2>/dev/null || echo "Auto-scaling info non disponible"
        ;;
        
    4)
        echo "📊 ===== STATUT ACTUEL ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🤖 Statut de l'agent :"
        curl -s http://localhost:3001/api/agent-status | jq '.' 2>/dev/null || curl -s http://localhost:3001/api/agent-status
        
        echo ""
        echo "⚡ Statut des accélérateurs KYBER :"
        curl -s http://localhost:3001/api/kyber-status | jq '.kyber' 2>/dev/null || curl -s http://localhost:3001/api/kyber-status
        
        echo ""
        echo "📈 Informations auto-scaling :"
        curl -s http://localhost:3001/api/kyber-autoscaling | jq '.autoScaling' 2>/dev/null || curl -s http://localhost:3001/api/kyber-autoscaling
        
        echo ""
        echo "🔥 Statut mémoire thermique :"
        curl -s http://localhost:3001/api/memory-stats | jq '.stats' 2>/dev/null || curl -s http://localhost:3001/api/memory-stats
        ;;
        
    *)
        echo "❌ Choix invalide"
        exit 1
        ;;
esac

echo ""
echo "🎯 ===== RÉSUMÉ FINAL ====="

# Statut final
FINAL_STATUS=$(curl -s http://localhost:3001/api/kyber-status)
if echo "$FINAL_STATUS" | grep -q '"success":true'; then
    FINAL_ACTIVE=$(echo "$FINAL_STATUS" | grep -o '"active":[0-9]*' | cut -d':' -f2)
    FINAL_PERSISTENT=$(echo "$FINAL_STATUS" | grep -o '"persistent":[0-9]*' | cut -d':' -f2)
    FINAL_MAX=$(echo "$FINAL_STATUS" | grep -o '"max":[0-9]*' | cut -d':' -f2)
    
    echo "⚡ Accélérateurs finaux: $FINAL_ACTIVE/$FINAL_MAX"
    echo "🔗 Persistants: $FINAL_PERSISTENT"
    
    if [ "$FINAL_ACTIVE" -gt "$ACTIVE_ACCELERATORS" ]; then
        ADDED=$((FINAL_ACTIVE - ACTIVE_ACCELERATORS))
        echo "📈 Accélérateurs ajoutés: +$ADDED"
        echo "✅ Auto-scaling fonctionnel !"
    else
        echo "📊 Aucun accélérateur ajouté"
    fi
else
    echo "⚠️  Impossible de récupérer le statut final"
fi

echo ""
echo "🎯 Test terminé à $(date)"
echo "🎯 =================================================="
