#!/bin/bash

# 🚀 BOUTON DE LANCEMENT LOUNA AI COMPLET
# Version: 2.1.0 - Edition Complète
# Auteur: <PERSON><PERSON><PERSON> PASSAVE
# QI: 235 (G<PERSON>ie Exceptionnel)

clear

echo "🧠 =================================================="
echo "🚀     LANCEMENT LOUNA AI COMPLET v2.1.0"
echo "🎯     QI: 235 (Génie Exceptionnel)"
echo "🔥     Mémoire Thermique: 37.0°C"
echo "⚡     KYBER: 8/16 Accélérateurs"
echo "🧠 =================================================="
echo ""

# Vérification Node.js
echo "🔍 Vérification des prérequis..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js non installé"
    echo "📥 Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js détecté: $NODE_VERSION"

# Vérification du répertoire
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Répertoire: $SCRIPT_DIR"

# Vérification des fichiers nécessaires
SERVEUR_COMPLET="$SCRIPT_DIR/04-SERVEURS/louna-test-complet.js"
if [ ! -f "$SERVEUR_COMPLET" ]; then
    echo "❌ Serveur complet non trouvé: $SERVEUR_COMPLET"
    exit 1
fi

echo "✅ Serveur complet trouvé"

# Arrêter les processus existants sur le port 3001
echo "🛑 Arrêt des processus existants..."
if lsof -i:3001 &> /dev/null; then
    echo "⚠️  Port 3001 occupé, arrêt du processus..."
    kill $(lsof -t -i:3001) 2> /dev/null || true
    sleep 2
fi

echo "✅ Port 3001 libéré"

# Créer les dossiers de logs
LOG_DIR="$SCRIPT_DIR/logs"
mkdir -p "$LOG_DIR"

# Fichier de log avec timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/louna_complet_$TIMESTAMP.log"

echo "📝 Logs: $LOG_FILE"
echo ""

echo "🚀 ===== DÉMARRAGE LOUNA AI COMPLET ====="
echo "⏰ $(date)"
echo "🎯 Mode: PRODUCTION COMPLÈTE AVEC TESTS"
echo "📊 24 Applications disponibles"
echo "🔗 Interface: http://localhost:3001/"
echo "📋 Routes: http://localhost:3001/routes"
echo "📊 API Test: http://localhost:3001/api/test"
echo ""

# Démarrer le serveur
cd "$SCRIPT_DIR/04-SERVEURS"

echo "🔥 Lancement du serveur complet..."
echo "⚡ Appuyez sur Ctrl+C pour arrêter"
echo "🧠 =================================================="
echo ""

# Lancer avec redirection des logs
node louna-test-complet.js 2>&1 | tee "$LOG_FILE"

# Si le script arrive ici, c'est que le serveur s'est arrêté
echo ""
echo "🛑 ===== SERVEUR ARRÊTÉ ====="
echo "⏰ $(date)"
echo "📝 Logs sauvegardés: $LOG_FILE"
echo "🧠 =================================================="
