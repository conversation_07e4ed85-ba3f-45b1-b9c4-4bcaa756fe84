<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Évolution du Code | Mémoire Thermique</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/css/code-evolution.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="evolution-container">
        <div class="evolution-header">
            <h1 class="evolution-title">Laboratoire d'Évolution du Code</h1>
            <div class="system-status">
                <span class="status-label">Niveau d'Évolution:</span>
                <span id="evolutionLevel" class="status-badge level-badge">Niveau 1</span>
                <span class="status-label">État:</span>
                <span id="systemStatus" class="status-badge status-inactive">Inactif</span>
            </div>
        </div>

        <!-- Section Statut & Contrôles -->
        <div class="evolution-section control-section">
            <h2 class="section-title">État du Système & Contrôles</h2>
            <div class="control-grid">
                <div class="status-card">
                    <div class="status-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="status-info">
                        <h3>Moteur d'Innovation</h3>
                        <div class="status-indicator">
                            <div id="engineStatusIndicator" class="indicator-light"></div>
                            <span id="engineStatusLabel">Inactif</span>
                        </div>
                    </div>
                </div>
                
                <div class="control-card">
                    <h3>Contrôle du Système</h3>
                    <div class="control-buttons">
                        <button id="startEvolutionBtn" class="primary-btn">
                            <i class="fas fa-play"></i> Démarrer l'Évolution
                        </button>
                        <button id="stopEvolutionBtn" class="danger-btn" disabled>
                            <i class="fas fa-stop"></i> Arrêter l'Évolution
                        </button>
                    </div>
                </div>
                
                <div class="stats-card">
                    <h3>Patterns Découverts</h3>
                    <div class="stat-value" id="patternsCount">0</div>
                </div>
                
                <div class="stats-card">
                    <h3>Innovations Générées</h3>
                    <div class="stat-value" id="innovationsCount">0</div>
                </div>
            </div>
            
            <div class="evolution-chart-container">
                <canvas id="evolutionChart"></canvas>
            </div>
        </div>

        <!-- Section Innovations -->
        <div class="evolution-section innovations-section">
            <h2 class="section-title">Dernières Innovations</h2>
            <div class="innovations-controls">
                <button id="refreshInnovationsBtn" class="action-btn">
                    <i class="fas fa-sync-alt"></i> Rafraîchir
                </button>
                <button id="generateInnovationBtn" class="primary-btn">
                    <i class="fas fa-lightbulb"></i> Générer une Innovation
                </button>
            </div>
            
            <div class="innovations-grid" id="innovationsGrid">
                <!-- Les innovations seront ajoutées ici dynamiquement -->
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Chargement des innovations...</p>
                </div>
            </div>
        </div>

        <!-- Section Langages de Programmation -->
        <div class="evolution-section languages-section">
            <h2 class="section-title">Langages de Programmation</h2>
            <div class="languages-grid" id="languagesGrid">
                <!-- Les langages seront ajoutés ici dynamiquement -->
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Chargement des langages...</p>
                </div>
            </div>
        </div>

        <!-- Section Domaines d'Innovation -->
        <div class="evolution-section focus-areas-section">
            <h2 class="section-title">Domaines d'Innovation</h2>
            <div class="focus-areas-grid">
                <div class="focus-area-card" data-focus="performance">
                    <div class="focus-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3>Performance</h3>
                    <p>Optimisation algorithmique, efficacité mémoire, vitesse d'exécution</p>
                </div>
                
                <div class="focus-area-card" data-focus="readability">
                    <div class="focus-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3>Lisibilité</h3>
                    <p>Clarté du code, documentation, conventions de nommage</p>
                </div>
                
                <div class="focus-area-card" data-focus="security">
                    <div class="focus-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Sécurité</h3>
                    <p>Validation des entrées, authentification, chiffrement</p>
                </div>
                
                <div class="focus-area-card" data-focus="scalability">
                    <div class="focus-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Évolutivité</h3>
                    <p>Calcul distribué, gestion de charge, gestion des ressources</p>
                </div>
                
                <div class="focus-area-card" data-focus="maintainability">
                    <div class="focus-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Maintenabilité</h3>
                    <p>Conception modulaire, couverture de tests, refactoring</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="innovationDetailModal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <div id="innovationDetail" class="innovation-detail">
                <!-- Les détails de l'innovation seront ajoutés ici dynamiquement -->
            </div>
        </div>
    </div>

    <div id="generateInnovationModal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Générer une Innovation Ciblée</h2>
            <div class="generate-form">
                <div class="form-group">
                    <label for="languageSelect">Langage de Programmation</label>
                    <select id="languageSelect" class="form-control">
                        <!-- Les options seront ajoutées ici dynamiquement -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="focusAreaSelect">Domaine d'Innovation</label>
                    <select id="focusAreaSelect" class="form-control">
                        <option value="performance">Performance</option>
                        <option value="readability">Lisibilité</option>
                        <option value="security">Sécurité</option>
                        <option value="scalability">Évolutivité</option>
                        <option value="maintainability">Maintenabilité</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button id="confirmGenerateBtn" class="primary-btn">Générer</button>
                    <button id="cancelGenerateBtn" class="neutral-btn">Annuler</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/code-evolution.js"></script>
</body>
</html>