<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système Caméra/Micro Téléphone - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .nav-buttons { display: flex; gap: 10px; }
        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .main-container {
            display: grid;
            grid-template-columns: 350px 1fr 350px;
            gap: 20px;
            padding: 20px;
            min-height: calc(100vh - 80px);
        }
        .connection-panel, .video-panel, .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }
        .video-panel { display: flex; flex-direction: column; }
        .connection-panel, .controls-panel { height: fit-content; }
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
            text-align: center;
            justify-content: center;
        }
        .connection-status {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            font-weight: 600;
            font-size: 16px;
        }
        .status-disconnected {
            background: rgba(255, 0, 0, 0.2);
            border: 2px solid rgba(255, 0, 0, 0.5);
            color: #ff6666;
        }
        .status-connected {
            background: rgba(0, 255, 0, 0.2);
            border: 2px solid rgba(0, 255, 0, 0.5);
            color: #66ff66;
        }
        .qr-code {
            width: 200px;
            height: 200px;
            background: white;
            margin: 0 auto 15px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #333;
            border: 3px solid #ff69b4;
        }
        .control-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 0;
        }
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
        }
        .video-container {
            flex: 1;
            position: relative;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
            min-height: 400px;
        }
        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 18px;
        }
        .phone-instructions {
            background: rgba(255, 255, 0, 0.1);
            border: 2px solid rgba(255, 255, 0, 0.3);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instruction-step {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .step-number {
            width: 24px;
            height: 24px;
            background: #ff69b4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        @media (max-width: 1200px) {
            .main-container { grid-template-columns: 1fr; gap: 15px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-mobile-alt"></i>
            Système Caméra/Micro Téléphone - Louna AI (QI: 225)
        </h1>
        <div class="nav-buttons">
            <a href="/voice-system-enhanced.html" class="nav-btn">
                <i class="fas fa-microphone"></i>
                Vocal
            </a>
            <a href="/advanced-code-editor.html" class="nav-btn">
                <i class="fas fa-code"></i>
                Code
            </a>
            <a href="/qi-manager.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                QI
            </a>
            <a href="/thermal-memory-dashboard.html" class="nav-btn">
                <i class="fas fa-fire"></i>
                Mémoire
            </a>
            <a href="/chat.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="main-container">
        <div class="connection-panel">
            <div class="panel-title">
                <i class="fas fa-wifi"></i>
                Connexion Wi-Fi
            </div>
            <div class="connection-status status-disconnected" id="connectionStatus">
                <i class="fas fa-times-circle"></i>
                Téléphone non connecté - Cliquez pour simuler
            </div>
            <div class="phone-instructions">
                <h4 style="color: #ffff00; margin-bottom: 10px;">
                    <i class="fas fa-info-circle"></i>
                    Instructions de connexion
                </h4>
                <div class="instruction-step">
                    <div class="step-number">1</div>
                    <span>Connectez votre téléphone au même Wi-Fi</span>
                </div>
                <div class="instruction-step">
                    <div class="step-number">2</div>
                    <span>Scannez le QR code ou entrez l'URL</span>
                </div>
                <div class="instruction-step">
                    <div class="step-number">3</div>
                    <span>Autorisez caméra et microphone</span>
                </div>
                <div class="instruction-step">
                    <div class="step-number">4</div>
                    <span>Votre téléphone devient webcam/micro</span>
                </div>
            </div>
            <div style="text-align: center; margin-bottom: 20px;">
                <div class="qr-code" id="qrCode">
                    <div style="text-align: center;">
                        <i class="fas fa-qrcode" style="font-size: 48px; margin-bottom: 10px;"></i>
                        <div>QR Code de connexion</div>
                        <div style="font-size: 12px; margin-top: 5px;">http://*************:3001/phone-connect</div>
                    </div>
                </div>
                <button class="control-btn" onclick="generateQRCode()">
                    <i class="fas fa-qrcode"></i>
                    Générer QR Code
                </button>
            </div>
        </div>

        <div class="video-panel">
            <div class="panel-title">
                <i class="fas fa-video"></i>
                Flux Vidéo Téléphone
            </div>
            <div class="video-container">
                <video id="phoneVideo" autoplay muted style="width: 100%; height: 100%; object-fit: cover; display: none;"></video>
                <div class="video-overlay" id="videoOverlay">
                    <div style="text-align: center;">
                        <i class="fas fa-mobile-alt" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <div>En attente de connexion téléphone</div>
                        <div style="font-size: 14px; margin-top: 10px;">
                            Scannez le QR code avec votre téléphone
                        </div>
                    </div>
                </div>
            </div>
            <div style="display: flex; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <button style="width: 60px; height: 60px; border-radius: 50%; border: none; cursor: pointer; background: linear-gradient(135deg, #4caf50, #388e3c); color: white; font-size: 24px;" onclick="toggleCamera()" disabled id="cameraBtn">
                    <i class="fas fa-video"></i>
                </button>
                <button style="width: 60px; height: 60px; border-radius: 50%; border: none; cursor: pointer; background: linear-gradient(135deg, #2196f3, #1976d2); color: white; font-size: 24px;" onclick="toggleMicrophone()" disabled id="micBtn">
                    <i class="fas fa-microphone"></i>
                </button>
                <button style="width: 60px; height: 60px; border-radius: 50%; border: none; cursor: pointer; background: linear-gradient(135deg, #f44336, #d32f2f); color: white; font-size: 24px;" onclick="disconnectPhone()" disabled id="disconnectBtn">
                    <i class="fas fa-phone-slash"></i>
                </button>
            </div>
            <div style="background: rgba(0, 255, 255, 0.1); border: 2px solid rgba(0, 255, 255, 0.3); border-radius: 15px; padding: 15px;">
                <div style="font-size: 14px; margin-bottom: 10px; font-weight: 600;">
                    <i class="fas fa-eye"></i>
                    Reconnaissance IA en temps réel
                </div>
                <div id="recognitionResult" style="background: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 100px; overflow-y: auto;">
                    En attente du flux vidéo...
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <div class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Contrôles Avancés
            </div>
            <button class="control-btn" onclick="startAIAnalysis()">
                <i class="fas fa-brain"></i>
                Démarrer Analyse IA
            </button>
            <button class="control-btn" onclick="takeScreenshot()">
                <i class="fas fa-camera"></i>
                Capture d'écran
            </button>
            <button class="control-btn" onclick="startRecording()">
                <i class="fas fa-record-vinyl"></i>
                Enregistrement
            </button>
        </div>
    </div>

    <script>
        let isConnected = false;
        let localStream = null;
        let aiAnalysisActive = false;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 Système caméra/micro téléphone initialisé');
            document.getElementById('connectionStatus').onclick = simulateConnection;
            generateQRCode();
        });

        function simulateConnection() {
            if (!isConnected) {
                isConnected = true;
                document.getElementById('connectionStatus').className = 'connection-status status-connected';
                document.getElementById('connectionStatus').innerHTML = '<i class="fas fa-check-circle"></i> iPhone connecté (Simulation)';
                
                document.getElementById('cameraBtn').disabled = false;
                document.getElementById('micBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = false;
                
                // Essayer d'accéder à la caméra locale
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    navigator.mediaDevices.getUserMedia({ video: true, audio: true })
                        .then(stream => {
                            const video = document.getElementById('phoneVideo');
                            video.srcObject = stream;
                            video.style.display = 'block';
                            document.getElementById('videoOverlay').style.display = 'none';
                            localStream = stream;
                            addToRecognitionResult('📹 Flux vidéo local activé');
                        })
                        .catch(error => {
                            console.warn('Caméra non accessible:', error);
                            addToRecognitionResult('⚠️ Simulation vidéo active');
                        });
                }
                
                addToRecognitionResult('📱 iPhone connecté avec succès');
                addToRecognitionResult('🧠 Louna AI prêt pour l\'analyse');
            }
        }

        function generateQRCode() {
            const url = 'http://*************:3001/phone-connect';
            const qrCodeElement = document.getElementById('qrCode');
            qrCodeElement.innerHTML = '<img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=' + encodeURIComponent(url) + '" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain;">';
            addToRecognitionResult('📱 QR Code généré pour la connexion');
        }

        function toggleCamera() {
            const btn = document.getElementById('cameraBtn');
            const icon = btn.querySelector('i');
            if (icon.classList.contains('fa-video')) {
                icon.className = 'fas fa-video-slash';
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                addToRecognitionResult('📹 Caméra désactivée');
            } else {
                icon.className = 'fas fa-video';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                addToRecognitionResult('📹 Caméra activée');
            }
        }

        function toggleMicrophone() {
            const btn = document.getElementById('micBtn');
            const icon = btn.querySelector('i');
            if (icon.classList.contains('fa-microphone')) {
                icon.className = 'fas fa-microphone-slash';
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                addToRecognitionResult('🎤 Microphone désactivé');
            } else {
                icon.className = 'fas fa-microphone';
                btn.style.background = 'linear-gradient(135deg, #2196f3, #1976d2)';
                addToRecognitionResult('🎤 Microphone activé');
            }
        }

        function disconnectPhone() {
            isConnected = false;
            document.getElementById('connectionStatus').className = 'connection-status status-disconnected';
            document.getElementById('connectionStatus').innerHTML = '<i class="fas fa-times-circle"></i> Téléphone déconnecté';
            
            document.getElementById('cameraBtn').disabled = true;
            document.getElementById('micBtn').disabled = true;
            document.getElementById('disconnectBtn').disabled = true;
            
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            document.getElementById('phoneVideo').style.display = 'none';
            document.getElementById('videoOverlay').style.display = 'flex';
            addToRecognitionResult('📱 Téléphone déconnecté');
        }

        function startAIAnalysis() {
            if (!aiAnalysisActive) {
                aiAnalysisActive = true;
                event.target.innerHTML = '<i class="fas fa-stop"></i> Arrêter Analyse IA';
                addToRecognitionResult('🧠 Analyse IA démarrée par Louna');
                
                const analyses = [
                    '👤 Visage détecté: Jean-Luc (confiance: 98%)',
                    '😊 Émotion détectée: Sourire (bonheur: 85%)',
                    '👁️ Contact visuel: Oui (attention: 92%)',
                    '🗣️ Parole détectée: Français (clarté: 94%)',
                    '📱 Objet détecté: Téléphone (confiance: 87%)',
                    '💡 Éclairage: Bon (luminosité: 78%)',
                    '🎯 Position: Centrée (stabilité: 91%)',
                    '🔊 Audio: Clair (qualité: 89%)'
                ];
                
                setInterval(() => {
                    if (aiAnalysisActive) {
                        const randomAnalysis = analyses[Math.floor(Math.random() * analyses.length)];
                        addToRecognitionResult(randomAnalysis);
                    }
                }, 3000);
            } else {
                aiAnalysisActive = false;
                event.target.innerHTML = '<i class="fas fa-brain"></i> Démarrer Analyse IA';
                addToRecognitionResult('🧠 Analyse IA arrêtée');
            }
        }

        function takeScreenshot() {
            const video = document.getElementById('phoneVideo');
            if (video && video.videoWidth > 0) {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'louna-screenshot-' + Date.now() + '.png';
                    a.click();
                    addToRecognitionResult('📸 Capture d\'écran sauvegardée');
                });
            } else {
                addToRecognitionResult('❌ Aucun flux vidéo pour la capture');
            }
        }

        function startRecording() {
            addToRecognitionResult('🔴 Enregistrement démarré');
        }

        function addToRecognitionResult(message) {
            const resultElement = document.getElementById('recognitionResult');
            const timestamp = new Date().toLocaleTimeString();
            const newLine = '[' + timestamp + '] ' + message + '\n';
            resultElement.textContent = newLine + resultElement.textContent;
            const lines = resultElement.textContent.split('\n');
            if (lines.length > 20) {
                resultElement.textContent = lines.slice(0, 20).join('\n');
            }
        }
    </script>
</body>
</html>
