<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laboratoire de Code - Agent à Mémoire Thermique</title>

    <!-- Styles de base -->
    <link rel="stylesheet" href="/css/reset.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/code-display.css">

    <!-- Styles futuristes -->
    <link rel="stylesheet" href="/css/futuristic-interface.css">
    <link rel="stylesheet" href="/css/modern-components.css">
    <link rel="stylesheet" href="/css/code-lab.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CodeMirror pour l'éditeur de code -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/dracula.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/clike/clike.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="code-lab-wrapper">
        <!-- En-tête du laboratoire -->
        <header class="code-lab-header">
            <div class="logo">
                <i class="fas fa-code"></i>
                <h1>Laboratoire de Code</h1>
            </div>
            <div class="header-actions">
                <button id="returnToMain" class="btn"><i class="fas fa-arrow-left"></i> Retour à l'interface principale</button>
            </div>
        </header>

        <!-- Contenu principal -->
        <div class="code-lab-container">
            <!-- Barre latérale des outils -->
            <div class="code-lab-sidebar">
                <div class="sidebar-section">
                    <h3><i class="fas fa-file-code"></i> Fichiers</h3>
                    <div class="sidebar-actions">
                        <button id="newCodeBtn" class="btn small"><i class="fas fa-plus"></i> Nouveau</button>
                        <button id="loadCodeBtn" class="btn small"><i class="fas fa-folder-open"></i> Charger</button>
                        <button id="saveCodeBtn" class="btn small primary"><i class="fas fa-save"></i> Sauvegarder</button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-code"></i> Langage</h3>
                    <select id="languageSelect" class="input">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="html">HTML</option>
                        <option value="css">CSS</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="rust">Rust</option>
                        <option value="go">Go</option>
                        <option value="ruby">Ruby</option>
                        <option value="php">PHP</option>
                    </select>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-tools"></i> Outils</h3>
                    <div class="tool-buttons">
                        <button id="formatCodeBtn" class="btn tool-btn"><i class="fas fa-align-left"></i> Formater</button>
                        <button id="analyzeCodeBtn" class="btn tool-btn"><i class="fas fa-search"></i> Analyser</button>
                        <button id="improveCodeBtn" class="btn tool-btn"><i class="fas fa-magic"></i> Améliorer</button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-play"></i> Exécution</h3>
                    <div class="tool-buttons">
                        <button id="runCodeBtn" class="btn primary tool-btn"><i class="fas fa-play"></i> Exécuter</button>
                        <button id="clearOutputBtn" class="btn tool-btn"><i class="fas fa-trash"></i> Effacer</button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-compress-alt"></i> Compression</h3>
                    <div class="tool-buttons">
                        <button id="analyzeCompressionBtn" class="btn tool-btn"><i class="fas fa-chart-bar"></i> Analyser</button>
                        <button id="innovateCompressionBtn" class="btn tool-btn"><i class="fas fa-lightbulb"></i> Innover</button>
                    </div>
                </div>
            </div>

            <!-- Zone principale -->
            <div class="code-lab-main">
                <!-- Éditeur de code -->
                <div class="code-editor-container">
                    <h2><i class="fas fa-edit"></i> Éditeur de Code</h2>
                    <div id="codeEditor" class="code-editor"></div>
                </div>

                <!-- Résultats -->
                <div class="code-results-container">
                    <div class="results-tabs">
                        <button class="tab-btn active" data-tab="output"><i class="fas fa-terminal"></i> Sortie</button>
                        <button class="tab-btn" data-tab="analysis"><i class="fas fa-search"></i> Analyse</button>
                        <button class="tab-btn" data-tab="compression"><i class="fas fa-compress-alt"></i> Compression</button>
                    </div>

                    <div class="tab-content">
                        <div id="output" class="tab-pane active">
                            <div id="outputDisplay" class="output-display">
                                <p class="output-placeholder">Les résultats d'exécution s'afficheront ici.</p>
                            </div>
                        </div>

                        <div id="analysis" class="tab-pane">
                            <div id="analysisDisplay" class="analysis-display">
                                <p class="analysis-placeholder">L'analyse du code s'affichera ici.</p>
                            </div>
                        </div>

                        <div id="compression" class="tab-pane">
                            <div id="compressionDisplay" class="compression-display">
                                <p class="compression-placeholder">Les résultats d'analyse de compression s'afficheront ici.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <div id="loadCodeModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Charger un code</h2>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="load-options">
                        <div class="load-option">
                            <h3>Codes sauvegardés</h3>
                            <select id="savedCodeSelect" class="input">
                                <option disabled selected>Sélectionnez un code</option>
                            </select>
                            <button id="loadSavedCodeBtn" class="btn primary"><i class="fas fa-check"></i> Charger</button>
                        </div>

                        <div class="load-option">
                            <h3>Importer un fichier</h3>
                            <input type="file" id="codeFileInput" class="file-input" accept=".js,.py,.html,.css,.java,.cpp,.h,.rs,.go,.rb,.php">
                            <label for="codeFileInput" class="file-label"><i class="fas fa-upload"></i> Choisir un fichier</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="saveCodeModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Sauvegarder le code</h2>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="codeNameInput">Nom du code</label>
                        <input type="text" id="codeNameInput" class="input" placeholder="Entrez un nom pour ce code">
                    </div>
                    <div class="form-group">
                        <label for="codeDescInput">Description (optionnelle)</label>
                        <textarea id="codeDescInput" class="input" placeholder="Entrez une description pour ce code"></textarea>
                    </div>
                    <div class="modal-actions">
                        <button id="cancelSaveBtn" class="btn">Annuler</button>
                        <button id="confirmSaveBtn" class="btn primary">Sauvegarder</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="compressionAnalysisModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h2>Analyse de Compression Détaillée</h2>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="compressionDetailDisplay"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/code-tester.js"></script>
</body>
</html>
