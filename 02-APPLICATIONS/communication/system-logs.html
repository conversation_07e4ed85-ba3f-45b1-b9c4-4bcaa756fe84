<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs Système - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #00ff00;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            padding: 15px 0;
            border-bottom: 2px solid #00ff00;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 1.8rem;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }

        .header-title .icon {
            font-size: 2rem;
            color: #00ff00;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 15px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            border-radius: 5px;
            color: #00ff00;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
        }

        .nav-btn:hover {
            background: rgba(0, 255, 0, 0.2);
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
        }

        /* Container principal */
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            height: calc(100vh - 80px);
        }

        /* Zone des logs */
        .logs-section {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
        }

        .logs-header {
            padding: 15px;
            border-bottom: 1px solid #00ff00;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logs-title {
            color: #00ff00;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .logs-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 5px 10px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            border-radius: 3px;
            color: #00ff00;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(0, 255, 0, 0.2);
        }

        .control-btn.active {
            background: rgba(0, 255, 0, 0.3);
        }

        .logs-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            background: rgba(0, 255, 0, 0.05);
        }

        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }

        .log-level {
            margin-right: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .log-level.info {
            background: rgba(0, 123, 255, 0.3);
            color: #007bff;
        }

        .log-level.success {
            background: rgba(40, 167, 69, 0.3);
            color: #28a745;
        }

        .log-level.warning {
            background: rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }

        .log-level.error {
            background: rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }

        .log-message {
            color: #00ff00;
        }

        /* Panneau de contrôle */
        .control-panel {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            border-radius: 5px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel-section {
            border-bottom: 1px solid rgba(0, 255, 0, 0.3);
            padding-bottom: 15px;
        }

        .panel-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .panel-title {
            color: #00ff00;
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .metric-label {
            color: #888;
        }

        .metric-value {
            color: #00ff00;
            font-weight: bold;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background: #28a745;
        }

        .status-dot.warning {
            background: #ffc107;
        }

        .status-dot.error {
            background: #dc3545;
        }

        .filter-section {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .filter-input {
            padding: 8px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff00;
            border-radius: 3px;
            color: #00ff00;
            font-family: inherit;
            font-size: 0.9rem;
        }

        .filter-input:focus {
            outline: none;
            box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
        }

        .filter-input::placeholder {
            color: #666;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }

            .control-panel {
                max-height: 300px;
                overflow-y: auto;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .container {
                padding: 10px;
                gap: 10px;
            }

            .logs-content {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-terminal icon"></i>
                <h1>LOGS SYSTÈME LOUNA</h1>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn">
                    <i class="fas fa-home"></i>
                    ACCUEIL
                </a>
                <a href="/dashboard-master.html" class="nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    DASHBOARD
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    CHAT
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Section des logs -->
        <div class="logs-section">
            <div class="logs-header">
                <div class="logs-title">JOURNAL SYSTÈME EN TEMPS RÉEL</div>
                <div class="logs-controls">
                    <button class="control-btn active" onclick="toggleAutoScroll()">AUTO-SCROLL</button>
                    <button class="control-btn" onclick="clearLogs()">EFFACER</button>
                    <button class="control-btn" onclick="exportLogs()">EXPORTER</button>
                </div>
            </div>
            <div class="logs-content" id="logs-content">
                <!-- Les logs seront ajoutés ici dynamiquement -->
            </div>
        </div>

        <!-- Panneau de contrôle -->
        <div class="control-panel">
            <div class="panel-section">
                <div class="panel-title">ÉTAT SYSTÈME</div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>Agent Claude</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>Serveur Express</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>Accélérateurs Kyber</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>Mémoire Thermique</span>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">MÉTRIQUES</div>
                <div class="metric-item">
                    <span class="metric-label">Messages traités:</span>
                    <span class="metric-value" id="messages-count">0</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Uptime:</span>
                    <span class="metric-value" id="uptime">00:00:00</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Mémoire:</span>
                    <span class="metric-value" id="memory-usage">42.3°C</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Kyber Boost:</span>
                    <span class="metric-value" id="kyber-boost">320%</span>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">FILTRES</div>
                <div class="filter-section">
                    <input type="text" class="filter-input" placeholder="Rechercher dans les logs..." id="search-filter">
                    <select class="filter-input" id="level-filter">
                        <option value="">Tous les niveaux</option>
                        <option value="info">INFO</option>
                        <option value="success">SUCCESS</option>
                        <option value="warning">WARNING</option>
                        <option value="error">ERROR</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let logs = [];
        let autoScroll = true;
        let startTime = Date.now();
        let messageCount = 0;

        // Types de logs
        const LOG_TYPES = {
            INFO: 'info',
            SUCCESS: 'success',
            WARNING: 'warning',
            ERROR: 'error'
        };

        // Ajouter un log
        function addLog(level, message, source = 'SYSTEM') {
            const timestamp = new Date().toLocaleTimeString('fr-FR', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                fractionalSecondDigits: 3
            });

            const logEntry = {
                timestamp,
                level,
                message,
                source,
                id: Date.now() + Math.random()
            };

            logs.push(logEntry);

            // Limiter à 1000 logs
            if (logs.length > 1000) {
                logs.shift();
            }

            renderLogs();
            updateMetrics();
        }

        // Rendre les logs
        function renderLogs() {
            const logsContent = document.getElementById('logs-content');
            const searchFilter = document.getElementById('search-filter').value.toLowerCase();
            const levelFilter = document.getElementById('level-filter').value;

            let filteredLogs = logs;

            // Appliquer les filtres
            if (searchFilter) {
                filteredLogs = filteredLogs.filter(log =>
                    log.message.toLowerCase().includes(searchFilter) ||
                    log.source.toLowerCase().includes(searchFilter)
                );
            }

            if (levelFilter) {
                filteredLogs = filteredLogs.filter(log => log.level === levelFilter);
            }

            // Générer le HTML
            const logsHtml = filteredLogs.map(log => `
                <div class="log-entry">
                    <span class="log-timestamp">[${log.timestamp}]</span>
                    <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                    <span class="log-message">[${log.source}] ${log.message}</span>
                </div>
            `).join('');

            logsContent.innerHTML = logsHtml;

            // Auto-scroll si activé
            if (autoScroll) {
                logsContent.scrollTop = logsContent.scrollHeight;
            }
        }

        // Basculer l'auto-scroll
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const btn = event.target;
            btn.classList.toggle('active', autoScroll);
            btn.textContent = autoScroll ? 'AUTO-SCROLL' : 'SCROLL OFF';
        }

        // Effacer les logs
        function clearLogs() {
            if (confirm('Êtes-vous sûr de vouloir effacer tous les logs ?')) {
                logs = [];
                renderLogs();
                addLog(LOG_TYPES.INFO, 'Logs effacés par l\'utilisateur', 'SYSTEM');
            }
        }

        // Exporter les logs
        function exportLogs() {
            const logsText = logs.map(log =>
                `[${log.timestamp}] ${log.level.toUpperCase()} [${log.source}] ${log.message}`
            ).join('\n');

            const blob = new Blob([logsText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `louna-logs-${new Date().toISOString().split('T')[0]}.txt`;
            link.click();

            URL.revokeObjectURL(url);
            addLog(LOG_TYPES.SUCCESS, 'Logs exportés avec succès', 'EXPORT');
        }

        // Mettre à jour les métriques
        function updateMetrics() {
            const uptimeMs = Date.now() - startTime;
            const hours = Math.floor(uptimeMs / 3600000);
            const minutes = Math.floor((uptimeMs % 3600000) / 60000);
            const seconds = Math.floor((uptimeMs % 60000) / 1000);

            document.getElementById('uptime').textContent =
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            document.getElementById('messages-count').textContent = messageCount;
        }

        // Simuler l'activité système
        function simulateSystemActivity() {
            const activities = [
                { level: LOG_TYPES.INFO, message: 'Vérification de l\'état des accélérateurs Kyber', source: 'KYBER' },
                { level: LOG_TYPES.SUCCESS, message: 'Synchronisation mémoire thermique terminée', source: 'MEMORY' },
                { level: LOG_TYPES.INFO, message: 'Mise à jour des métriques QI', source: 'QI-MONITOR' },
                { level: LOG_TYPES.SUCCESS, message: 'Connexion Claude maintenue', source: 'CLAUDE' },
                { level: LOG_TYPES.INFO, message: 'Nettoyage automatique des caches', source: 'SYSTEM' },
                { level: LOG_TYPES.WARNING, message: 'Température mémoire légèrement élevée', source: 'MEMORY' },
                { level: LOG_TYPES.SUCCESS, message: 'Optimisation Kyber appliquée', source: 'KYBER' },
                { level: LOG_TYPES.INFO, message: 'Sauvegarde automatique des paramètres', source: 'CONFIG' }
            ];

            if (Math.random() > 0.7) {
                const activity = activities[Math.floor(Math.random() * activities.length)];
                addLog(activity.level, activity.message, activity.source);
            }
        }

        // Surveiller l'API
        async function monitorAPI() {
            try {
                const response = await fetch('/api/settings');
                if (response.ok) {
                    addLog(LOG_TYPES.SUCCESS, 'API de paramètres accessible', 'API');
                } else {
                    addLog(LOG_TYPES.WARNING, `API répond avec le statut ${response.status}`, 'API');
                }
            } catch (error) {
                addLog(LOG_TYPES.ERROR, `Erreur de connexion API: ${error.message}`, 'API');
            }
        }

        // Gestionnaires d'événements pour les filtres
        document.getElementById('search-filter').addEventListener('input', renderLogs);
        document.getElementById('level-filter').addEventListener('change', renderLogs);

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Logs d'initialisation
            addLog(LOG_TYPES.SUCCESS, 'Interface de logs système initialisée', 'SYSTEM');
            addLog(LOG_TYPES.INFO, 'Connexion au serveur Louna établie', 'SERVER');
            addLog(LOG_TYPES.SUCCESS, 'Agent Claude 4GB opérationnel', 'CLAUDE');
            addLog(LOG_TYPES.INFO, 'Accélérateurs Kyber en ligne', 'KYBER');
            addLog(LOG_TYPES.SUCCESS, 'Mémoire thermique active', 'MEMORY');
            addLog(LOG_TYPES.INFO, 'Monitoring QI démarré', 'QI-MONITOR');

            // Mise à jour périodique
            setInterval(updateMetrics, 1000);
            setInterval(simulateSystemActivity, 3000);
            setInterval(monitorAPI, 30000);

            // Mise à jour des métriques dynamiques
            setInterval(() => {
                const memoryTemp = (40 + Math.random() * 10).toFixed(1);
                const kyberBoost = (300 + Math.random() * 100).toFixed(0);

                document.getElementById('memory-usage').textContent = memoryTemp + '°C';
                document.getElementById('kyber-boost').textContent = kyberBoost + '%';
            }, 5000);

            console.log('📊 Interface de logs système initialisée');
        });

        // Intercepter les erreurs JavaScript
        window.addEventListener('error', function(event) {
            addLog(LOG_TYPES.ERROR, `Erreur JavaScript: ${event.message} (${event.filename}:${event.lineno})`, 'JS-ERROR');
        });

        // Intercepter les erreurs de promesses
        window.addEventListener('unhandledrejection', function(event) {
            addLog(LOG_TYPES.ERROR, `Promesse rejetée: ${event.reason}`, 'PROMISE-ERROR');
        });

        // Simuler des messages Claude
        setInterval(() => {
            if (Math.random() > 0.8) {
                messageCount++;
                addLog(LOG_TYPES.SUCCESS, `Message traité par Claude (Total: ${messageCount})`, 'CLAUDE');
            }
        }, 8000);
    </script>
</body>
</html>
