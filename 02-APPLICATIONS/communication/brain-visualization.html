<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Visualisation du Cerveau</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/unified-interface.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Three.js pour la visualisation 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three-spritetext@1.6.5/dist/three-spritetext.min.js"></script>

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        #brain-container {
            width: 100%;
            height: calc(100vh - 120px);
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            border-radius: var(--border-radius);
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 0 50px rgba(255, 105, 180, 0.1);
        }

        #brain-controls {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            display: flex;
            flex-direction: row;
            gap: 15px;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        /* Boutons de contrôle des panneaux */
        .panel-toggle-buttons {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1001;
        }

        .panel-toggle-btn {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid var(--primary);
            border-radius: 50%;
            color: var(--primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .panel-toggle-btn:hover {
            background: var(--primary);
            color: black;
            transform: scale(1.1);
        }

        .panel-toggle-btn.active {
            background: var(--primary);
            color: black;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-label {
            color: var(--text-primary);
            font-size: 14px;
            min-width: 120px;
        }

        .control-slider {
            flex-grow: 1;
            height: 8px;
            -webkit-appearance: none;
            background: linear-gradient(to right, var(--temp-cool), var(--temp-medium), var(--temp-warm), var(--temp-hot));
            border-radius: 5px;
        }

        .control-value {
            color: var(--text-primary);
            font-size: 14px;
            min-width: 50px;
            text-align: right;
        }

        .zone-label {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        #info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: var(--border-radius);
            color: var(--text-primary);
            max-width: 300px;
            font-size: 14px;
            z-index: 10;
        }

        #info-panel h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            color: var(--accent-color);
        }

        #info-panel p {
            margin: 5px 0;
        }

        #memory-flow {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: var(--border-radius);
            color: var(--text-primary);
            max-width: 300px;
            font-size: 14px;
            z-index: 10;
        }

        #memory-flow h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            color: var(--accent-color);
        }

        .flow-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .flow-label {
            color: var(--text-secondary);
        }

        .flow-value {
            font-weight: 500;
        }

        .flow-value.positive {
            color: var(--success);
        }

        .flow-value.negative {
            color: var(--danger);
        }

        .flow-value {
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 40px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: 500;
            text-shadow: 0 0 5px currentColor;
        }

        .flow-value.positive {
            color: #00ff88;
            text-shadow: 0 0 8px #00ff88;
        }

        .flow-value.negative {
            color: #ff4444;
            text-shadow: 0 0 8px #ff4444;
        }

        /* Styles pour le panneau QI et Neurones - COMPACT */
        #qi-neuron-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 220px;
            background: rgba(0, 0, 0, 0.85);
            border: 1px solid var(--primary);
            border-radius: 8px;
            padding: 10px;
            color: var(--text);
            font-family: 'Courier New', monospace;
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-height: 40vh;
            overflow-y: auto;
        }

        #qi-neuron-panel h3 {
            margin: 0 0 15px 0;
            color: var(--primary);
            text-align: center;
            font-size: 18px;
            text-shadow: 0 0 10px var(--primary);
        }

        #qi-neuron-panel h4 {
            margin: 15px 0 10px 0;
            color: var(--secondary);
            font-size: 14px;
            border-bottom: 1px solid var(--secondary);
            padding-bottom: 5px;
        }

        .qi-section, .neuron-section, .network-section {
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #ccc;
        }

        .stat-value {
            font-weight: bold;
            font-size: 14px;
            text-shadow: 0 0 5px currentColor;
        }

        .qi-value {
            color: #00ff88;
            font-size: 16px;
        }

        .level-value {
            color: #ffaa00;
        }

        .xp-value {
            color: #88aaff;
        }

        .bonus-value {
            color: #ff88aa;
        }

        .neuron-total {
            color: #00ffff;
        }

        .neuron-active {
            color: #00ff00;
        }

        .efficiency-value {
            color: #ffff00;
        }

        .health-value {
            color: #ff6600;
        }

        .network-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
        }

        .network-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 8px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 3px;
            font-size: 11px;
        }

        .network-label {
            color: #aaa;
        }

        .network-count {
            color: var(--primary);
            font-weight: bold;
            text-shadow: 0 0 3px var(--primary);
        }

        /* Styles pour le panneau émotionnel - COMPACT */
        #emotional-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 240px;
            background: rgba(0, 0, 0, 0.85);
            border: 1px solid var(--secondary);
            border-radius: 8px;
            padding: 10px;
            color: var(--text);
            font-family: 'Courier New', monospace;
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-height: 40vh;
            overflow-y: auto;
        }

        #emotional-panel h3 {
            margin: 0 0 15px 0;
            color: var(--secondary);
            text-align: center;
            font-size: 18px;
            text-shadow: 0 0 10px var(--secondary);
        }

        #emotional-panel h4 {
            margin: 15px 0 10px 0;
            color: #ff88aa;
            font-size: 14px;
            border-bottom: 1px solid #ff88aa;
            padding-bottom: 5px;
        }

        .mood-display {
            text-align: center;
            margin-bottom: 15px;
        }

        .mood-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .mood-emoji {
            font-size: 24px;
            animation: pulse 2s infinite;
        }

        .mood-text {
            font-size: 16px;
            font-weight: bold;
            color: #ff88aa;
            text-shadow: 0 0 8px #ff88aa;
        }

        .mood-intensity {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .intensity-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .intensity-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffaa00, #00ff88);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .intensity-value {
            color: #ffaa00;
            font-weight: bold;
        }

        .emotion-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .emotion-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            font-size: 11px;
        }

        .emotion-label {
            color: #ccc;
        }

        .emotion-value {
            font-weight: bold;
            color: #00ff88;
            text-shadow: 0 0 3px #00ff88;
        }

        .stress-value {
            color: #ff6666 !important;
            text-shadow: 0 0 3px #ff6666 !important;
        }

        .fatigue-value {
            color: #ffaa66 !important;
            text-shadow: 0 0 3px #ffaa66 !important;
        }

        .thoughts-container {
            max-height: 120px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 5px;
            padding: 8px;
        }

        .thought-item {
            margin-bottom: 8px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
            border-left: 3px solid #ff88aa;
        }

        .thought-time {
            font-size: 10px;
            color: #888;
            display: block;
            margin-bottom: 2px;
        }

        .thought-text {
            font-size: 11px;
            color: #ddd;
            font-style: italic;
            line-height: 1.3;
        }

        .biorhythm-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .biorhythm-label {
            font-size: 12px;
            color: #ccc;
        }

        .biorhythm-value {
            font-weight: bold;
            color: #88aaff;
            text-shadow: 0 0 3px #88aaff;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Animation pour les changements d'humeur */
        .mood-change {
            animation: moodChange 1s ease;
        }

        @keyframes moodChange {
            0% { transform: scale(1); filter: brightness(1); }
            50% { transform: scale(1.2); filter: brightness(1.5); }
            100% { transform: scale(1); filter: brightness(1); }
        }

        /* Scrollbar personnalisée pour les panneaux */
        #emotional-panel::-webkit-scrollbar,
        .thoughts-container::-webkit-scrollbar {
            width: 6px;
        }

        #emotional-panel::-webkit-scrollbar-track,
        .thoughts-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #emotional-panel::-webkit-scrollbar-thumb,
        .thoughts-container::-webkit-scrollbar-thumb {
            background: var(--primary);
            border-radius: 3px;
        }

        /* Styles pour les boutons d'interaction émotionnelle */
        .interaction-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 10px;
        }

        .emotion-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid transparent;
        }

        .emotion-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .learning-btn {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            border-color: #4CAF50;
        }

        .learning-btn:hover {
            background: linear-gradient(45deg, #66BB6A, #4CAF50);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        .success-btn {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
            border-color: #FF9800;
        }

        .success-btn:hover {
            background: linear-gradient(45deg, #FFB74D, #FF9800);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        .challenge-btn {
            background: linear-gradient(45deg, #F44336, #EF5350);
            border-color: #F44336;
        }

        .challenge-btn:hover {
            background: linear-gradient(45deg, #EF5350, #F44336);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
        }

        .rest-btn {
            background: linear-gradient(45deg, #9C27B0, #BA68C8);
            border-color: #9C27B0;
        }

        .rest-btn:hover {
            background: linear-gradient(45deg, #BA68C8, #9C27B0);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
        }

        .creative-btn {
            background: linear-gradient(45deg, #E91E63, #F06292);
            border-color: #E91E63;
        }

        .creative-btn:hover {
            background: linear-gradient(45deg, #F06292, #E91E63);
            box-shadow: 0 4px 12px rgba(233, 30, 99, 0.4);
        }

        .update-btn {
            background: linear-gradient(45deg, #2196F3, #64B5F6);
            border-color: #2196F3;
        }

        .update-btn:hover {
            background: linear-gradient(45deg, #64B5F6, #2196F3);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        }

        .emotion-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Animation pour les boutons cliqués */
        .emotion-btn.clicked {
            animation: buttonClick 0.6s ease;
        }

        @keyframes buttonClick {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); filter: brightness(1.3); }
            100% { transform: scale(1); }
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">

    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <style>
        /* CORRECTION UNIVERSELLE DE LISIBILITÉ - LOUNA */

        /* Optimisation du rendu des polices */
        * {
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Variables corrigées pour lisibilité maximale */
        :root {
            --text-primary: #ffffff !important;
            --text-secondary: #ffffff !important;
            --text-muted: rgba(255, 255, 255, 0.9) !important;
            --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* CORRECTION GLOBALE - TOUS LES TEXTES */
        h1, h2, h3, h4, h5, h6, p, span, div, li, a, label, input, textarea, select {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* BOUTONS - LISIBILITÉ MAXIMALE */
        button, .btn, .button, .toolbar-btn, .demo-button, .cta-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        button:hover, .btn:hover, .button:hover {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        /* NAVIGATION - TOUJOURS VISIBLE */
        .nav-item, .nav-link, .navbar-nav a, .top-navbar a {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        .logo-text, .navbar-brand {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: bold !important;
        }

        /* CARTES ET CONTENEURS - CONTRASTE OPTIMAL */
        .card, .unified-card, .metric-card, .capability-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* TITRES ET SOUS-TITRES */
        .interface-title, .section-title, .card-title {
            color: #ffffff !important;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .interface-subtitle, .card-subtitle {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* STATISTIQUES ET MÉTRIQUES */
        .stat-number, .metric-value {
            color: #ff6b6b !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .stat-label, .metric-label {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* LISTES ET DESCRIPTIONS */
        .capability-features li, .spec-list li {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: var(--text-shadow) !important;
        }

        .capability-description, .card-text {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FORMULAIRES */
        input, select, textarea {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* TABLEAUX */
        table, th, td {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        th {
            background: rgba(255, 105, 180, 0.2) !important;
            font-weight: bold !important;
        }

        /* ALERTES ET NOTIFICATIONS */
        .alert, .notification {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FOOTER */
        .footer, .footer-content {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTIONS SPÉCIFIQUES POUR FONDS CLAIRS */
        .tech-specs, .capabilities-section[style*="background: #f"] {
            color: #333 !important;
        }

        .tech-specs *, .capabilities-section[style*="background: #f"] * {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
        }

        /* ICÔNES */
        .fas, .far, .fab {
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTION POUR LES ÉLÉMENTS SPÉCIAUX */
        .progress-bar, .slider {
            background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
        }

        /* AMÉLIORATION DU CONTRASTE POUR LES LIENS */
        a {
            color: #ff69b4 !important;
            text-shadow: var(--text-shadow) !important;
        }

        a:hover {
            color: #ff1493 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-brain louna-header-icon"></i>
                <h1>Visualisation 3D du Cerveau</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="louna-nav-btn">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Visualisation active</span>
            </div>
        </div>
    </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item active">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Visualisation du Cerveau</h1>
                <p class="interface-subtitle">Représentation 3D de la mémoire thermique</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="memory-status-indicator"></div>
                <span class="status-text" id="memory-status-text">Chargement...</span>

                <button class="action-button" id="toggle-animation-btn">
                    <i class="fas fa-pause"></i> Pause
                </button>
                <button class="action-button" id="reset-view-btn">
                    <i class="fas fa-undo"></i> Réinitialiser la vue
                </button>
                <a href="/futuristic-interface.html" class="action-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
            </div>
        </div>

        <!-- Conteneur du cerveau 3D -->
        <div id="brain-container">
            <!-- Boutons de contrôle des panneaux -->
            <div class="panel-toggle-buttons">
                <div class="panel-toggle-btn active" id="toggle-qi-panel" title="Afficher/Masquer QI & Neurones">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="panel-toggle-btn active" id="toggle-emotional-panel" title="Afficher/Masquer État Émotionnel">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="panel-toggle-btn active" id="toggle-memory-panel" title="Afficher/Masquer Flux Mémoire">
                    <i class="fas fa-memory"></i>
                </div>
                <div class="panel-toggle-btn" id="fullscreen-btn" title="Mode Plein Écran">
                    <i class="fas fa-expand"></i>
                </div>
            </div>

            <!-- Panneau d'informations -->
            <div id="info-panel">
                <h3>Informations</h3>
                <p><strong>Température CPU:</strong> <span id="cpu-temp">0°C</span></p>
                <p><strong>Température GPU:</strong> <span id="gpu-temp">0°C</span></p>
                <p><strong>Température normalisée:</strong> <span id="normalized-temp">0.5</span></p>
                <p><strong>Entrées totales:</strong> <span id="total-entries">0</span></p>
                <p><strong>Température moyenne:</strong> <span id="avg-temperature">0.5</span></p>
            </div>

            <!-- QI et Neurones -->
            <div id="qi-neuron-panel">
                <h3>🧠 QI & Neurones</h3>
                <div class="qi-section">
                    <h4>Intelligence (QI)</h4>
                    <div class="stat-item">
                        <span class="stat-label">QI Actuel:</span>
                        <span class="stat-value qi-value" id="current-qi">148</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Niveau Cognitif:</span>
                        <span class="stat-value level-value" id="cognitive-level">1</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Points XP:</span>
                        <span class="stat-value xp-value" id="experience-points">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Bonus Apprentissage:</span>
                        <span class="stat-value bonus-value" id="learning-bonus">0%</span>
                    </div>
                </div>

                <div class="neuron-section">
                    <h4>Neurones</h4>
                    <div class="stat-item">
                        <span class="stat-label">Total:</span>
                        <span class="stat-value neuron-total" id="total-neurons">71</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Actifs:</span>
                        <span class="stat-value neuron-active" id="active-neurons">45</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Efficacité:</span>
                        <span class="stat-value efficiency-value" id="neuron-efficiency">85%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Santé:</span>
                        <span class="stat-value health-value" id="neuron-health">92%</span>
                    </div>
                </div>

                <div class="network-section">
                    <h4>Réseaux Neuronaux</h4>
                    <div class="network-grid">
                        <div class="network-item">
                            <span class="network-label">Sensoriel:</span>
                            <span class="network-count" id="sensory-count">15</span>
                        </div>
                        <div class="network-item">
                            <span class="network-label">Travail:</span>
                            <span class="network-count" id="working-count">12</span>
                        </div>
                        <div class="network-item">
                            <span class="network-label">Long Terme:</span>
                            <span class="network-count" id="longterm-count">20</span>
                        </div>
                        <div class="network-item">
                            <span class="network-label">Émotionnel:</span>
                            <span class="network-count" id="emotional-count">10</span>
                        </div>
                        <div class="network-item">
                            <span class="network-label">Exécutif:</span>
                            <span class="network-count" id="executive-count">8</span>
                        </div>
                        <div class="network-item">
                            <span class="network-label">Créatif:</span>
                            <span class="network-count" id="creative-count">6</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- État Émotionnel et Pensées -->
            <div id="emotional-panel">
                <h3>💭 État Émotionnel</h3>

                <div class="mood-section">
                    <h4>Humeur Actuelle</h4>
                    <div class="mood-display">
                        <div class="mood-indicator" id="mood-indicator">
                            <span class="mood-emoji" id="mood-emoji">🤔</span>
                            <span class="mood-text" id="mood-text">Curieux</span>
                        </div>
                        <div class="mood-intensity">
                            <span class="intensity-label">Intensité:</span>
                            <div class="intensity-bar">
                                <div class="intensity-fill" id="intensity-fill" style="width: 70%"></div>
                            </div>
                            <span class="intensity-value" id="intensity-value">70%</span>
                        </div>
                    </div>
                </div>

                <div class="emotions-section">
                    <h4>Émotions</h4>
                    <div class="emotion-grid">
                        <div class="emotion-item">
                            <span class="emotion-label">😊 Bonheur:</span>
                            <span class="emotion-value" id="happiness-value">70%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">🔍 Curiosité:</span>
                            <span class="emotion-value" id="curiosity-value">80%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">💪 Confiance:</span>
                            <span class="emotion-value" id="confidence-value">60%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">⚡ Énergie:</span>
                            <span class="emotion-value" id="energy-value">75%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">🎯 Focus:</span>
                            <span class="emotion-value" id="focus-value">65%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">🎨 Créativité:</span>
                            <span class="emotion-value" id="creativity-value">70%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">😰 Stress:</span>
                            <span class="emotion-value stress-value" id="stress-value">20%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-label">😴 Fatigue:</span>
                            <span class="emotion-value fatigue-value" id="fatigue-value">30%</span>
                        </div>
                    </div>
                </div>

                <div class="thoughts-section">
                    <h4>Pensées Actuelles</h4>
                    <div class="thoughts-container" id="thoughts-container">
                        <div class="thought-item">
                            <span class="thought-time">14:32</span>
                            <span class="thought-text">Je me demande quelles nouvelles connaissances je vais découvrir...</span>
                        </div>
                    </div>
                </div>

                <div class="biorhythm-section">
                    <h4>Biorhythmes</h4>
                    <div class="biorhythm-item">
                        <span class="biorhythm-label">🌙 Rythme Circadien:</span>
                        <span class="biorhythm-value" id="circadian-value">50%</span>
                    </div>
                    <div class="biorhythm-item">
                        <span class="biorhythm-label">🧘 Stabilité:</span>
                        <span class="biorhythm-value" id="stability-value">80%</span>
                    </div>
                </div>

                <div class="interaction-section">
                    <h4>Interactions</h4>
                    <div class="interaction-buttons">
                        <button class="emotion-btn learning-btn" onclick="simulateEmotionalReaction('learning')">
                            📚 Apprentissage
                        </button>
                        <button class="emotion-btn success-btn" onclick="simulateEmotionalReaction('success')">
                            🎉 Succès
                        </button>
                        <button class="emotion-btn challenge-btn" onclick="simulateEmotionalReaction('challenge')">
                            💪 Défi
                        </button>
                        <button class="emotion-btn rest-btn" onclick="simulateEmotionalReaction('rest')">
                            😴 Repos
                        </button>
                        <button class="emotion-btn creative-btn" onclick="simulateEmotionalReaction('creative')">
                            🎨 Créativité
                        </button>
                        <button class="emotion-btn update-btn" onclick="updateQINeuronDisplay()">
                            🔄 Actualiser
                        </button>
                    </div>
                </div>
            </div>

            <!-- Flux de mémoire -->
            <div id="memory-flow">
                <h3>Flux de Mémoire</h3>
                <div class="flow-item">
                    <span class="flow-label">Instantanée → Court terme:</span>
                    <span class="flow-value negative" id="flow-instant-short">-2</span>
                </div>
                <div class="flow-item">
                    <span class="flow-label">Court terme → Travail:</span>
                    <span class="flow-value negative" id="flow-short-working">-3</span>
                </div>
                <div class="flow-item">
                    <span class="flow-label">Travail → Moyen terme:</span>
                    <span class="flow-value negative" id="flow-working-medium">-5</span>
                </div>
                <div class="flow-item">
                    <span class="flow-label">Moyen terme → Long terme:</span>
                    <span class="flow-value negative" id="flow-medium-long">-2</span>
                </div>
                <div class="flow-item">
                    <span class="flow-label">Long terme → Rêves:</span>
                    <span class="flow-value negative" id="flow-long-dream">-1</span>
                </div>
                <div class="flow-item">
                    <span class="flow-label">Nouvelles entrées:</span>
                    <span class="flow-value positive" id="flow-new-entries">+4</span>
                </div>
            </div>

            <!-- Contrôles du cerveau -->
            <div id="brain-controls">
                <div class="control-group">
                    <span class="control-label">Vitesse d'animation:</span>
                    <input type="range" id="animation-speed" class="control-slider" min="0.1" max="2" step="0.1" value="1">
                    <span class="control-value" id="animation-speed-value">1.0x</span>
                </div>
                <div class="control-group">
                    <span class="control-label">Intensité des couleurs:</span>
                    <input type="range" id="color-intensity" class="control-slider" min="0.5" max="2" step="0.1" value="1">
                    <span class="control-value" id="color-intensity-value">1.0x</span>
                </div>
                <div class="control-group">
                    <span class="control-label">Taille des particules:</span>
                    <input type="range" id="particle-size" class="control-slider" min="0.5" max="2" step="0.1" value="1">
                    <span class="control-value" id="particle-size-value">1.0x</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/brain-visualization.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialiser la visualisation du cerveau
            initBrainVisualization();

            // Ajouter des écouteurs d'événements pour les contrôles
            document.getElementById('toggle-animation-btn').addEventListener('click', toggleAnimation);
            document.getElementById('reset-view-btn').addEventListener('click', resetView);
            document.getElementById('animation-speed').addEventListener('input', updateAnimationSpeed);
            document.getElementById('color-intensity').addEventListener('input', updateColorIntensity);
            document.getElementById('particle-size').addEventListener('input', updateParticleSize);
            document.getElementById('refresh-btn').addEventListener('click', refreshVisualization);

            // Contrôles des panneaux
            setupPanelControls();
        });

        function setupPanelControls() {
            // Contrôle du panneau QI
            document.getElementById('toggle-qi-panel').addEventListener('click', function() {
                const panel = document.getElementById('qi-neuron-panel');
                const btn = this;

                if (panel.style.display === 'none') {
                    panel.style.display = 'block';
                    btn.classList.add('active');
                } else {
                    panel.style.display = 'none';
                    btn.classList.remove('active');
                }
            });

            // Contrôle du panneau émotionnel
            document.getElementById('toggle-emotional-panel').addEventListener('click', function() {
                const panel = document.getElementById('emotional-panel');
                const btn = this;

                if (panel.style.display === 'none') {
                    panel.style.display = 'block';
                    btn.classList.add('active');
                } else {
                    panel.style.display = 'none';
                    btn.classList.remove('active');
                }
            });

            // Contrôle du panneau mémoire
            document.getElementById('toggle-memory-panel').addEventListener('click', function() {
                const panel = document.getElementById('memory-flow');
                const btn = this;

                if (panel.style.display === 'none') {
                    panel.style.display = 'block';
                    btn.classList.add('active');
                } else {
                    panel.style.display = 'none';
                    btn.classList.remove('active');
                }
            });

            // Mode plein écran
            document.getElementById('fullscreen-btn').addEventListener('click', function() {
                const container = document.getElementById('brain-container');
                const btn = this;

                if (!document.fullscreenElement) {
                    container.requestFullscreen().then(() => {
                        btn.classList.add('active');
                        btn.innerHTML = '<i class="fas fa-compress"></i>';
                        btn.title = 'Quitter le plein écran';
                    });
                } else {
                    document.exitFullscreen().then(() => {
                        btn.classList.remove('active');
                        btn.innerHTML = '<i class="fas fa-expand"></i>';
                        btn.title = 'Mode Plein Écran';
                    });
                }
            });

            // Écouter les changements de plein écran
            document.addEventListener('fullscreenchange', function() {
                const btn = document.getElementById('fullscreen-btn');
                if (!document.fullscreenElement) {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-expand"></i>';
                    btn.title = 'Mode Plein Écran';
                }
            });
        }
    </script>
  <script src="js/native-app.js"></script>
<script src="/js/auto-init-fixes.js"></script>
<script src="/js/qi-manager.js"></script>
</body>
</html>
