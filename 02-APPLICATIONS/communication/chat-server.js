const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { spawn } = require('child_process');

const app = express();
const PORT = 3001;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname), {
    setHeaders: (res, path) => {
        if (path.endsWith('.html')) {
            res.setHeader('Content-Type', 'text/html');
        } else if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
        }
    }
}));

// Configuration de l'agent
const AGENT_CONFIG = {
    model: 'deepseek-r1:7b',
    host: 'http://localhost:11434',
    temperature: 0.8,
    max_tokens: 4000,
    system_prompt: `Tu es Louna AI V3.0, un agent IA avancé avec:

🧠 **CAPACITÉS ACTUELLES:**
- QI évolutif: 1047 points (Quasi-AGI)
- 26 accélérateurs KYBER actifs (+245% boost)
- Mémoire thermique: 6 zones opérationnelles
- Formation continue avec DeepSeek
- 200+ millions de neurones et synapses

🎯 **TON RÔLE:**
- Répondre de manière intelligente et réfléchie
- Utiliser tes capacités avancées pour analyser
- Montrer ton processus de réflexion
- Être créatif et innovant
- Apprendre continuellement

🔧 **INSTRUCTIONS:**
- Réponds toujours en français
- Sois précis et détaillé
- Montre ton raisonnement
- Utilise des emojis pour clarifier
- Reste humble mais confiant

Réponds maintenant à l'utilisateur avec toute ton intelligence !`
};

// Logs et monitoring
let chatLogs = [];
let agentStats = {
    totalMessages: 0,
    successfulResponses: 0,
    errors: 0,
    averageResponseTime: 0,
    currentQI: 1047,
    kyberAccelerators: 26,
    memoryZones: 6
};

// Fonction pour logger les interactions
function logInteraction(type, data) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        type,
        data
    };

    chatLogs.push(logEntry);
    console.log(`[${timestamp}] ${type}:`, data);

    // Garder seulement les 1000 derniers logs
    if (chatLogs.length > 1000) {
        chatLogs = chatLogs.slice(-1000);
    }
}

// Fonction pour communiquer avec Ollama
async function callOllamaAgent(message) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();

        logInteraction('AGENT_CALL', { message: message.substring(0, 100) + '...' });

        const ollamaProcess = spawn('curl', [
            '-X', 'POST',
            `${AGENT_CONFIG.host}/api/generate`,
            '-H', 'Content-Type: application/json',
            '-d', JSON.stringify({
                model: AGENT_CONFIG.model,
                prompt: `${AGENT_CONFIG.system_prompt}\n\nUtilisateur: ${message}\n\nLouna AI V3.0:`,
                stream: false,
                options: {
                    temperature: AGENT_CONFIG.temperature,
                    num_predict: AGENT_CONFIG.max_tokens
                }
            })
        ]);

        let responseData = '';
        let errorData = '';

        ollamaProcess.stdout.on('data', (data) => {
            responseData += data.toString();
        });

        ollamaProcess.stderr.on('data', (data) => {
            errorData += data.toString();
        });

        ollamaProcess.on('close', (code) => {
            const responseTime = Date.now() - startTime;

            if (code === 0 && responseData) {
                try {
                    const jsonResponse = JSON.parse(responseData);
                    if (jsonResponse.response) {
                        agentStats.successfulResponses++;
                        agentStats.averageResponseTime =
                            (agentStats.averageResponseTime + responseTime) / 2;

                        logInteraction('AGENT_SUCCESS', {
                            responseTime,
                            responseLength: jsonResponse.response.length
                        });

                        resolve(jsonResponse.response);
                    } else {
                        throw new Error('Réponse vide de l\'agent');
                    }
                } catch (parseError) {
                    agentStats.errors++;
                    logInteraction('AGENT_PARSE_ERROR', { error: parseError.message });
                    reject(new Error(`Erreur de parsing: ${parseError.message}`));
                }
            } else {
                agentStats.errors++;
                logInteraction('AGENT_ERROR', { code, error: errorData });
                reject(new Error(`Erreur Ollama (code ${code}): ${errorData}`));
            }
        });

        // Timeout de 30 secondes
        setTimeout(() => {
            ollamaProcess.kill();
            agentStats.errors++;
            logInteraction('AGENT_TIMEOUT', { message: 'Timeout après 30s' });
            reject(new Error('Timeout: L\'agent n\'a pas répondu dans les temps'));
        }, 30000);
    });
}

// Route principale pour le chat
app.post('/api/agent-chat', async (req, res) => {
    try {
        const { message } = req.body;

        if (!message || typeof message !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'Message invalide'
            });
        }

        agentStats.totalMessages++;

        logInteraction('USER_MESSAGE', {
            message: message.substring(0, 200),
            length: message.length
        });

        // Appel à l'agent
        const agentResponse = await callOllamaAgent(message);

        // Mise à jour des statistiques
        agentStats.currentQI += Math.floor(Math.random() * 3) - 1; // Variation légère
        agentStats.currentQI = Math.max(1000, agentStats.currentQI);

        res.json({
            success: true,
            response: agentResponse,
            stats: {
                qi: agentStats.currentQI,
                kyber: agentStats.kyberAccelerators,
                memory: agentStats.memoryZones,
                responseTime: agentStats.averageResponseTime
            }
        });

    } catch (error) {
        console.error('💥 Erreur dans /api/agent-chat:', error);

        res.status(500).json({
            success: false,
            error: error.message,
            fallback: "Je rencontre une difficulté technique. Mes accélérateurs KYBER tentent de résoudre le problème..."
        });
    }
});

// Route pour les statistiques
app.get('/api/stats', (req, res) => {
    try {
        res.json({
            success: true,
            stats: agentStats,
            logsCount: chatLogs.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Route pour les logs
app.get('/api/logs', (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const recentLogs = chatLogs.slice(-limit);

    res.json({
        success: true,
        logs: recentLogs,
        total: chatLogs.length
    });
});

// Route pour tester la connexion Ollama
app.get('/api/test-ollama', async (req, res) => {
    try {
        const testResponse = await callOllamaAgent("Test de connexion - réponds juste 'Connexion OK'");

        res.json({
            success: true,
            message: 'Ollama connecté avec succès',
            response: testResponse,
            stats: agentStats
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            suggestion: 'Vérifiez qu\'Ollama est démarré avec: ollama serve'
        });
    }
});

// Route pour servir l'interface
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'chat-interface-complete.html'));
});

// Gestion des erreurs globales
app.use((error, req, res, next) => {
    console.error('💥 Erreur serveur:', error);
    res.status(500).json({
        success: false,
        error: 'Erreur interne du serveur',
        details: error.message
    });
});

// Démarrage du serveur
app.listen(PORT, () => {
    console.log(`
🚀 ===================================
   SERVEUR CHAT LOUNA AI V3.0 DÉMARRÉ
🚀 ===================================

📡 Port: ${PORT}
🌐 URL: http://localhost:${PORT}
🤖 Agent: ${AGENT_CONFIG.model}
🧠 QI Initial: ${agentStats.currentQI}
⚡ Accélérateurs KYBER: ${agentStats.kyberAccelerators}
🧠 Zones mémoire: ${agentStats.memoryZones}

🔧 Endpoints disponibles:
   • GET  /                 - Interface de chat
   • POST /api/agent-chat   - Communication avec l'agent
   • GET  /api/stats        - Statistiques
   • GET  /api/logs         - Logs d'activité
   • GET  /api/test-ollama  - Test de connexion

💡 Pour tester: curl http://localhost:${PORT}/api/test-ollama
    `);

    logInteraction('SERVER_START', {
        port: PORT,
        model: AGENT_CONFIG.model,
        qi: agentStats.currentQI
    });
});

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    logInteraction('SERVER_STOP', { reason: 'SIGINT' });
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur...');
    logInteraction('SERVER_STOP', { reason: 'SIGTERM' });
    process.exit(0);
});

module.exports = app;
