<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖥️ TeamViewer - Contrôle à Distance | Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0066CC 0%, #004499 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .connection-info {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .connection-id {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
            margin: 10px 0;
            letter-spacing: 2px;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .device-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .device-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .status-offline { background: #f44336; }
        .status-connecting { background: #FF9800; animation: pulse 1s infinite; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .device-info {
            margin-bottom: 15px;
        }

        .device-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .device-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .remote-screen {
            background: rgba(0,0,0,0.5);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed rgba(255,255,255,0.3);
        }

        .screen-placeholder {
            color: rgba(255,255,255,0.5);
            font-size: 1.2rem;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quick-action {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-action:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.7);
            transform: translateY(-2px);
        }

        .security-panel {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .security-warning {
            color: #FFD700;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .session-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }

        .performance-meter {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .meter-bar {
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .meter-fill {
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goToHome()">
        <i class="fas fa-arrow-left"></i> Retour
    </button>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-desktop"></i> TeamViewer - Contrôle à Distance</h1>
            <p>Accès sécurisé à Louna AI depuis n'importe où</p>
        </div>

        <div class="connection-info">
            <h3><i class="fas fa-id-card"></i> Informations de Connexion</h3>
            <div>ID TeamViewer : <span class="connection-id" id="teamviewer-id">1 234 567 890</span></div>
            <div>Mot de passe : <span style="font-family: monospace; background: rgba(0,0,0,0.3); padding: 5px 10px; border-radius: 5px;">LounaAI2025</span></div>
            <button class="btn btn-secondary" onclick="generateNewPassword()">
                <i class="fas fa-refresh"></i> Nouveau mot de passe
            </button>
        </div>

        <div class="devices-grid">
            <!-- Ordinateur Principal -->
            <div class="device-card">
                <div class="device-status">
                    <div class="status-dot" id="main-pc-status"></div>
                    <span><strong>Ordinateur Principal</strong></span>
                </div>
                <div class="device-info">
                    <div class="device-name">MacBook Pro M4 - Louna AI</div>
                    <div class="device-details">
                        macOS 15.3.1 • QI: 235 • Mémoire: 32GB<br>
                        IP: ************* • Dernière connexion: Maintenant
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="connectDevice('main-pc')">
                        <i class="fas fa-play"></i> Connecter
                    </button>
                    <button class="btn btn-secondary" onclick="viewDevice('main-pc')">
                        <i class="fas fa-eye"></i> Voir
                    </button>
                    <button class="btn btn-warning" onclick="controlDevice('main-pc')">
                        <i class="fas fa-mouse-pointer"></i> Contrôler
                    </button>
                </div>
            </div>

            <!-- Mobile -->
            <div class="device-card">
                <div class="device-status">
                    <div class="status-dot status-offline" id="mobile-status"></div>
                    <span><strong>Appareil Mobile</strong></span>
                </div>
                <div class="device-info">
                    <div class="device-name">iPhone/Android - Louna Mobile</div>
                    <div class="device-details">
                        iOS/Android • Application Louna Mobile<br>
                        Connexion: WiFi/4G • Statut: Hors ligne
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="connectDevice('mobile')">
                        <i class="fas fa-mobile-alt"></i> Connecter
                    </button>
                    <button class="btn btn-secondary" onclick="installMobileApp()">
                        <i class="fas fa-download"></i> Installer App
                    </button>
                </div>
            </div>

            <!-- Serveur Backup -->
            <div class="device-card">
                <div class="device-status">
                    <div class="status-dot status-connecting" id="backup-status"></div>
                    <span><strong>Serveur de Sauvegarde</strong></span>
                </div>
                <div class="device-info">
                    <div class="device-name">Serveur Cloud - Backup Louna</div>
                    <div class="device-details">
                        Ubuntu Server • Sauvegarde automatique<br>
                        Stockage: 2TB • Dernière sync: 5 min
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="connectDevice('backup')">
                        <i class="fas fa-server"></i> Connecter
                    </button>
                    <button class="btn btn-secondary" onclick="syncBackup()">
                        <i class="fas fa-sync"></i> Synchroniser
                    </button>
                </div>
            </div>
        </div>

        <!-- Écran de contrôle à distance -->
        <div class="remote-screen" id="remote-screen">
            <div class="screen-placeholder">
                <i class="fas fa-desktop" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
                Cliquez sur "Connecter" pour démarrer une session de contrôle à distance
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="quick-actions">
            <div class="quick-action" onclick="quickAction('wake')">
                <i class="fas fa-sun"></i><br>
                Réveiller Louna
            </div>
            <div class="quick-action" onclick="quickAction('sleep')">
                <i class="fas fa-moon"></i><br>
                Mettre en veille
            </div>
            <div class="quick-action" onclick="quickAction('restart')">
                <i class="fas fa-redo"></i><br>
                Redémarrer
            </div>
            <div class="quick-action" onclick="quickAction('status')">
                <i class="fas fa-heartbeat"></i><br>
                Vérifier statut
            </div>
            <div class="quick-action" onclick="quickAction('backup')">
                <i class="fas fa-save"></i><br>
                Sauvegarde
            </div>
            <div class="quick-action" onclick="quickAction('logs')">
                <i class="fas fa-list"></i><br>
                Voir logs
            </div>
        </div>

        <!-- Informations de session -->
        <div class="session-info">
            <h4><i class="fas fa-info-circle"></i> Informations de Session</h4>
            <div class="info-row">
                <span>Statut de connexion :</span>
                <span id="connection-status">Déconnecté</span>
            </div>
            <div class="info-row">
                <span>Durée de session :</span>
                <span id="session-duration">00:00:00</span>
            </div>
            <div class="info-row">
                <span>Qualité de connexion :</span>
                <span id="connection-quality">Excellente</span>
            </div>
            <div class="info-row">
                <span>Bande passante :</span>
                <span id="bandwidth">0 Mbps</span>
            </div>
        </div>

        <!-- Métriques de performance -->
        <div class="performance-meter">
            <h4><i class="fas fa-tachometer-alt"></i> Performance Système</h4>
            <div>
                CPU: <span id="cpu-usage">25%</span>
                <div class="meter-bar">
                    <div class="meter-fill" style="width: 25%"></div>
                </div>
            </div>
            <div>
                Mémoire: <span id="memory-usage">45%</span>
                <div class="meter-bar">
                    <div class="meter-fill" style="width: 45%"></div>
                </div>
            </div>
            <div>
                Réseau: <span id="network-usage">12%</span>
                <div class="meter-bar">
                    <div class="meter-fill" style="width: 12%"></div>
                </div>
            </div>
        </div>

        <!-- Panneau de sécurité -->
        <div class="security-panel">
            <div class="security-warning">
                <i class="fas fa-shield-alt"></i> Sécurité et Confidentialité
            </div>
            <p>• Toutes les connexions sont chiffrées end-to-end</p>
            <p>• Authentification à deux facteurs activée</p>
            <p>• Logs de sécurité automatiques</p>
            <p>• Accès limité aux fonctions critiques de Louna AI</p>
            <button class="btn btn-danger" onclick="emergencyDisconnect()">
                <i class="fas fa-power-off"></i> Déconnexion d'urgence
            </button>
        </div>
    </div>

    <script>
        let isConnected = false;
        let sessionStartTime = null;
        let sessionTimer = null;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = '/';
        }

        // Générer nouveau mot de passe
        function generateNewPassword() {
            const passwords = ['LounaSecure2025', 'AI-TeamView-235', 'SecureLouna2025', 'TeamAI-2025-QI'];
            const newPassword = passwords[Math.floor(Math.random() * passwords.length)];
            
            const passwordElement = document.querySelector('.connection-info span[style*="monospace"]');
            passwordElement.textContent = newPassword;
            
            showNotification('🔐 Nouveau mot de passe généré !', 'success');
        }

        // Connecter à un appareil
        function connectDevice(deviceType) {
            const statusElement = document.getElementById(`${deviceType}-status`);
            const screenElement = document.getElementById('remote-screen');
            
            statusElement.className = 'status-dot status-connecting';
            showNotification('🔄 Connexion en cours...', 'info');
            
            setTimeout(() => {
                statusElement.className = 'status-dot';
                isConnected = true;
                startSession();
                
                screenElement.innerHTML = `
                    <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1e3c72, #2a5298); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div style="text-align: center;">
                            <i class="fas fa-desktop" style="font-size: 48px; margin-bottom: 15px;"></i>
                            <h3>Session Active - ${deviceType.toUpperCase()}</h3>
                            <p>Contrôle à distance établi • QI Louna: 235</p>
                            <button class="btn btn-danger" onclick="disconnectSession()">
                                <i class="fas fa-times"></i> Déconnecter
                            </button>
                        </div>
                    </div>
                `;
                
                showNotification(`✅ Connecté à ${deviceType} !`, 'success');
                updateConnectionStatus('Connecté');
            }, 2000);
        }

        // Voir appareil
        function viewDevice(deviceType) {
            showNotification(`👁️ Mode visualisation activé pour ${deviceType}`, 'info');
        }

        // Contrôler appareil
        function controlDevice(deviceType) {
            if (!isConnected) {
                showNotification('❌ Veuillez vous connecter d\'abord', 'error');
                return;
            }
            showNotification(`🖱️ Contrôle total activé pour ${deviceType}`, 'success');
        }

        // Installer app mobile
        function installMobileApp() {
            showNotification('📱 Lien d\'installation envoyé sur votre mobile', 'info');
        }

        // Synchroniser backup
        function syncBackup() {
            showNotification('🔄 Synchronisation backup en cours...', 'info');
            setTimeout(() => {
                showNotification('✅ Sauvegarde synchronisée !', 'success');
            }, 3000);
        }

        // Actions rapides
        function quickAction(action) {
            const actions = {
                'wake': '🌅 Louna AI réveillé',
                'sleep': '💤 Louna AI en veille',
                'restart': '🔄 Redémarrage en cours...',
                'status': '📊 Statut: En ligne • QI: 235 • Système optimal',
                'backup': '💾 Sauvegarde démarrée',
                'logs': '📋 Logs système affichés'
            };
            
            showNotification(actions[action] || 'Action exécutée', 'success');
        }

        // Démarrer session
        function startSession() {
            sessionStartTime = new Date();
            sessionTimer = setInterval(updateSessionDuration, 1000);
            updateConnectionStatus('Connecté');
        }

        // Déconnecter session
        function disconnectSession() {
            isConnected = false;
            if (sessionTimer) {
                clearInterval(sessionTimer);
                sessionTimer = null;
            }
            
            document.getElementById('remote-screen').innerHTML = `
                <div class="screen-placeholder">
                    <i class="fas fa-desktop" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
                    Session terminée. Cliquez sur "Connecter" pour démarrer une nouvelle session
                </div>
            `;
            
            updateConnectionStatus('Déconnecté');
            showNotification('🔌 Session terminée', 'info');
        }

        // Déconnexion d'urgence
        function emergencyDisconnect() {
            disconnectSession();
            showNotification('🚨 DÉCONNEXION D\'URGENCE ACTIVÉE !', 'warning');
        }

        // Mettre à jour durée de session
        function updateSessionDuration() {
            if (!sessionStartTime) return;
            
            const now = new Date();
            const duration = Math.floor((now - sessionStartTime) / 1000);
            const hours = Math.floor(duration / 3600);
            const minutes = Math.floor((duration % 3600) / 60);
            const seconds = duration % 60;
            
            document.getElementById('session-duration').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Mettre à jour statut de connexion
        function updateConnectionStatus(status) {
            document.getElementById('connection-status').textContent = status;
        }

        // Afficher notification
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 1000;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                max-width: 300px;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        // Simulation des métriques
        function updateMetrics() {
            const cpu = Math.floor(Math.random() * 30) + 20;
            const memory = Math.floor(Math.random() * 20) + 40;
            const network = Math.floor(Math.random() * 15) + 5;
            
            document.getElementById('cpu-usage').textContent = cpu + '%';
            document.getElementById('memory-usage').textContent = memory + '%';
            document.getElementById('network-usage').textContent = network + '%';
            
            document.querySelector('.performance-meter .meter-fill').style.width = cpu + '%';
            document.querySelectorAll('.performance-meter .meter-fill')[1].style.width = memory + '%';
            document.querySelectorAll('.performance-meter .meter-fill')[2].style.width = network + '%';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🖥️ TeamViewer Control initialisé');
            
            // Générer ID TeamViewer aléatoire
            const randomId = Math.floor(Math.random() * 9000000000) + 1000000000;
            document.getElementById('teamviewer-id').textContent = randomId.toString().replace(/(\d{3})(\d{3})(\d{3})(\d{1})/, '$1 $2 $3 $4');
            
            // Mettre à jour les métriques toutes les 3 secondes
            setInterval(updateMetrics, 3000);
            updateMetrics();
        });
    </script>
</body>
</html>
