<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat Intelligent avec Réflexions - Louna AI V3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        /* Barre de navigation supérieure */
        .top-nav {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            padding: 8px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            font-weight: bold;
            color: #000;
            box-shadow: 0 2px 10px rgba(0, 255, 136, 0.3);
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-item {
            padding: 5px 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #000;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        .qi-display {
            background: rgba(0, 255, 0, 0.3);
            border: 1px solid #00ff00;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        /* Container principal */
        .main-container {
            display: flex;
            height: calc(100vh - 50px);
        }

        /* Menu latéral */
        .sidebar {
            width: 250px;
            background: rgba(0, 0, 0, 0.8);
            border-right: 2px solid #4ecdc4;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #4ecdc4;
            margin-bottom: 15px;
            font-size: 14px;
            text-transform: uppercase;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #fff;
            font-size: 13px;
        }

        .sidebar-item:hover {
            background: rgba(78, 205, 196, 0.3);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: #000;
            font-weight: bold;
        }

        /* Zone de chat principale */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
        }

        /* En-tête du chat */
        .chat-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            font-weight: bold;
            color: #4ecdc4;
        }

        .chat-status {
            display: flex;
            gap: 15px;
            font-size: 12px;
        }

        .status-item {
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
        }

        /* Zone des messages */
        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 15px;
            border-radius: 15px;
            word-wrap: break-word;
            position: relative;
        }

        .message.user {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            margin-left: auto;
            color: #fff;
        }

        .message.agent {
            background: linear-gradient(135deg, #00ff88, #00ccff);
            color: #000;
            margin-right: auto;
        }

        .message.system {
            background: linear-gradient(135deg, #ffaa00, #ff6600);
            color: #000;
            margin: 0 auto;
            text-align: center;
            max-width: 90%;
        }

        .message.error {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            color: #fff;
            margin-right: auto;
            border: 2px solid #ff0000;
        }

        .message-time {
            font-size: 10px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .typing-indicator {
            background: rgba(255, 255, 0, 0.2);
            border: 2px solid #ffff00;
            animation: pulse 1.5s infinite;
            margin-right: auto;
        }

        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        /* Zone de saisie */
        .input-area {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-top: 2px solid #4ecdc4;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            padding: 15px;
            border: 2px solid #4ecdc4;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 14px;
            resize: vertical;
            min-height: 50px;
            max-height: 150px;
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .input-field:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #00ff88, #00ccff);
            color: #000;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Barre d'outils en bas */
        .bottom-toolbar {
            background: rgba(0, 0, 0, 0.9);
            padding: 10px 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .toolbar-item {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-decoration: none;
            color: #fff;
        }

        .toolbar-item:hover {
            background: rgba(78, 205, 196, 0.3);
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }

            .nav-item {
                padding: 3px 6px;
                font-size: 10px;
            }

            .message {
                max-width: 95%;
            }
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #00ff88, #00ccff);
        }
    </style>
</head>
<body>
    <!-- Barre de navigation supérieure -->
    <div class="top-nav">
        <div class="nav-left">
            <span class="nav-item">💬 Chat Intelligent</span>
            <span class="nav-item">🔄 Génération</span>
            <span class="nav-item">🔧 Développement</span>
            <span class="nav-item">📊 Analyse</span>
            <span class="nav-item">🔍 Recherche Web</span>
            <span class="nav-item">🔄 Reconnaissance</span>
            <span class="nav-item">📚 Apprentissage</span>
            <span class="nav-item">🧠 Mémoire Thermique</span>
            <span class="nav-item">📊 Monitoring</span>
            <span class="nav-item">⚙️ Paramètres</span>
            <span class="nav-item">🔒 Sécurité</span>
            <span class="nav-item">🌐 VPN</span>
            <span class="nav-item">🤖 Agents</span>
            <span class="nav-item">🏠 Accueil</span>
        </div>
        <div class="nav-right">
            <span class="qi-display" id="qiDisplay">🧠 QI: 1047</span>
            <span class="nav-item">Agent connecté: ✅ QI: 1047</span>
            <span class="nav-item">Mode Quasi-AGI</span>
        </div>
    </div>

    <!-- Container principal -->
    <div class="main-container">
        <!-- Menu latéral -->
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>PRINCIPAL</h3>
                <a href="#" class="sidebar-item active">
                    💬 Chat Intelligent
                </a>
                <a href="#" class="sidebar-item">
                    🤖 Louna Principal
                </a>
            </div>

            <div class="sidebar-section">
                <h3>🤖 DeepSeek Formation</h3>
                <a href="#" class="sidebar-item">
                    📚 Formation
                </a>
            </div>

            <div class="sidebar-section">
                <h3>🧠 Système</h3>
                <a href="/brain-monitoring-complete.html" class="sidebar-item">
                    🧠 Mémoire Thermique
                </a>
                <a href="#" class="sidebar-item">
                    📊 Monitoring
                </a>
                <a href="#" class="sidebar-item">
                    ⚙️ Paramètres
                </a>
            </div>

            <div class="sidebar-section">
                <h3>🔧 Développement</h3>
                <a href="#" class="sidebar-item">
                    ⚡ Éditeur de Code
                </a>
                <a href="#" class="sidebar-item">
                    🔍 Analyseur
                </a>
            </div>
        </div>

        <!-- Zone de chat principale -->
        <div class="chat-area">
            <!-- En-tête du chat -->
            <div class="chat-header">
                <div class="chat-title">
                    💬 Chat Intelligent avec Réflexions
                </div>
                <div class="chat-status">
                    <div class="status-item" id="statusQI">🧠 QI évolutif : 1047 (Quasi-AGI)</div>
                    <div class="status-item">🧠 Mémoire thermique : 6 zones opérationnelles</div>
                    <div class="status-item">⚡ Accélérateurs KYBER : 26/∞ actifs (+245% boost)</div>
                    <div class="status-item">🎓 Formation continue avec DeepSeek</div>
                </div>
            </div>

            <!-- Zone des messages -->
            <div class="messages-container" id="messagesContainer">
                <div class="message system">
                    🧠 **Comment puis-je vous aider ?** <br>
                    • Conversations intelligentes avec réflexions<br>
                    • Génération de contenu créatif<br>
                    • Analyse et développement<br>
                    • Formation et apprentissage<br><br>
                    Utilisez les boutons horizontaux pour naviguer entre les différentes fonctionnalités !
                    <div class="message-time">Système initialisé</div>
                </div>
            </div>

            <!-- Zone de saisie -->
            <div class="input-area">
                <div class="input-container">
                    <textarea
                        class="input-field"
                        id="messageInput"
                        placeholder="💬 Posez-moi une question ou demandez-moi d'analyser quelque chose... (Entrée pour envoyer)"
                        rows="2"
                    ></textarea>
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        📤 Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre d'outils en bas -->
    <div class="bottom-toolbar">
        <a href="#" class="toolbar-item">📷 Caméra</a>
        <a href="#" class="toolbar-item">📞 WhatsApp</a>
        <a href="#" class="toolbar-item">🖥️ TeamViewer</a>
        <a href="#" class="toolbar-item">📁 Fichier</a>
        <a href="#" class="toolbar-item">📤 Exporter</a>
        <a href="#" class="toolbar-item">🔄 Réflexions</a>
    </div>

    <!-- Système de calcul du QI -->
    <script src="qi-calculator.js"></script>

    <script>
        // Variables globales
        let isConnected = false;
        let isSending = false;
        let currentQI = 1047;
        let qiCalculator;

        // Éléments DOM
        const messagesContainer = document.getElementById('messagesContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const qiDisplay = document.getElementById('qiDisplay');
        const statusQI = document.getElementById('statusQI');

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Interface de chat complète initialisée');

            // Initialiser le calculateur de QI
            qiCalculator = new QICalculator();
            qiCalculator.loadMetrics();
            qiCalculator.startAutoUpdate(30000); // Mise à jour toutes les 30 secondes

            // Écouter les changements de QI
            qiCalculator.addListener((newQI, metrics) => {
                currentQI = newQI;
                updateQIDisplay();
                console.log(`🧠 QI mis à jour: ${newQI}`, metrics);
            });

            // QI initial
            currentQI = qiCalculator.currentQI;

            updateConnectionStatus(true);
            messageInput.focus();
            updateQIDisplay();
        });

        // Gestion de l'envoi avec Entrée
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Fonction pour mettre à jour le statut de connexion
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusText = connected ? '✅ Connecté' : '❌ Déconnecté';
            const statusColor = connected ? '#00ff00' : '#ff0000';

            // Mise à jour visuelle si nécessaire
        }

        // Fonction pour mettre à jour l'affichage du QI
        function updateQIDisplay() {
            qiDisplay.textContent = `🧠 QI: ${currentQI}`;
            statusQI.textContent = `🧠 QI évolutif : ${currentQI} (Quasi-AGI)`;
        }

        // Fonction pour ajouter un message
        function addMessage(content, type = 'agent', timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const time = timestamp || new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div>${content}</div>
                <div class="message-time">${time}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Fonction pour afficher l'indicateur de frappe
        function showTyping() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message typing-indicator';
            typingDiv.id = 'typing-indicator';
            typingDiv.innerHTML = '<div>🤖 Louna réfléchit et analyse...</div>';
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Fonction pour masquer l'indicateur de frappe
        function hideTyping() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Fonction principale pour envoyer un message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isSending) return;

            // Ajouter le message de l'utilisateur
            addMessage(message, 'user');
            messageInput.value = '';

            // Vérifier les commandes spéciales
            if (handleSpecialCommands(message)) {
                return; // Commande spéciale traitée, pas besoin d'envoyer à l'agent
            }

            // Désactiver l'envoi
            isSending = true;
            sendButton.disabled = true;
            sendButton.textContent = '⏳ Envoi...';

            // Afficher l'indicateur de frappe
            showTyping();

            const startTime = Date.now();

            try {
                console.log('📤 Envoi du message:', message);

                const response = await fetch('/api/agent-chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                console.log('📥 Réponse reçue:', data);

                hideTyping();

                if (data.success && data.response) {
                    addMessage(data.response, 'agent');
                    updateConnectionStatus(true);

                    // Enregistrer la réponse pour le calcul du QI
                    if (qiCalculator) {
                        const responseTime = (Date.now() - startTime) / 1000;
                        const quality = estimateResponseQuality(data.response);
                        qiCalculator.recordResponse(quality, responseTime, true, estimateComplexity(message));
                    }
                } else {
                    const errorMsg = data.error || 'Erreur inconnue';
                    addMessage(`❌ Erreur: ${errorMsg}`, 'error');
                    updateConnectionStatus(false);

                    // Enregistrer l'échec pour le calcul du QI
                    if (qiCalculator) {
                        const responseTime = (Date.now() - startTime) / 1000;
                        qiCalculator.recordResponse(0, responseTime, false, 1);
                    }
                }

            } catch (error) {
                console.error('💥 Erreur de connexion:', error);
                hideTyping();
                addMessage(`💥 Erreur de connexion: ${error.message}`, 'error');
                updateConnectionStatus(false);
            }

            // Réactiver l'envoi
            isSending = false;
            sendButton.disabled = false;
            sendButton.textContent = '📤 Envoyer';
            messageInput.focus();
        }

        // Fonctions d'évaluation pour le QI
        function estimateResponseQuality(response) {
            let quality = 5; // Base

            // Longueur appropriée
            if (response.length > 50 && response.length < 2000) quality += 1;

            // Présence d'emojis (engagement)
            if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(response)) quality += 0.5;

            // Structure (paragraphes, listes)
            if (response.includes('\n') || response.includes('•') || response.includes('-')) quality += 1;

            // Mots techniques/complexes
            const complexWords = ['algorithme', 'analyse', 'optimisation', 'intelligence', 'neuronal', 'quantique'];
            const hasComplexWords = complexWords.some(word => response.toLowerCase().includes(word));
            if (hasComplexWords) quality += 1;

            // Références à des concepts avancés
            if (response.includes('KYBER') || response.includes('mémoire thermique') || response.includes('QI')) quality += 1;

            // Créativité (métaphores, analogies)
            if (response.includes('comme') || response.includes('tel que') || response.includes('similaire')) quality += 0.5;

            return Math.min(10, quality);
        }

        function estimateComplexity(question) {
            let complexity = 1; // Base

            // Longueur de la question
            if (question.length > 100) complexity += 1;
            if (question.length > 200) complexity += 1;

            // Mots-clés complexes
            const complexKeywords = ['pourquoi', 'comment', 'expliquer', 'analyser', 'calculer', 'résoudre', 'optimiser'];
            const hasComplexKeywords = complexKeywords.some(word => question.toLowerCase().includes(word));
            if (hasComplexKeywords) complexity += 2;

            // Questions mathématiques/scientifiques
            if (/\d+|\+|\-|\*|\/|=|équation|formule|théorème/i.test(question)) complexity += 3;

            // Questions philosophiques/abstraites
            if (/sens|existence|conscience|réalité|vérité|morale/i.test(question)) complexity += 2;

            // Questions techniques
            if (/code|programmation|algorithme|intelligence artificielle|machine learning/i.test(question)) complexity += 3;

            return Math.min(10, complexity);
        }

        // Fonction pour afficher le rapport détaillé du QI
        function showQIReport() {
            if (!qiCalculator) return;

            const report = qiCalculator.getDetailedReport();
            const breakdown = report.breakdown;

            const reportHTML = `
                <div style="background: rgba(0,255,136,0.1); border: 2px solid #00ff88; border-radius: 10px; padding: 15px; margin: 10px 0;">
                    <h3 style="color: #00ff88; margin-bottom: 10px;">🧠 RAPPORT DÉTAILLÉ DU QI - LOUNA AI V3.0</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div><strong>QI Total:</strong> <span style="color: #00ff88; font-size: 18px;">${report.currentQI}</span></div>
                        <div><strong>Classification:</strong> <span style="color: #ffaa00;">${report.classification}</span></div>
                    </div>

                    <h4 style="color: #4ecdc4; margin: 10px 0;">📊 Décomposition du calcul:</h4>
                    <div style="font-size: 12px; line-height: 1.4;">
                        • <strong>Base cognitive:</strong> ${Math.round(breakdown.base)} points<br>
                        • <strong>Qualité réponses:</strong> +${Math.round(breakdown.quality)} points (${report.metrics.responseQuality.toFixed(1)}/10)<br>
                        • <strong>Vitesse traitement:</strong> +${Math.round(breakdown.speed)} points (${report.metrics.averageResponseTime.toFixed(2)}s)<br>
                        • <strong>Résolution problèmes:</strong> +${Math.round(breakdown.problemSolving)} points (${(report.metrics.problemSolvingSuccess*100).toFixed(1)}%)<br>
                        • <strong>Capacités cognitives:</strong> +${Math.round(breakdown.cognitive)} points<br>
                        • <strong>Accélérateurs KYBER:</strong> +${Math.round(breakdown.kyber)} points (${report.metrics.kyberAccelerators} actifs)<br>
                        • <strong>Mémoire thermique:</strong> +${Math.round(breakdown.memory)} points (${report.metrics.memoryZones} zones)<br>
                        • <strong>Problèmes millénaire:</strong> +${Math.round(breakdown.millennium)} points (${report.metrics.millenniumProblems} résolus)<br>
                        • <strong>Expérience:</strong> +${Math.round(breakdown.experience)} points (${report.metrics.questionsAnswered} questions)<br>
                        • <strong>Précision:</strong> +${Math.round(breakdown.accuracy)} points (${((report.metrics.correctAnswers/Math.max(1,report.metrics.questionsAnswered))*100).toFixed(1)}%)<br>
                        • <strong>Réseau neuronal:</strong> +${Math.round(breakdown.neuronal)} points (${(report.metrics.neuronCount/1000000).toFixed(0)}M neurones)
                    </div>

                    <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <strong>🎯 Performance actuelle:</strong><br>
                        Créativité: ${report.metrics.creativityScore.toFixed(1)}/10 |
                        Logique: ${report.metrics.logicalReasoning.toFixed(1)}/10 |
                        Efficacité: ${(report.metrics.memoryEfficiency*100).toFixed(1)}%
                    </div>
                </div>
            `;

            addMessage(reportHTML, 'system');
        }

        // Commandes spéciales
        function handleSpecialCommands(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('rapport qi') || lowerMessage.includes('calcul qi') || lowerMessage.includes('détaillé qi')) {
                showQIReport();
                return true;
            }

            if (lowerMessage.includes('statistiques') || lowerMessage.includes('stats')) {
                if (qiCalculator) {
                    const metrics = qiCalculator.metrics;
                    const statsHTML = `
                        <div style="background: rgba(0,136,255,0.1); border: 2px solid #0088ff; border-radius: 10px; padding: 15px;">
                            <h3 style="color: #0088ff;">📊 STATISTIQUES SYSTÈME</h3>
                            <div style="font-size: 12px; line-height: 1.4;">
                                • Questions traitées: ${metrics.questionsAnswered}<br>
                                • Taux de réussite: ${((metrics.correctAnswers/Math.max(1,metrics.questionsAnswered))*100).toFixed(1)}%<br>
                                • Temps de réponse moyen: ${metrics.averageResponseTime.toFixed(2)}s<br>
                                • Temps le plus rapide: ${metrics.fastestResponse.toFixed(2)}s<br>
                                • Qualité moyenne: ${metrics.averageQuality.toFixed(1)}/10<br>
                                • Problèmes complexes résolus: ${metrics.complexProblemsResolved}<br>
                                • Neurones actifs: ${(metrics.neuronCount/1000000).toFixed(1)}M<br>
                                • Synapses: ${(metrics.synapseCount/1000000).toFixed(1)}M
                            </div>
                        </div>
                    `;
                    addMessage(statsHTML, 'system');
                }
                return true;
            }

            return false;
        }

        // Message de bienvenue avec suggestion
        setTimeout(() => {
            addMessage(`💡 <strong>Suggestions de commandes:</strong><br>
            • "Rapport QI détaillé" - Voir le calcul complet du QI<br>
            • "Statistiques" - Voir les performances système<br>
            • "Explique-moi ton calcul de QI avec tes accélérateurs KYBER"<br>
            • Ou posez-moi n'importe quelle question complexe !`, 'system');
        }, 2000);
    </script>
</body>
</html>
