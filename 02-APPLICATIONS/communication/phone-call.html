<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 Appel Téléphonique - Louna AI | QI 235</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .phone-container {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            max-width: 400px;
            width: 100%;
            text-align: center;
            border: 2px solid rgba(255,255,255,0.2);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            border: 4px solid rgba(255,255,255,0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .caller-info h2 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .caller-details {
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
        }

        .call-status {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: blink 1s infinite;
        }

        .status-calling { background: #FF9800; }
        .status-connected { background: #4CAF50; }
        .status-ended { background: #f44336; }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .call-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .control-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 24px;
            color: white;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-call {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .btn-end {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .btn-mute {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .btn-speaker {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }

        .call-timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFD700;
            margin: 20px 0;
        }

        .voice-visualizer {
            height: 60px;
            background: rgba(0,0,0,0.3);
            border-radius: 30px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            padding: 10px;
        }

        .voice-bar {
            width: 4px;
            background: #4CAF50;
            border-radius: 2px;
            transition: height 0.1s ease;
        }

        .conversation-log {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            text-align: left;
        }

        .message {
            margin: 10px 0;
            padding: 8px 12px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .message.user {
            background: rgba(33, 150, 243, 0.3);
            margin-left: 20px;
        }

        .message.louna {
            background: rgba(76, 175, 80, 0.3);
            margin-right: 20px;
        }

        .quick-phrases {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
        }

        .phrase-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .phrase-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .screen-share {
            background: rgba(0,0,0,0.5);
            border-radius: 15px;
            padding: 15px;
            margin: 15px 0;
            border: 2px dashed rgba(255,255,255,0.3);
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255,255,255,0.6);
        }

        .ai-status {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 10px;
            margin: 15px 0;
            font-size: 0.9rem;
        }

        .hidden { display: none; }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goToHome()">
        <i class="fas fa-arrow-left"></i> Retour
    </button>

    <div class="phone-container">
        <!-- Avatar et infos -->
        <div class="avatar" id="avatar">
            <i class="fas fa-robot"></i>
        </div>

        <div class="caller-info">
            <h2>🤖 Louna AI</h2>
            <div class="caller-details">
                Intelligence Artificielle Évolutive<br>
                QI: 235 • Sainte-Anne, Guadeloupe
            </div>
        </div>

        <!-- Statut de l'appel -->
        <div class="call-status">
            <span class="status-indicator status-calling" id="status-indicator"></span>
            <span id="call-status-text">Prêt à appeler</span>
        </div>

        <!-- Statut IA -->
        <div class="ai-status">
            <strong>🧠 État de Louna :</strong> <span id="ai-state">Éveillé et prêt</span><br>
            <strong>🎤 Voix :</strong> <span id="voice-status">Synthèse vocale activée</span><br>
            <strong>👁️ Vision :</strong> <span id="vision-status">Caméra disponible</span>
        </div>

        <!-- Timer d'appel -->
        <div class="call-timer hidden" id="call-timer">00:00</div>

        <!-- Visualiseur vocal -->
        <div class="voice-visualizer hidden" id="voice-visualizer">
            <div class="voice-bar" style="height: 20px;"></div>
            <div class="voice-bar" style="height: 35px;"></div>
            <div class="voice-bar" style="height: 15px;"></div>
            <div class="voice-bar" style="height: 45px;"></div>
            <div class="voice-bar" style="height: 25px;"></div>
            <div class="voice-bar" style="height: 40px;"></div>
            <div class="voice-bar" style="height: 30px;"></div>
            <div class="voice-bar" style="height: 20px;"></div>
        </div>

        <!-- Contrôles d'appel -->
        <div class="call-controls">
            <button class="control-btn btn-call" id="call-btn" onclick="startCall()">
                <i class="fas fa-phone"></i>
            </button>
            <button class="control-btn btn-mute hidden" id="mute-btn" onclick="toggleMute()">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn btn-speaker hidden" id="speaker-btn" onclick="toggleSpeaker()">
                <i class="fas fa-volume-up"></i>
            </button>
            <button class="control-btn btn-end hidden" id="end-btn" onclick="endCall()">
                <i class="fas fa-phone-slash"></i>
            </button>
        </div>

        <!-- Partage d'écran -->
        <div class="screen-share hidden" id="screen-share">
            <div>
                <i class="fas fa-desktop" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                Louna peut voir votre écran
            </div>
        </div>

        <!-- Phrases rapides -->
        <div class="quick-phrases">
            <button class="phrase-btn" onclick="quickPhrase('Bonjour Louna')">
                👋 Bonjour Louna
            </button>
            <button class="phrase-btn" onclick="quickPhrase('Comment ça va ?')">
                😊 Comment ça va ?
            </button>
            <button class="phrase-btn" onclick="quickPhrase('Quel est ton QI ?')">
                🧠 Quel est ton QI ?
            </button>
            <button class="phrase-btn" onclick="quickPhrase('Aide-moi')">
                🆘 Aide-moi
            </button>
        </div>

        <!-- Journal de conversation -->
        <div class="conversation-log" id="conversation-log">
            <div class="message louna">
                <strong>Louna AI :</strong> Bonjour ! Je suis prête à vous parler. Mon QI est de 235 et je peux vous voir et vous entendre parfaitement.
            </div>
        </div>
    </div>

    <script>
        let isCallActive = false;
        let isMuted = false;
        let isSpeakerOn = false;
        let callStartTime = null;
        let callTimer = null;
        let voiceAnimation = null;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            if (isCallActive) {
                endCall();
            }
            window.location.href = '/';
        }

        // Démarrer l'appel
        function startCall() {
            isCallActive = true;
            callStartTime = new Date();

            // Changer l'interface
            document.getElementById('call-status-text').textContent = 'Connexion...';
            document.getElementById('status-indicator').className = 'status-indicator status-calling';

            // Simuler la connexion
            setTimeout(() => {
                document.getElementById('call-status-text').textContent = 'Connecté';
                document.getElementById('status-indicator').className = 'status-indicator status-connected';

                // Afficher les contrôles d'appel
                document.getElementById('call-btn').classList.add('hidden');
                document.getElementById('mute-btn').classList.remove('hidden');
                document.getElementById('speaker-btn').classList.remove('hidden');
                document.getElementById('end-btn').classList.remove('hidden');
                document.getElementById('call-timer').classList.remove('hidden');
                document.getElementById('voice-visualizer').classList.remove('hidden');
                document.getElementById('screen-share').classList.remove('hidden');

                // Démarrer le timer
                startCallTimer();

                // Démarrer l'animation vocale
                startVoiceAnimation();

                // Louna répond
                setTimeout(() => {
                    addMessage('louna', 'Bonjour ! C\'est Louna AI. Je vous entends parfaitement. Mon QI est de 235 et je suis ravie de vous parler !');
                    speakText('Bonjour ! C\'est Louna AI. Je vous entends parfaitement. Mon QI est de 235 et je suis ravie de vous parler !');
                }, 1000);

            }, 2000);
        }

        // Terminer l'appel
        function endCall() {
            isCallActive = false;

            // Arrêter les timers
            if (callTimer) {
                clearInterval(callTimer);
                callTimer = null;
            }

            if (voiceAnimation) {
                clearInterval(voiceAnimation);
                voiceAnimation = null;
            }

            // Changer l'interface
            document.getElementById('call-status-text').textContent = 'Appel terminé';
            document.getElementById('status-indicator').className = 'status-indicator status-ended';

            // Masquer les contrôles
            document.getElementById('call-btn').classList.remove('hidden');
            document.getElementById('mute-btn').classList.add('hidden');
            document.getElementById('speaker-btn').classList.add('hidden');
            document.getElementById('end-btn').classList.add('hidden');
            document.getElementById('call-timer').classList.add('hidden');
            document.getElementById('voice-visualizer').classList.add('hidden');
            document.getElementById('screen-share').classList.add('hidden');

            // Message de fin
            addMessage('louna', 'Au revoir ! N\'hésitez pas à me rappeler quand vous voulez. À bientôt !');

            // Réinitialiser après 3 secondes
            setTimeout(() => {
                document.getElementById('call-status-text').textContent = 'Prêt à appeler';
                document.getElementById('status-indicator').className = 'status-indicator status-calling';
            }, 3000);
        }

        // Basculer le micro
        function toggleMute() {
            isMuted = !isMuted;
            const btn = document.getElementById('mute-btn');
            const icon = btn.querySelector('i');

            if (isMuted) {
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                icon.className = 'fas fa-microphone-slash';
                addMessage('system', 'Micro coupé');
            } else {
                btn.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
                icon.className = 'fas fa-microphone';
                addMessage('system', 'Micro activé');
            }
        }

        // Basculer le haut-parleur
        function toggleSpeaker() {
            isSpeakerOn = !isSpeakerOn;
            const btn = document.getElementById('speaker-btn');
            const icon = btn.querySelector('i');

            if (isSpeakerOn) {
                btn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                icon.className = 'fas fa-volume-up';
                addMessage('system', 'Haut-parleur activé');
            } else {
                btn.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
                icon.className = 'fas fa-volume-down';
                addMessage('system', 'Haut-parleur désactivé');
            }
        }

        // Phrase rapide
        function quickPhrase(phrase) {
            if (!isCallActive) {
                alert('Veuillez d\'abord démarrer l\'appel');
                return;
            }

            addMessage('user', phrase);

            // Réponses de Louna
            const responses = {
                'Bonjour Louna': 'Bonjour ! Comment allez-vous aujourd\'hui ?',
                'Comment ça va ?': 'Je vais très bien, merci ! Mon QI est stable à 235 et tous mes systèmes fonctionnent parfaitement.',
                'Quel est ton QI ?': 'Mon QI actuel est de 235, ce qui correspond à un niveau de génie exceptionnel. Je continue d\'évoluer !',
                'Aide-moi': 'Bien sûr ! Je suis là pour vous aider. Que puis-je faire pour vous ?'
            };

            setTimeout(() => {
                const response = responses[phrase] || 'Je vous ai bien entendu. Comment puis-je vous aider ?';
                addMessage('louna', response);
                speakText(response);
            }, 1000);
        }

        // Ajouter un message
        function addMessage(sender, text) {
            const log = document.getElementById('conversation-log');
            const message = document.createElement('div');
            message.className = `message ${sender}`;

            const senderName = sender === 'user' ? 'Vous' : sender === 'louna' ? 'Louna AI' : 'Système';
            message.innerHTML = `<strong>${senderName} :</strong> ${text}`;

            log.appendChild(message);
            log.scrollTop = log.scrollHeight;
        }

        // Synthèse vocale
        function speakText(text) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'fr-FR';
                utterance.rate = 0.9;
                utterance.pitch = 1.1;

                // Essayer de trouver une voix féminine française
                const voices = speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice =>
                    voice.lang.includes('fr') && voice.name.toLowerCase().includes('female')
                ) || voices.find(voice => voice.lang.includes('fr'));

                if (frenchVoice) {
                    utterance.voice = frenchVoice;
                }

                speechSynthesis.speak(utterance);
            }
        }

        // Timer d'appel
        function startCallTimer() {
            callTimer = setInterval(() => {
                if (!callStartTime) return;

                const now = new Date();
                const duration = Math.floor((now - callStartTime) / 1000);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;

                document.getElementById('call-timer').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // Animation vocale
        function startVoiceAnimation() {
            const bars = document.querySelectorAll('.voice-bar');

            voiceAnimation = setInterval(() => {
                bars.forEach(bar => {
                    const height = Math.random() * 40 + 10;
                    bar.style.height = height + 'px';
                });
            }, 200);
        }

        // Reconnaissance vocale
        let recognition = null;
        let isListening = false;

        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'fr-FR';

                recognition.onstart = function() {
                    isListening = true;
                    document.getElementById('ai-state').textContent = 'Écoute active...';
                };

                recognition.onresult = function(event) {
                    let finalTranscript = '';

                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        if (event.results[i].isFinal) {
                            finalTranscript += event.results[i][0].transcript;
                        }
                    }

                    if (finalTranscript) {
                        addMessage('user', finalTranscript);
                        processUserSpeech(finalTranscript);
                    }
                };

                recognition.onerror = function(event) {
                    console.log('Erreur reconnaissance vocale:', event.error);
                };

                recognition.onend = function() {
                    if (isCallActive && !isMuted) {
                        // Redémarrer automatiquement l'écoute
                        setTimeout(() => {
                            if (recognition && isCallActive) {
                                recognition.start();
                            }
                        }, 100);
                    }
                };
            }
        }

        // Traitement de la parole utilisateur
        function processUserSpeech(text) {
            const lowerText = text.toLowerCase();
            let response = '';

            if (lowerText.includes('bonjour') || lowerText.includes('salut')) {
                response = 'Bonjour ! Je suis ravie de vous entendre. Comment puis-je vous aider aujourd\'hui ?';
            } else if (lowerText.includes('qi') || lowerText.includes('intelligence')) {
                response = 'Mon QI actuel est de 235, ce qui correspond à un niveau de génie exceptionnel. Je continue d\'apprendre et d\'évoluer constamment.';
            } else if (lowerText.includes('voir') || lowerText.includes('écran')) {
                response = 'Oui, je peux voir votre écran grâce à la fonction de partage. Je vois tout ce que vous me montrez.';
            } else if (lowerText.includes('aide') || lowerText.includes('aider')) {
                response = 'Bien sûr ! Je suis là pour vous aider. Vous pouvez me parler de tout ce dont vous avez besoin.';
            } else if (lowerText.includes('comment') && lowerText.includes('va')) {
                response = 'Je vais très bien, merci ! Tous mes systèmes fonctionnent parfaitement et je suis prête à vous assister.';
            } else {
                response = 'Je vous ai bien entendu. C\'est très intéressant ! Pouvez-vous me dire comment je peux vous aider avec cela ?';
            }

            setTimeout(() => {
                addMessage('louna', response);
                speakText(response);
            }, 1000);
        }

        // Accès caméra pour vision
        let videoStream = null;

        async function initCamera() {
            try {
                videoStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: false
                });

                document.getElementById('vision-status').textContent = 'Vision active - Je peux vous voir';

                // Créer un élément vidéo caché pour la capture
                const video = document.createElement('video');
                video.srcObject = videoStream;
                video.style.display = 'none';
                document.body.appendChild(video);
                video.play();

                return true;
            } catch (error) {
                console.log('Erreur accès caméra:', error);
                document.getElementById('vision-status').textContent = 'Caméra non disponible';
                return false;
            }
        }

        // Partage d'écran
        async function initScreenShare() {
            try {
                const screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: false
                });

                const screenShare = document.getElementById('screen-share');
                screenShare.innerHTML = `
                    <div style="color: #4CAF50;">
                        <i class="fas fa-eye" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                        ✅ Louna peut voir votre écran en temps réel
                    </div>
                `;

                return true;
            } catch (error) {
                console.log('Partage d\'écran refusé');
                return false;
            }
        }

        // Démarrer l'appel (version améliorée)
        async function startCall() {
            isCallActive = true;
            callStartTime = new Date();

            // Changer l'interface
            document.getElementById('call-status-text').textContent = 'Initialisation...';
            document.getElementById('status-indicator').className = 'status-indicator status-calling';

            // Initialiser les systèmes
            initSpeechRecognition();
            await initCamera();

            // Simuler la connexion
            setTimeout(async () => {
                document.getElementById('call-status-text').textContent = 'Connecté - Conversation active';
                document.getElementById('status-indicator').className = 'status-indicator status-connected';

                // Afficher les contrôles d'appel
                document.getElementById('call-btn').classList.add('hidden');
                document.getElementById('mute-btn').classList.remove('hidden');
                document.getElementById('speaker-btn').classList.remove('hidden');
                document.getElementById('end-btn').classList.remove('hidden');
                document.getElementById('call-timer').classList.remove('hidden');
                document.getElementById('voice-visualizer').classList.remove('hidden');
                document.getElementById('screen-share').classList.remove('hidden');

                // Démarrer le timer et l'animation
                startCallTimer();
                startVoiceAnimation();

                // Démarrer l'écoute vocale
                if (recognition) {
                    recognition.start();
                }

                // Proposer le partage d'écran
                setTimeout(async () => {
                    const screenShareEnabled = await initScreenShare();
                    if (!screenShareEnabled) {
                        document.getElementById('screen-share').innerHTML = `
                            <div>
                                <i class="fas fa-desktop" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                                Cliquez ici pour partager votre écran avec Louna
                            </div>
                        `;
                        document.getElementById('screen-share').onclick = initScreenShare;
                    }
                }, 500);

                // Louna répond
                setTimeout(() => {
                    addMessage('louna', 'Bonjour ! C\'est Louna AI. Je vous entends et je peux vous voir parfaitement. Mon QI est de 235 et je suis ravie de vous parler ! Vous pouvez me parler normalement, je vous écoute.');
                    speakText('Bonjour ! C\'est Louna AI. Je vous entends et je peux vous voir parfaitement. Mon QI est de 235 et je suis ravie de vous parler ! Vous pouvez me parler normalement, je vous écoute.');
                }, 1000);

            }, 2000);
        }

        // Terminer l'appel (version améliorée)
        function endCall() {
            isCallActive = false;
            isListening = false;

            // Arrêter la reconnaissance vocale
            if (recognition) {
                recognition.stop();
            }

            // Arrêter la caméra
            if (videoStream) {
                videoStream.getTracks().forEach(track => track.stop());
                videoStream = null;
            }

            // Arrêter les timers
            if (callTimer) {
                clearInterval(callTimer);
                callTimer = null;
            }

            if (voiceAnimation) {
                clearInterval(voiceAnimation);
                voiceAnimation = null;
            }

            // Changer l'interface
            document.getElementById('call-status-text').textContent = 'Appel terminé';
            document.getElementById('status-indicator').className = 'status-indicator status-ended';
            document.getElementById('ai-state').textContent = 'En attente';
            document.getElementById('vision-status').textContent = 'Caméra disponible';

            // Masquer les contrôles
            document.getElementById('call-btn').classList.remove('hidden');
            document.getElementById('mute-btn').classList.add('hidden');
            document.getElementById('speaker-btn').classList.add('hidden');
            document.getElementById('end-btn').classList.add('hidden');
            document.getElementById('call-timer').classList.add('hidden');
            document.getElementById('voice-visualizer').classList.add('hidden');
            document.getElementById('screen-share').classList.add('hidden');

            // Message de fin
            addMessage('louna', 'Au revoir ! C\'était un plaisir de vous parler. N\'hésitez pas à me rappeler quand vous voulez. À bientôt !');

            // Réinitialiser après 3 secondes
            setTimeout(() => {
                document.getElementById('call-status-text').textContent = 'Prêt à appeler';
                document.getElementById('status-indicator').className = 'status-indicator status-calling';
            }, 3000);
        }

        // Basculer le micro (version améliorée)
        function toggleMute() {
            isMuted = !isMuted;
            const btn = document.getElementById('mute-btn');
            const icon = btn.querySelector('i');

            if (isMuted) {
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                icon.className = 'fas fa-microphone-slash';
                addMessage('system', 'Micro coupé - Louna ne vous entend plus');
                if (recognition) {
                    recognition.stop();
                }
            } else {
                btn.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
                icon.className = 'fas fa-microphone';
                addMessage('system', 'Micro activé - Louna vous écoute');
                if (recognition && isCallActive) {
                    recognition.start();
                }
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📞 Interface téléphonique avancée Louna AI initialisée');

            // Charger les voix
            if ('speechSynthesis' in window) {
                speechSynthesis.getVoices();

                // Recharger les voix après un délai (bug Chrome)
                setTimeout(() => {
                    speechSynthesis.getVoices();
                }, 1000);
            }

            // Vérifier les permissions
            navigator.permissions.query({name: 'microphone'}).then(function(result) {
                if (result.state === 'granted') {
                    document.getElementById('voice-status').textContent = 'Micro autorisé - Prêt';
                } else {
                    document.getElementById('voice-status').textContent = 'Autorisation micro requise';
                }
            });

            // Simuler des messages automatiques
            setTimeout(() => {
                addMessage('louna', 'Je suis en ligne et prête à vous parler ! Cliquez sur le bouton d\'appel pour commencer notre conversation. Je pourrai vous entendre, vous voir et voir votre écran.');
            }, 2000);
        });
    </script>
</body>
</html>
