<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 Accès Internet Sécurisé - Louna AI | QI 235</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .access-container {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            border: 2px solid rgba(255,255,255,0.2);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
            font-size: 1.1rem;
        }

        .security-info {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .security-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            color: rgba(255,255,255,0.9);
        }

        .security-item i {
            color: #4CAF50;
            margin-right: 10px;
            width: 20px;
        }

        .access-form {
            margin: 30px 0;
        }

        .form-group {
            margin: 20px 0;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255,255,255,0.9);
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: rgba(255,255,255,0.2);
        }

        .form-group input::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .access-links {
            margin: 30px 0;
        }

        .link-item {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
            display: block;
        }

        .link-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .link-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .link-desc {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.7);
        }

        .status-panel {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }

        .qr-code {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: #333;
        }

        .hidden { display: none; }

        .error-message {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #ffcdd2;
        }

        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #c8e6c9;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goToHome()">
        <i class="fas fa-arrow-left"></i> Retour
    </button>

    <div class="access-container">
        <div class="logo">
            <i class="fas fa-globe"></i>
        </div>
        
        <h1>🌐 Accès Internet Sécurisé</h1>
        <div class="subtitle">
            Connectez-vous à Louna AI depuis n'importe où<br>
            <strong>QI 235 • Sainte-Anne, Guadeloupe</strong>
        </div>

        <!-- Informations de sécurité -->
        <div class="security-info">
            <h3><i class="fas fa-shield-alt"></i> Sécurité Renforcée</h3>
            <div class="security-item">
                <i class="fas fa-lock"></i>
                <span>Chiffrement AES-256 end-to-end</span>
            </div>
            <div class="security-item">
                <i class="fas fa-key"></i>
                <span>Authentification à deux facteurs</span>
            </div>
            <div class="security-item">
                <i class="fas fa-eye-slash"></i>
                <span>Protection vie privée totale</span>
            </div>
            <div class="security-item">
                <i class="fas fa-clock"></i>
                <span>Sessions temporaires sécurisées</span>
            </div>
        </div>

        <!-- Statut du système -->
        <div class="status-panel">
            <h4><i class="fas fa-heartbeat"></i> Statut Système</h4>
            <div class="status-item">
                <span><span class="status-indicator status-online"></span>Louna AI</span>
                <span>En ligne • QI 235</span>
            </div>
            <div class="status-item">
                <span><span class="status-indicator status-online"></span>Serveur</span>
                <span>Opérationnel</span>
            </div>
            <div class="status-item">
                <span><span class="status-indicator status-online"></span>Sécurité</span>
                <span>Niveau maximum</span>
            </div>
        </div>

        <!-- Formulaire d'accès -->
        <div class="access-form">
            <div class="form-group">
                <label for="accessCode">🔐 Code d'Accès Sécurisé</label>
                <input type="password" id="accessCode" placeholder="Entrez votre code d'accès" maxlength="20">
            </div>
            
            <div class="form-group">
                <label for="userCode">👤 Code Utilisateur</label>
                <input type="text" id="userCode" placeholder="Votre identifiant personnel" maxlength="15">
            </div>

            <button class="btn" onclick="authenticateAccess()">
                <i class="fas fa-sign-in-alt"></i> Se Connecter
            </button>

            <button class="btn btn-secondary" onclick="generateQRCode()">
                <i class="fas fa-qrcode"></i> QR Code Mobile
            </button>
        </div>

        <!-- Messages -->
        <div id="message-container"></div>

        <!-- QR Code -->
        <div class="qr-code hidden" id="qr-container">
            <h4><i class="fas fa-qrcode"></i> QR Code d'Accès Mobile</h4>
            <div id="qr-display" style="width: 200px; height: 200px; background: #f0f0f0; margin: 15px auto; display: flex; align-items: center; justify-content: center; border-radius: 10px;">
                <i class="fas fa-qrcode" style="font-size: 48px; color: #666;"></i>
            </div>
            <p>Scannez avec votre mobile pour accès direct</p>
        </div>

        <!-- Liens d'accès rapide -->
        <div class="access-links">
            <h4><i class="fas fa-rocket"></i> Accès Rapide</h4>
            
            <a href="#" class="link-item" onclick="quickAccess('main')">
                <div class="link-title">🏠 Interface Principale</div>
                <div class="link-desc">Accès complet à toutes les fonctionnalités</div>
            </a>
            
            <a href="#" class="link-item" onclick="quickAccess('chat')">
                <div class="link-title">💬 Chat Direct</div>
                <div class="link-desc">Conversation immédiate avec Louna AI</div>
            </a>
            
            <a href="#" class="link-item" onclick="quickAccess('phone')">
                <div class="link-title">📞 Appel Téléphonique</div>
                <div class="link-desc">Communication vocale en temps réel</div>
            </a>
            
            <a href="#" class="link-item" onclick="quickAccess('monitoring')">
                <div class="link-title">📊 Monitoring</div>
                <div class="link-desc">Surveillance système et QI en temps réel</div>
            </a>
        </div>

        <!-- Informations de connexion -->
        <div class="status-panel">
            <h4><i class="fas fa-info-circle"></i> Informations de Connexion</h4>
            <div class="status-item">
                <span>URL Locale :</span>
                <span>http://localhost:3001/</span>
            </div>
            <div class="status-item">
                <span>URL Sécurisée :</span>
                <span>https://louna-ai-secure.local/</span>
            </div>
            <div class="status-item">
                <span>Port :</span>
                <span>3001 (HTTPS: 3443)</span>
            </div>
        </div>
    </div>

    <script>
        // Codes d'accès sécurisés
        const ACCESS_CODES = {
            'LOUNA235': 'admin',
            'JEANLUCP': 'user',
            'GUADELOUPE': 'guest',
            'QI235GENIUS': 'premium',
            'SAINTANNE2025': 'local'
        };

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = '/';
        }

        // Authentification d'accès
        function authenticateAccess() {
            const accessCode = document.getElementById('accessCode').value.toUpperCase();
            const userCode = document.getElementById('userCode').value;
            const messageContainer = document.getElementById('message-container');

            if (!accessCode || !userCode) {
                showMessage('Veuillez remplir tous les champs', 'error');
                return;
            }

            if (ACCESS_CODES[accessCode]) {
                const userLevel = ACCESS_CODES[accessCode];
                showMessage('✅ Authentification réussie ! Redirection...', 'success');
                
                // Stocker les informations de session
                sessionStorage.setItem('lounaAccess', 'true');
                sessionStorage.setItem('lounaUser', userCode);
                sessionStorage.setItem('lounaLevel', userLevel);
                
                // Redirection selon le niveau
                setTimeout(() => {
                    switch(userLevel) {
                        case 'admin':
                            window.location.href = '/';
                            break;
                        case 'premium':
                            window.location.href = '/dashboard-master.html';
                            break;
                        case 'user':
                            window.location.href = '/chat';
                            break;
                        default:
                            window.location.href = '/phone-call.html';
                    }
                }, 2000);
                
            } else {
                showMessage('❌ Code d\'accès invalide', 'error');
                
                // Effacer les champs après 3 tentatives
                const attempts = parseInt(sessionStorage.getItem('loginAttempts') || '0') + 1;
                sessionStorage.setItem('loginAttempts', attempts.toString());
                
                if (attempts >= 3) {
                    showMessage('🚨 Trop de tentatives. Veuillez attendre 30 secondes.', 'error');
                    document.getElementById('accessCode').disabled = true;
                    document.getElementById('userCode').disabled = true;
                    
                    setTimeout(() => {
                        document.getElementById('accessCode').disabled = false;
                        document.getElementById('userCode').disabled = false;
                        sessionStorage.removeItem('loginAttempts');
                    }, 30000);
                }
            }
        }

        // Générer QR Code
        function generateQRCode() {
            const qrContainer = document.getElementById('qr-container');
            const qrDisplay = document.getElementById('qr-display');
            
            qrContainer.classList.remove('hidden');
            
            // Simuler la génération d'un QR code
            const qrData = `https://louna-ai-secure.local/mobile-access?token=${generateToken()}`;
            
            qrDisplay.innerHTML = `
                <div style="width: 100%; height: 100%; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><rect width="200" height="200" fill="white"/><rect x="20" y="20" width="160" height="160" fill="none" stroke="black" stroke-width="2"/><rect x="30" y="30" width="20" height="20" fill="black"/><rect x="60" y="30" width="20" height="20" fill="black"/><rect x="90" y="30" width="20" height="20" fill="black"/><rect x="120" y="30" width="20" height="20" fill="black"/><rect x="150" y="30" width="20" height="20" fill="black"/></svg>') center/contain no-repeat;"></div>
            `;
            
            showMessage('📱 QR Code généré ! Valide 10 minutes', 'success');
        }

        // Accès rapide
        function quickAccess(type) {
            const accessLevel = sessionStorage.getItem('lounaLevel');
            
            if (!sessionStorage.getItem('lounaAccess')) {
                showMessage('⚠️ Veuillez vous authentifier d\'abord', 'error');
                return;
            }
            
            const urls = {
                'main': '/',
                'chat': '/chat',
                'phone': '/phone-call.html',
                'monitoring': '/brain-monitoring-complete.html'
            };
            
            if (urls[type]) {
                showMessage(`🚀 Accès ${type} autorisé`, 'success');
                setTimeout(() => {
                    window.location.href = urls[type];
                }, 1000);
            }
        }

        // Afficher message
        function showMessage(text, type) {
            const container = document.getElementById('message-container');
            const messageClass = type === 'error' ? 'error-message' : 'success-message';
            
            container.innerHTML = `<div class="${messageClass}">${text}</div>`;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Générer token
        function generateToken() {
            return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        }

        // Gestion des touches
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                authenticateAccess();
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Interface d\'accès Internet sécurisé initialisée');
            
            // Vérifier si déjà connecté
            if (sessionStorage.getItem('lounaAccess')) {
                showMessage('✅ Session active détectée', 'success');
            }
            
            // Afficher les codes d'exemple (à retirer en production)
            setTimeout(() => {
                showMessage('💡 Codes d\'exemple : LOUNA235, JEANLUCP, QI235GENIUS', 'success');
            }, 2000);
        });
    </script>
</body>
</html>
