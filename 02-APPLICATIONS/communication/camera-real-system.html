<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>🎥 Système Caméra RÉEL - Louna AI</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: rgba(255,255,255,0.05); padding: 30px; border-radius: 20px; }
        .header h1 { font-size: 2.5em; color: #00ff88; text-align: center; }
        .status { display: inline-block; padding: 8px 16px; border-radius: 20px; font-weight: bold; margin: 10px; }
        .status.active { background: #4CAF50; }
        .status.inactive { background: #f44336; }
        .video-container { background: #000; border-radius: 15px; margin: 20px 0; min-height: 400px; display: flex; align-items: center; justify-content: center; }
        #videoElement { width: 100%; max-height: 500px; border-radius: 15px; }
        .controls { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .btn { background: linear-gradient(45deg, #00ff88, #00ccff); color: #000; border: none; padding: 12px 20px; border-radius: 25px; font-weight: bold; cursor: pointer; }
        .btn:disabled { background: #666; color: #999; cursor: not-allowed; }
        .info-panel { background: rgba(0,255,136,0.1); border-radius: 15px; padding: 20px; margin: 20px 0; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .info-item { background: rgba(255,255,255,0.05); padding: 15px; border-radius: 10px; text-align: center; }
        .info-value { font-size: 1.2em; font-weight: bold; color: #00ff88; }
        .error { background: rgba(255,0,0,0.2); border: 1px solid #ff0000; border-radius: 10px; padding: 15px; margin: 20px 0; color: #ff6666; }
        .success { background: rgba(0,255,0,0.2); border: 1px solid #00ff00; border-radius: 10px; padding: 15px; margin: 20px 0; color: #66ff66; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 Système Caméra RÉEL</h1>
            <p>Accès caméra avec JavaScript natif - Louna AI v2.1.0</p>
            <div id="cameraStatus" class="status inactive">📷 Caméra Inactive</div>
            <div id="micStatus" class="status inactive">🎤 Micro Inactive</div>
        </div>

        <div class="video-container">
            <video id="videoElement" autoplay muted style="display: none;"></video>
            <div id="noVideoMessage">🎥 Cliquez sur "Démarrer Caméra" pour commencer</div>
        </div>

        <div class="controls">
            <button class="btn" id="startCamera">📷 Démarrer Caméra</button>
            <button class="btn" id="stopCamera" disabled>⏹️ Arrêter Caméra</button>
            <button class="btn" id="capturePhoto" disabled>📸 Capturer Photo</button>
            <button class="btn" id="toggleMic" disabled>🎤 Activer Micro</button>
        </div>

        <div class="info-panel">
            <h3>📊 Informations Temps Réel</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div>Résolution</div>
                    <div class="info-value" id="resolution">--</div>
                </div>
                <div class="info-item">
                    <div>Caméras Détectées</div>
                    <div class="info-value" id="cameraCount">--</div>
                </div>
                <div class="info-item">
                    <div>Statut</div>
                    <div class="info-value" id="streamStatus">Arrêté</div>
                </div>
            </div>
        </div>

        <div id="messageArea"></div>
        <canvas id="captureCanvas" style="display: none; max-width: 100%; border: 2px solid #00ff88;"></canvas>
    </div>

    <script>
        let videoElement = document.getElementById("videoElement");
        let stream = null;
        let cameras = [];
        let micEnabled = false;

        const startBtn = document.getElementById("startCamera");
        const stopBtn = document.getElementById("stopCamera");
        const captureBtn = document.getElementById("capturePhoto");
        const micBtn = document.getElementById("toggleMic");
        const messageArea = document.getElementById("messageArea");

        function showMessage(message, type = "info") {
            const messageDiv = document.createElement("div");
            messageDiv.className = type;
            messageDiv.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            messageArea.innerHTML = "";
            messageArea.appendChild(messageDiv);
            setTimeout(() => messageArea.innerHTML = "", 5000);
        }

        async function detectCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                cameras = devices.filter(device => device.kind === "videoinput");
                document.getElementById("cameraCount").textContent = cameras.length;
                
                if (cameras.length === 0) {
                    showMessage("Aucune caméra détectée sur cet appareil.", "error");
                    return false;
                }
                showMessage(`${cameras.length} caméra(s) détectée(s).`, "success");
                return true;
            } catch (error) {
                showMessage("Erreur lors de la détection des caméras: " + error.message, "error");
                return false;
            }
        }

        async function startCamera() {
            try {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error("API MediaDevices non supportée par ce navigateur.");
                }

                const camerasFound = await detectCameras();
                if (!camerasFound) return;

                const constraints = {
                    video: { width: { ideal: 1280 }, height: { ideal: 720 } },
                    audio: micEnabled
                };

                stream = await navigator.mediaDevices.getUserMedia(constraints);
                videoElement.srcObject = stream;
                videoElement.style.display = "block";
                document.getElementById("noVideoMessage").style.display = "none";

                updateUI(true);
                showMessage("Caméra démarrée avec succès !", "success");
                updateStreamInfo();

            } catch (error) {
                showMessage("Erreur lors du démarrage de la caméra: " + error.message, "error");
                console.error("Erreur caméra:", error);
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            videoElement.style.display = "none";
            document.getElementById("noVideoMessage").style.display = "block";
            updateUI(false);
            showMessage("Caméra arrêtée.", "success");
        }

        function capturePhoto() {
            if (!stream) return;
            const canvas = document.getElementById("captureCanvas");
            const context = canvas.getContext("2d");
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
            canvas.style.display = "block";
            showMessage("Photo capturée avec succès !", "success");
        }

        function toggleMicrophone() {
            micEnabled = !micEnabled;
            micBtn.textContent = micEnabled ? "🎤 Désactiver Micro" : "🎤 Activer Micro";
            document.getElementById("micStatus").textContent = micEnabled ? "🎤 Micro Actif" : "🎤 Micro Inactif";
            document.getElementById("micStatus").className = micEnabled ? "status active" : "status inactive";
            
            if (stream) {
                stopCamera();
                setTimeout(startCamera, 500);
            }
        }

        function updateUI(isActive) {
            startBtn.disabled = isActive;
            stopBtn.disabled = !isActive;
            captureBtn.disabled = !isActive;
            micBtn.disabled = !isActive;
            
            document.getElementById("cameraStatus").textContent = isActive ? "📷 Caméra Active" : "📷 Caméra Inactive";
            document.getElementById("cameraStatus").className = isActive ? "status active" : "status inactive";
            document.getElementById("streamStatus").textContent = isActive ? "Actif" : "Arrêté";
        }

        function updateStreamInfo() {
            if (stream && videoElement.videoWidth) {
                document.getElementById("resolution").textContent = 
                    `${videoElement.videoWidth}x${videoElement.videoHeight}`;
            }
        }

        startBtn.addEventListener("click", startCamera);
        stopBtn.addEventListener("click", stopCamera);
        captureBtn.addEventListener("click", capturePhoto);
        micBtn.addEventListener("click", toggleMicrophone);
        videoElement.addEventListener("loadedmetadata", updateStreamInfo);

        detectCameras();
        showMessage("Système caméra initialisé. Cliquez sur \"Démarrer Caméra\" pour commencer.", "success");
    </script>
</body>
</html>
