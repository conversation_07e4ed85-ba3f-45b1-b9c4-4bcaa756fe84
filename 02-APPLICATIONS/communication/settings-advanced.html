<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paramètres Avancés - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title .icon {
            font-size: 2.5rem;
            color: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        /* Container principal */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* Navigation des paramètres */
        .settings-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .settings-tab {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-tab:hover {
            background: rgba(255, 105, 180, 0.2);
            border-color: rgba(255, 105, 180, 0.4);
        }

        .settings-tab.active {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border-color: #ff69b4;
        }

        /* Sections de paramètres */
        .settings-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .settings-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }

        .settings-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #4ecdc4;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .setting-input, .setting-select, .setting-textarea {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .setting-input:focus, .setting-select:focus, .setting-textarea:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        .setting-input::placeholder, .setting-textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .setting-color {
            width: 60px;
            height: 40px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0;
        }

        .setting-color:hover {
            border-color: #ff69b4;
            transform: scale(1.05);
        }

        .setting-color:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        .setting-toggle {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }

        .setting-range {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            outline: none;
            -webkit-appearance: none;
        }

        .setting-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border-radius: 50%;
            cursor: pointer;
        }

        .range-value {
            display: inline-block;
            margin-left: 10px;
            font-weight: 600;
            color: #4ecdc4;
        }

        /* Boutons d'action */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .btn-save {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-reset {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-export {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-import {
            background: linear-gradient(135deg, #6f42c1, #5a2d91);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        /* Status indicator */
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.4);
            border-radius: 8px;
            color: #28a745;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .settings-nav {
                justify-content: center;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-cogs icon"></i>
                <h1>Paramètres Avancés</h1>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/kyber-dashboard.html" class="nav-btn">
                    <i class="fas fa-bolt"></i>
                    Kyber
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Status -->
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>Système Louna opérationnel - Tous les modules connectés</span>
        </div>

        <!-- Navigation des paramètres -->
        <div class="settings-nav">
            <div class="settings-tab active" onclick="showSection('agent')">
                <i class="fas fa-robot"></i>
                Agent Claude
            </div>
            <div class="settings-tab" onclick="showSection('voice')">
                <i class="fas fa-microphone"></i>
                Vocal
            </div>
            <div class="settings-tab" onclick="showSection('memory')">
                <i class="fas fa-fire"></i>
                Mémoire
            </div>
            <div class="settings-tab" onclick="showSection('kyber')">
                <i class="fas fa-bolt"></i>
                Kyber
            </div>
            <div class="settings-tab" onclick="showSection('interface')">
                <i class="fas fa-palette"></i>
                Interface
            </div>
            <div class="settings-tab" onclick="showSection('security')">
                <i class="fas fa-shield-alt"></i>
                Sécurité
            </div>
            <div class="settings-tab" onclick="showSection('network')">
                <i class="fas fa-wifi"></i>
                Réseau
            </div>
            <div class="settings-tab" onclick="showSection('system')">
                <i class="fas fa-desktop"></i>
                Système
            </div>
            <div class="settings-tab" onclick="showSection('advanced')">
                <i class="fas fa-cogs"></i>
                Avancé
            </div>
        </div>

        <!-- Section Agent Claude -->
        <div class="settings-section active" id="agent-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-brain"></i>
                        Configuration Agent
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Modèle Claude</label>
                        <select class="setting-select" id="claude-model">
                            <option value="incept5/llama3.1-claude:latest">Claude 4GB (Recommandé)</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="claude-3-haiku">Claude 3 Haiku</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Température de créativité</label>
                        <input type="range" class="setting-range" id="temperature-range" min="0" max="1" step="0.1" value="0.7">
                        <span class="range-value" id="temperature-value">0.7</span>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Tokens maximum</label>
                        <input type="range" class="setting-range" id="tokens-range" min="500" max="4000" step="100" value="2000">
                        <span class="range-value" id="tokens-value">2000</span>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Réflexions visibles</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-comments"></i>
                        Comportement Chat
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Personnalité</label>
                        <select class="setting-select" id="personality">
                            <option value="helpful">Serviable et professionnel</option>
                            <option value="creative">Créatif et imaginatif</option>
                            <option value="analytical">Analytique et précis</option>
                            <option value="friendly">Amical et décontracté</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Longueur des réponses</label>
                        <select class="setting-select" id="response-length">
                            <option value="concise">Concises</option>
                            <option value="balanced">Équilibrées</option>
                            <option value="detailed">Détaillées</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Mode développeur</label>
                            <div class="toggle-switch" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Vocal -->
        <div class="settings-section" id="voice-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-microphone"></i>
                        Reconnaissance Vocale
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Reconnaissance vocale activée</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Langue de reconnaissance</label>
                        <select class="setting-select" id="voice-language">
                            <option value="fr-FR" selected>Français (France)</option>
                            <option value="en-US">Anglais (États-Unis)</option>
                            <option value="en-GB">Anglais (Royaume-Uni)</option>
                            <option value="es-ES">Espagnol</option>
                            <option value="de-DE">Allemand</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Sensibilité microphone</label>
                        <input type="range" class="setting-range" id="mic-sensitivity" min="0" max="100" value="75">
                        <span class="range-value" id="mic-sensitivity-value">75%</span>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Activation par mot-clé</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Mot-clé d'activation</label>
                        <input type="text" class="setting-input" id="wake-word" value="Hey Louna" placeholder="Ex: Hey Louna">
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-volume-up"></i>
                        Synthèse Vocale
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Réponses vocales activées</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Voix de l'agent</label>
                        <select class="setting-select" id="voice-type">
                            <option value="female-fr" selected>Voix féminine française</option>
                            <option value="male-fr">Voix masculine française</option>
                            <option value="female-en">Voix féminine anglaise</option>
                            <option value="male-en">Voix masculine anglaise</option>
                            <option value="neural-fr">Voix neurale française</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Vitesse de parole</label>
                        <input type="range" class="setting-range" id="speech-rate" min="0.5" max="2" step="0.1" value="1">
                        <span class="range-value" id="speech-rate-value">1.0x</span>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Volume</label>
                        <input type="range" class="setting-range" id="voice-volume" min="0" max="100" value="80">
                        <span class="range-value" id="voice-volume-value">80%</span>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="testVoice()">
                            <i class="fas fa-play"></i>
                            Tester la voix
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-cog"></i>
                        Paramètres Audio
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Périphérique d'entrée</label>
                        <select class="setting-select" id="input-device">
                            <option value="default" selected>Microphone par défaut</option>
                            <option value="builtin">Microphone intégré</option>
                            <option value="external">Microphone externe</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Périphérique de sortie</label>
                        <select class="setting-select" id="output-device">
                            <option value="default" selected>Haut-parleurs par défaut</option>
                            <option value="builtin">Haut-parleurs intégrés</option>
                            <option value="headphones">Casque/Écouteurs</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Réduction de bruit</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Écho annulation</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Mémoire -->
        <div class="settings-section" id="memory-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-fire"></i>
                        Mémoire Thermique
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Température globale</label>
                        <div class="status-indicator">
                            <span id="thermal-temp" style="color: #ff6b6b; font-weight: 700;">42.3°C</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Entrées totales</label>
                        <div class="status-indicator">
                            <span id="total-entries">85 / 1420</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Efficacité système</label>
                        <div class="status-indicator">
                            <span id="memory-efficiency" style="color: #4ecdc4; font-weight: 700;">95.2%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Auto-optimisation</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="optimizeThermalMemory()">
                            <i class="fas fa-magic"></i>
                            Optimiser maintenant
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-layer-group"></i>
                        Zones Mémoire (6 zones)
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">🔴 Zone Instantanée</label>
                        <div class="status-indicator">
                            <span id="instant-zone">12 / 100 entrées</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🟠 Zone Court Terme</label>
                        <div class="status-indicator">
                            <span id="short-zone">28 / 500 entrées</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🟡 Zone de Travail</label>
                        <div class="status-indicator">
                            <span id="working-zone">45 / 1000 entrées</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🟢 Zone Moyen Terme</label>
                        <div class="status-indicator">
                            <span id="medium-zone">156 / 2000 entrées</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🔵 Zone Long Terme</label>
                        <div class="status-indicator">
                            <span id="long-zone">892 / 5000 entrées</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🟣 Zone Créative</label>
                        <div class="status-indicator">
                            <span id="creative-zone">67 / 1000 entrées</span>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-thermometer-half"></i>
                        Contrôle Thermique
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Seuil de refroidissement</label>
                        <input type="range" class="setting-range" id="cooling-threshold" min="30" max="80" value="60">
                        <span class="range-value" id="cooling-threshold-value">60°C</span>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Vitesse de refroidissement</label>
                        <select class="setting-select" id="cooling-speed">
                            <option value="slow">Lente (économie d'énergie)</option>
                            <option value="normal" selected>Normale</option>
                            <option value="fast">Rapide (performance)</option>
                            <option value="aggressive">Agressive (maximum)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Refroidissement automatique</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="forceCooling()">
                            <i class="fas fa-snowflake"></i>
                            Forcer refroidissement
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section KYBER -->
        <div class="settings-section" id="kyber-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-bolt"></i>
                        Accélérateurs KYBER
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Accélérateurs actifs</label>
                        <div class="status-indicator">
                            <span id="active-kyber" style="color: #ffd700; font-weight: 700;">8 / 16</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Boost performance</label>
                        <div class="status-indicator">
                            <span id="performance-boost" style="color: #4ecdc4; font-weight: 700;">+245%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Consommation énergétique</label>
                        <div class="status-indicator">
                            <span id="energy-consumption">127W</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Auto-ajustement</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="addKyberAccelerator()">
                            <i class="fas fa-plus"></i>
                            Ajouter accélérateur
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-tachometer-alt"></i>
                        Configuration Performance
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Mode performance</label>
                        <select class="setting-select" id="performance-mode">
                            <option value="eco">Économie d'énergie</option>
                            <option value="balanced" selected>Équilibré</option>
                            <option value="performance">Performance</option>
                            <option value="maximum">Performance maximale</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Niveau de compression</label>
                        <select class="setting-select" id="compression-level">
                            <option value="1">Niveau 1 (rapide)</option>
                            <option value="2" selected>Niveau 2 (équilibré)</option>
                            <option value="3">Niveau 3 (maximum)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Parallélisation</label>
                        <input type="range" class="setting-range" id="parallelization" min="1" max="16" value="8">
                        <span class="range-value" id="parallelization-value">8 threads</span>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Optimisation quantique</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        Métriques KYBER
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Vitesse de traitement</label>
                        <div class="status-indicator">
                            <span id="processing-speed">2.8 THz</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Efficacité énergétique</label>
                        <div class="status-indicator">
                            <span id="energy-efficiency">87.3%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Température KYBER</label>
                        <div class="status-indicator">
                            <span id="kyber-temp">38.2°C</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Uptime accélérateurs</label>
                        <div class="status-indicator">
                            <span id="kyber-uptime">99.7%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="benchmarkKyber()">
                            <i class="fas fa-stopwatch"></i>
                            Test de performance
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Interface -->
        <div class="settings-section" id="interface-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-palette"></i>
                        Thème et Couleurs
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Thème principal</label>
                        <select class="setting-select" id="main-theme">
                            <option value="louna-pink" selected>Louna Rose & Cyan</option>
                            <option value="dark-blue">Bleu Sombre</option>
                            <option value="purple-gold">Violet & Or</option>
                            <option value="green-matrix">Vert Matrix</option>
                            <option value="custom">Personnalisé</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Couleur primaire</label>
                        <input type="color" class="setting-color" id="primary-color" value="#ff69b4">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Couleur secondaire</label>
                        <input type="color" class="setting-color" id="secondary-color" value="#4ecdc4">
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Mode sombre</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="applyTheme()">
                            <i class="fas fa-paint-brush"></i>
                            Appliquer le thème
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-desktop"></i>
                        Affichage
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Taille de police</label>
                        <select class="setting-select" id="font-size">
                            <option value="small">Petite</option>
                            <option value="normal" selected>Normale</option>
                            <option value="large">Grande</option>
                            <option value="extra-large">Très grande</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Police de caractères</label>
                        <select class="setting-select" id="font-family">
                            <option value="roboto" selected>Roboto</option>
                            <option value="inter">Inter</option>
                            <option value="system">Système</option>
                            <option value="monospace">Monospace</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Transparence interface</label>
                        <input type="range" class="setting-range" id="interface-opacity" min="70" max="100" value="90">
                        <span class="range-value" id="interface-opacity-value">90%</span>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Animations fluides</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-bell"></i>
                        Notifications
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Notifications activées</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Position notifications</label>
                        <select class="setting-select" id="notification-position">
                            <option value="top-right" selected>Haut droite</option>
                            <option value="top-left">Haut gauche</option>
                            <option value="bottom-right">Bas droite</option>
                            <option value="bottom-left">Bas gauche</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Durée d'affichage</label>
                        <input type="range" class="setting-range" id="notification-duration" min="2" max="10" value="5">
                        <span class="range-value" id="notification-duration-value">5s</span>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Sons de notification</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="testNotification()">
                            <i class="fas fa-bell"></i>
                            Tester notification
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-layout-alt"></i>
                        Disposition
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Layout principal</label>
                        <select class="setting-select" id="main-layout">
                            <option value="sidebar" selected>Barre latérale</option>
                            <option value="top-nav">Navigation haute</option>
                            <option value="compact">Compact</option>
                            <option value="fullscreen">Plein écran</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Barre de navigation fixe</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Panneau de réflexion</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="resetLayout()">
                            <i class="fas fa-undo"></i>
                            Réinitialiser layout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Sécurité -->
        <div class="settings-section" id="security-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        Protection Antivirus
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Antivirus actif</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)" id="antivirus-toggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Niveau de protection</label>
                        <select class="setting-select" id="antivirus-level">
                            <option value="basic">Basique</option>
                            <option value="standard" selected>Standard</option>
                            <option value="advanced">Avancé</option>
                            <option value="maximum">Maximum</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Scan en temps réel</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="runAntivirusScan()">
                            <i class="fas fa-search"></i>
                            Lancer un scan
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-user-shield"></i>
                        Protection VPN
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">VPN activé</label>
                            <div class="toggle-switch" onclick="toggleVPN(this)" id="vpn-toggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Serveur VPN</label>
                        <select class="setting-select" id="vpn-server">
                            <option value="france">France (Paris)</option>
                            <option value="usa">États-Unis (New York)</option>
                            <option value="uk">Royaume-Uni (Londres)</option>
                            <option value="germany">Allemagne (Berlin)</option>
                            <option value="japan">Japon (Tokyo)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Protocole</label>
                        <select class="setting-select" id="vpn-protocol">
                            <option value="openvpn">OpenVPN</option>
                            <option value="wireguard" selected>WireGuard</option>
                            <option value="ikev2">IKEv2</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="status-indicator" id="vpn-status">
                            <div class="status-dot" style="background: #dc3545;"></div>
                            <span>VPN déconnecté</span>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-lock"></i>
                        Firewall
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Firewall activé</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Niveau de filtrage</label>
                        <select class="setting-select" id="firewall-level">
                            <option value="permissive">Permissif</option>
                            <option value="balanced" selected>Équilibré</option>
                            <option value="strict">Strict</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Bloquer les trackers</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Réseau -->
        <div class="settings-section" id="network-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-wifi"></i>
                        Wi-Fi
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Wi-Fi activé</label>
                            <div class="toggle-switch active" onclick="toggleWiFi(this)" id="wifi-toggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Réseau actuel</label>
                        <div class="status-indicator" id="wifi-status">
                            <div class="status-dot"></div>
                            <span id="wifi-network">Connecté à: Louna-Network</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="scanWiFiNetworks()">
                            <i class="fas fa-search"></i>
                            Scanner les réseaux
                        </button>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Connexion automatique</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-bluetooth-b"></i>
                        Bluetooth
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Bluetooth activé</label>
                            <div class="toggle-switch" onclick="toggleBluetooth(this)" id="bluetooth-toggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Appareils connectés</label>
                        <div id="bluetooth-devices">
                            <div style="color: rgba(255,255,255,0.6); font-size: 0.9rem;">Aucun appareil connecté</div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="scanBluetoothDevices()">
                            <i class="fas fa-search"></i>
                            Rechercher appareils
                        </button>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Découvrable</label>
                            <div class="toggle-switch" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-share-alt"></i>
                        AirDrop
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">AirDrop activé</label>
                            <div class="toggle-switch" onclick="toggleAirDrop(this)" id="airdrop-toggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Visibilité</label>
                        <select class="setting-select" id="airdrop-visibility">
                            <option value="off">Désactivé</option>
                            <option value="contacts" selected>Contacts uniquement</option>
                            <option value="everyone">Tout le monde</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Acceptation automatique</label>
                            <div class="toggle-switch" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="status-indicator" id="airdrop-status">
                            <div class="status-dot" style="background: #dc3545;"></div>
                            <span>AirDrop désactivé</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Système -->
        <div class="settings-section" id="system-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-microchip"></i>
                        Performance Système
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Utilisation CPU</label>
                        <div class="status-indicator">
                            <span id="cpu-usage">45%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Utilisation RAM</label>
                        <div class="status-indicator">
                            <span id="ram-usage">6.2 GB / 16 GB</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Température CPU</label>
                        <div class="status-indicator">
                            <span id="cpu-temp">52°C</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="optimizeSystem()">
                            <i class="fas fa-rocket"></i>
                            Optimiser le système
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-hdd"></i>
                        Stockage
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Espace utilisé</label>
                        <div class="status-indicator">
                            <span id="storage-used">245 GB / 1 TB</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Mémoire thermique</label>
                        <div class="status-indicator">
                            <span id="thermal-storage">85 entrées / 1420 max</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Nettoyage automatique</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="cleanupStorage()">
                            <i class="fas fa-broom"></i>
                            Nettoyer le stockage
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-sync-alt"></i>
                        Mises à jour
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Mises à jour automatiques</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Dernière vérification</label>
                        <div class="status-indicator">
                            <span id="last-update-check">Aujourd'hui à 14:30</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="checkForUpdates()">
                            <i class="fas fa-download"></i>
                            Vérifier les mises à jour
                        </button>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="transmitMemoryAnalysis()">
                            <i class="fas fa-brain"></i>
                            Transmettre analyse mémoire à l'agent
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Agent -->
        <div class="settings-section" id="agent-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-robot"></i>
                        Configuration Agent Principal
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">QI Actuel</label>
                        <div class="status-indicator">
                            <span id="agent-qi" style="color: #4ecdc4; font-weight: 700; font-size: 1.2rem;">203</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Modèle Agent</label>
                        <select class="setting-select" id="agent-model">
                            <option value="claude-sonnet-4" selected>Claude Sonnet 4 (4GB)</option>
                            <option value="claude-opus">Claude Opus (8GB)</option>
                            <option value="claude-haiku">Claude Haiku (2GB)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Mode MCP</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Réflexions visibles</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-brain"></i>
                        Mémoire Thermique
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Entrées stockées</label>
                        <div class="status-indicator">
                            <span id="memory-entries">85 / 1420</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Température globale</label>
                        <div class="status-indicator">
                            <span id="memory-temp">42.3°C</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Auto-évolution</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="optimizeMemory()">
                            <i class="fas fa-magic"></i>
                            Optimiser Mémoire
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-rocket"></i>
                        Accélérateurs KYBER
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Accélérateurs actifs</label>
                        <div class="status-indicator">
                            <span id="kyber-count">8 / 16</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Boost performance</label>
                        <div class="status-indicator">
                            <span id="kyber-boost">+245%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Auto-ajustement</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="addKyberAccelerator()">
                            <i class="fas fa-plus"></i>
                            Ajouter Accélérateur
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Avancé -->
        <div class="settings-section" id="advanced-section">
            <div class="settings-grid">
                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        Sauvegarde & Restauration
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Dernière sauvegarde</label>
                        <div class="status-indicator">
                            <span id="last-backup">Aujourd'hui à 15:30</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Sauvegarde automatique</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Fréquence sauvegarde</label>
                        <select class="setting-select" id="backup-frequency">
                            <option value="hourly">Toutes les heures</option>
                            <option value="daily" selected>Quotidienne</option>
                            <option value="weekly">Hebdomadaire</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="createBackup()">
                            <i class="fas fa-save"></i>
                            Créer Sauvegarde
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        Monitoring Avancé
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Logs détaillés</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Métriques temps réel</label>
                            <div class="toggle-switch active" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Niveau de debug</label>
                        <select class="setting-select" id="debug-level">
                            <option value="error">Erreurs seulement</option>
                            <option value="warn">Avertissements</option>
                            <option value="info" selected>Informations</option>
                            <option value="debug">Debug complet</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="exportLogs()">
                            <i class="fas fa-download"></i>
                            Exporter Logs
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-tools"></i>
                        Outils de Développement
                    </h3>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Mode développeur</label>
                            <div class="toggle-switch" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <label class="setting-label">Console debug</label>
                            <div class="toggle-switch" onclick="toggleSetting(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="runDiagnostics()">
                            <i class="fas fa-stethoscope"></i>
                            Diagnostics Complets
                        </button>
                    </div>
                    <div class="setting-item">
                        <button class="action-btn btn-save" onclick="resetToDefaults()">
                            <i class="fas fa-undo"></i>
                            Réinitialiser
                        </button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Informations Système
                    </h3>
                    <div class="setting-item">
                        <label class="setting-label">Version Louna</label>
                        <div class="status-indicator">
                            <span>v2.1.0 Final</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Créateur</label>
                        <div class="status-indicator">
                            <span>Jean-Luc Passave</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Localisation</label>
                        <div class="status-indicator">
                            <span>Sainte-Anne, Guadeloupe</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Uptime</label>
                        <div class="status-indicator">
                            <span id="system-uptime">2h 34m</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration par défaut
        const defaultSettings = {
            agent: {
                model: 'incept5/llama3.1-claude:latest',
                temperature: 0.7,
                maxTokens: 2000,
                reflectionsVisible: true,
                personality: 'helpful',
                responseLength: 'balanced',
                developerMode: false
            },
            voice: {
                language: 'fr-FR',
                voiceType: 'female',
                speed: 1.0,
                volume: 0.8,
                autoSpeak: true
            },
            memory: {
                thermalZones: 5,
                retentionTime: 24,
                compressionLevel: 'medium',
                backupEnabled: true
            },
            kyber: {
                acceleratorsCount: 8,
                boostLevel: 'optimal',
                energyMode: 'balanced',
                autoOptimize: true
            },
            interface: {
                theme: 'dark',
                animations: true,
                compactMode: false,
                showStats: true
            },
            security: {
                encryptionEnabled: true,
                vpnProtection: false,
                antivirusActive: true,
                secureMode: false
            }
        };

        let currentSettings = { ...defaultSettings };

        // Afficher une section
        function showSection(sectionName) {
            // Masquer toutes les sections
            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.remove('active');
            });

            // Désactiver tous les onglets
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Activer la section et l'onglet sélectionnés
            document.getElementById(sectionName + '-section').classList.add('active');
            event.target.closest('.settings-tab').classList.add('active');
        }

        // Basculer un paramètre toggle
        function toggleSetting(element) {
            element.classList.toggle('active');
        }

        // Mettre à jour les valeurs des ranges
        function updateRangeValues() {
            const temperatureRange = document.getElementById('temperature-range');
            const temperatureValue = document.getElementById('temperature-value');
            const tokensRange = document.getElementById('tokens-range');
            const tokensValue = document.getElementById('tokens-value');

            temperatureRange.addEventListener('input', function() {
                temperatureValue.textContent = this.value;
                currentSettings.agent.temperature = parseFloat(this.value);
            });

            tokensRange.addEventListener('input', function() {
                tokensValue.textContent = this.value;
                currentSettings.agent.maxTokens = parseInt(this.value);
            });
        }

        // Sauvegarder les paramètres
        function saveSettings() {
            try {
                localStorage.setItem('lounaSettings', JSON.stringify(currentSettings));
                showNotification('✅ Paramètres sauvegardés avec succès !', 'success');

                // Appliquer les paramètres
                applySettings();
            } catch (error) {
                console.error('Erreur sauvegarde:', error);
                showNotification('❌ Erreur lors de la sauvegarde', 'error');
            }
        }

        // Charger les paramètres
        function loadSettings() {
            try {
                const saved = localStorage.getItem('lounaSettings');
                if (saved) {
                    currentSettings = { ...defaultSettings, ...JSON.parse(saved) };
                    updateUI();
                    showNotification('📥 Paramètres chargés', 'info');
                }
            } catch (error) {
                console.error('Erreur chargement:', error);
                showNotification('❌ Erreur lors du chargement', 'error');
            }
        }

        // Réinitialiser les paramètres
        function resetSettings() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
                currentSettings = { ...defaultSettings };
                updateUI();
                localStorage.removeItem('lounaSettings');
                showNotification('🔄 Paramètres réinitialisés', 'warning');
            }
        }

        // Exporter les paramètres
        function exportSettings() {
            const dataStr = JSON.stringify(currentSettings, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'louna-settings.json';
            link.click();

            URL.revokeObjectURL(url);
            showNotification('📤 Paramètres exportés', 'success');
        }

        // Importer les paramètres
        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const imported = JSON.parse(e.target.result);
                            currentSettings = { ...defaultSettings, ...imported };
                            updateUI();
                            saveSettings();
                            showNotification('📥 Paramètres importés avec succès !', 'success');
                        } catch (error) {
                            console.error('Erreur import:', error);
                            showNotification('❌ Fichier de paramètres invalide', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };

            input.click();
        }

        // Mettre à jour l'interface
        function updateUI() {
            // Mettre à jour les valeurs des contrôles
            document.getElementById('claude-model').value = currentSettings.agent.model;
            document.getElementById('temperature-range').value = currentSettings.agent.temperature;
            document.getElementById('temperature-value').textContent = currentSettings.agent.temperature;
            document.getElementById('tokens-range').value = currentSettings.agent.maxTokens;
            document.getElementById('tokens-value').textContent = currentSettings.agent.maxTokens;
            document.getElementById('personality').value = currentSettings.agent.personality;
            document.getElementById('response-length').value = currentSettings.agent.responseLength;
        }

        // Appliquer les paramètres
        function applySettings() {
            // Envoyer les paramètres au serveur
            fetch('/api/settings/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(currentSettings)
            }).then(response => {
                if (response.ok) {
                    console.log('✅ Paramètres appliqués au serveur');
                } else {
                    console.warn('⚠️ Impossible d\'appliquer les paramètres au serveur');
                }
            }).catch(error => {
                console.error('❌ Erreur application paramètres:', error);
            });
        }

        // Système de notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateRangeValues();
            loadSettings();

            console.log('⚙️ Interface de paramètres avancés initialisée');
        });

        // Ajouter les boutons d'action
        document.addEventListener('DOMContentLoaded', function() {
            const actionButtons = document.createElement('div');
            actionButtons.className = 'action-buttons';
            actionButtons.innerHTML = `
                <button class="action-btn btn-save" onclick="saveSettings()">
                    <i class="fas fa-save"></i>
                    Sauvegarder
                </button>
                <button class="action-btn btn-reset" onclick="resetSettings()">
                    <i class="fas fa-undo"></i>
                    Réinitialiser
                </button>
                <button class="action-btn btn-export" onclick="exportSettings()">
                    <i class="fas fa-download"></i>
                    Exporter
                </button>
                <button class="action-btn btn-import" onclick="importSettings()">
                    <i class="fas fa-upload"></i>
                    Importer
                </button>
            `;
            document.querySelector('.container').appendChild(actionButtons);
        });

        // Fonctions pour les nouvelles sections

        // === SÉCURITÉ ===
        function toggleVPN(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            const status = document.getElementById('vpn-status');

            if (isActive) {
                status.innerHTML = '<div class="status-dot" style="background: #28a745;"></div><span>VPN connecté</span>';
                showNotification('🔒 VPN activé avec succès', 'success');
            } else {
                status.innerHTML = '<div class="status-dot" style="background: #dc3545;"></div><span>VPN déconnecté</span>';
                showNotification('🔓 VPN désactivé', 'warning');
            }
        }

        function runAntivirusScan() {
            showNotification('🔍 Démarrage du scan antivirus...', 'info');

            // Simulation du scan
            setTimeout(() => {
                showNotification('✅ Scan terminé - Aucune menace détectée', 'success');
            }, 3000);
        }

        // === RÉSEAU ===
        function toggleWiFi(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            const status = document.getElementById('wifi-status');
            const network = document.getElementById('wifi-network');

            if (isActive) {
                status.querySelector('.status-dot').style.background = '#28a745';
                network.textContent = 'Connecté à: Louna-Network';
                showNotification('📶 Wi-Fi activé', 'success');
            } else {
                status.querySelector('.status-dot').style.background = '#dc3545';
                network.textContent = 'Wi-Fi désactivé';
                showNotification('📵 Wi-Fi désactivé', 'warning');
            }
        }

        function toggleBluetooth(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');

            if (isActive) {
                showNotification('🔵 Bluetooth activé', 'success');
            } else {
                showNotification('⚫ Bluetooth désactivé', 'warning');
                document.getElementById('bluetooth-devices').innerHTML =
                    '<div style="color: rgba(255,255,255,0.6); font-size: 0.9rem;">Aucun appareil connecté</div>';
            }
        }

        function toggleAirDrop(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            const status = document.getElementById('airdrop-status');

            if (isActive) {
                status.innerHTML = '<div class="status-dot" style="background: #28a745;"></div><span>AirDrop activé</span>';
                showNotification('📤 AirDrop activé', 'success');
            } else {
                status.innerHTML = '<div class="status-dot" style="background: #dc3545;"></div><span>AirDrop désactivé</span>';
                showNotification('📥 AirDrop désactivé', 'warning');
            }
        }

        function scanWiFiNetworks() {
            showNotification('🔍 Recherche des réseaux Wi-Fi...', 'info');

            setTimeout(() => {
                showNotification('📶 5 réseaux trouvés', 'success');
            }, 2000);
        }

        function scanBluetoothDevices() {
            showNotification('🔍 Recherche d\'appareils Bluetooth...', 'info');

            setTimeout(() => {
                const devicesDiv = document.getElementById('bluetooth-devices');
                devicesDiv.innerHTML = `
                    <div style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <i class="fas fa-headphones"></i> AirPods Pro (Connecté)
                    </div>
                    <div style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                        <i class="fas fa-keyboard"></i> Magic Keyboard (Disponible)
                    </div>
                `;
                showNotification('🔵 2 appareils trouvés', 'success');
            }, 2500);
        }

        // === SYSTÈME ===
        function optimizeSystem() {
            showNotification('🚀 Optimisation du système en cours...', 'info');

            setTimeout(() => {
                // Simuler l'amélioration des performances
                document.getElementById('cpu-usage').textContent = '32%';
                document.getElementById('ram-usage').textContent = '4.8 GB / 16 GB';
                document.getElementById('cpu-temp').textContent = '48°C';

                showNotification('✅ Système optimisé avec succès !', 'success');
            }, 3000);
        }

        function cleanupStorage() {
            showNotification('🧹 Nettoyage du stockage...', 'info');

            setTimeout(() => {
                document.getElementById('storage-used').textContent = '238 GB / 1 TB';
                showNotification('✅ 7 GB libérés', 'success');
            }, 2500);
        }

        function checkForUpdates() {
            showNotification('🔄 Vérification des mises à jour...', 'info');

            setTimeout(() => {
                document.getElementById('last-update-check').textContent = 'Maintenant';
                showNotification('✅ Système à jour', 'success');
            }, 2000);
        }

        // === TRANSMISSION ANALYSE MÉMOIRE ===
        async function transmitMemoryAnalysis() {
            showNotification('📡 Transmission de l\'analyse mémoire à l\'agent...', 'info');

            try {
                const response = await fetch('/api/memory/transmit-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'transmit_memory_analysis',
                        timestamp: new Date().toISOString()
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification('✅ Analyse transmise avec succès à l\'agent !', 'success');
                    console.log('📊 Rapport d\'analyse:', result);
                } else {
                    throw new Error('Erreur serveur');
                }
            } catch (error) {
                console.error('❌ Erreur transmission:', error);
                showNotification('❌ Erreur lors de la transmission', 'error');
            }
        }

        // Mise à jour des métriques système en temps réel
        function updateSystemMetrics() {
            // Simuler des variations réalistes
            const cpuUsage = Math.max(20, Math.min(80, 45 + (Math.random() - 0.5) * 20));
            const ramUsed = Math.max(4, Math.min(12, 6.2 + (Math.random() - 0.5) * 2));
            const cpuTemp = Math.max(40, Math.min(70, 52 + (Math.random() - 0.5) * 10));

            document.getElementById('cpu-usage').textContent = Math.round(cpuUsage) + '%';
            document.getElementById('ram-usage').textContent = ramUsed.toFixed(1) + ' GB / 16 GB';
            document.getElementById('cpu-temp').textContent = Math.round(cpuTemp) + '°C';
        }

        // Démarrer la mise à jour des métriques
        setInterval(updateSystemMetrics, 5000);

        // === AGENT ===
        function optimizeMemory() {
            showNotification('🧠 Optimisation de la mémoire thermique...', 'info');

            setTimeout(() => {
                document.getElementById('memory-entries').textContent = '78 / 1420';
                document.getElementById('memory-temp').textContent = '39.8°C';
                showNotification('✅ Mémoire optimisée ! 7 entrées consolidées, température réduite', 'success');
            }, 3000);
        }

        function addKyberAccelerator() {
            const currentCount = parseInt(document.getElementById('kyber-count').textContent.split(' ')[0]);
            if (currentCount >= 16) {
                showNotification('⚠️ Limite maximale d\'accélérateurs atteinte', 'warning');
                return;
            }

            showNotification('⚡ Ajout d\'un accélérateur KYBER...', 'info');

            setTimeout(() => {
                const newCount = currentCount + 1;
                const newBoost = Math.round(245 + (newCount - 8) * 15);

                document.getElementById('kyber-count').textContent = `${newCount} / 16`;
                document.getElementById('kyber-boost').textContent = `+${newBoost}%`;
                showNotification(`✅ Accélérateur KYBER #${newCount} ajouté ! Boost: +${newBoost}%`, 'success');
            }, 2500);
        }

        // === AVANCÉ ===
        function createBackup() {
            showNotification('💾 Création de la sauvegarde...', 'info');

            setTimeout(() => {
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.getElementById('last-backup').textContent = `Aujourd'hui à ${timeStr}`;
                showNotification('✅ Sauvegarde créée avec succès !', 'success');
            }, 3000);
        }

        function exportLogs() {
            showNotification('📄 Exportation des logs...', 'info');

            // Simuler l'exportation
            setTimeout(() => {
                const logData = {
                    timestamp: new Date().toISOString(),
                    version: 'v2.1.0 Final',
                    creator: 'Jean-Luc Passave',
                    location: 'Sainte-Anne, Guadeloupe',
                    logs: [
                        { level: 'INFO', message: 'Système démarré avec succès', timestamp: new Date().toISOString() },
                        { level: 'INFO', message: 'Mémoire thermique initialisée', timestamp: new Date().toISOString() },
                        { level: 'INFO', message: '8 accélérateurs KYBER activés', timestamp: new Date().toISOString() },
                        { level: 'INFO', message: 'QI évolutif: 203', timestamp: new Date().toISOString() }
                    ]
                };

                const dataStr = JSON.stringify(logData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `louna-logs-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showNotification('📁 Logs exportés avec succès !', 'success');
            }, 2000);
        }

        function runDiagnostics() {
            showNotification('🔍 Exécution des diagnostics complets...', 'info');

            setTimeout(() => {
                const diagnostics = [
                    '✅ Mémoire thermique: Fonctionnelle (95.2% efficacité)',
                    '✅ Accélérateurs KYBER: 8/8 actifs',
                    '✅ Agent principal: QI 203, stable',
                    '✅ Système de fichiers: Intégrité vérifiée',
                    '✅ Réseau: Connexions stables',
                    '✅ Sécurité: Tous les systèmes protégés'
                ];

                showInfo(`🔍 Rapport de Diagnostics Complets:

${diagnostics.join('\n')}

🎯 Résultat: Tous les systèmes fonctionnent de manière optimale !
📊 Score global: 98.7/100
⏱️ Temps d'exécution: 2.3 secondes`);
            }, 2300);
        }

        function resetToDefaults() {
            if (confirm('⚠️ Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?')) {
                showNotification('🔄 Réinitialisation en cours...', 'info');

                setTimeout(() => {
                    // Réinitialiser les valeurs affichées
                    document.getElementById('agent-qi').textContent = '203';
                    document.getElementById('memory-entries').textContent = '85 / 1420';
                    document.getElementById('memory-temp').textContent = '42.3°C';
                    document.getElementById('kyber-count').textContent = '8 / 16';
                    document.getElementById('kyber-boost').textContent = '+245%';

                    showSuccess('✅ Paramètres réinitialisés aux valeurs par défaut');
                }, 2000);
            }
        }

        // Mise à jour de l'uptime
        function updateUptime() {
            const startTime = new Date(Date.now() - Math.random() * 10800000); // 0-3 heures
            const now = new Date();
            const diff = now - startTime;

            const hours = Math.floor(diff / 3600000);
            const minutes = Math.floor((diff % 3600000) / 60000);

            document.getElementById('system-uptime').textContent = `${hours}h ${minutes}m`;
        }

        // Mettre à jour l'uptime toutes les minutes
        setInterval(updateUptime, 60000);
        updateUptime(); // Mise à jour initiale

        // Mise à jour des métriques agent en temps réel
        function updateAgentMetrics() {
            // Simuler de légères variations du QI et des métriques
            const baseQI = 203;
            const qiVariation = Math.floor((Math.random() - 0.5) * 2); // ±1
            const newQI = Math.max(200, Math.min(210, baseQI + qiVariation));

            const memoryEntries = 85 + Math.floor((Math.random() - 0.5) * 10);
            const memoryTemp = 42.3 + (Math.random() - 0.5) * 2;

            document.getElementById('agent-qi').textContent = newQI;
            document.getElementById('memory-entries').textContent = `${memoryEntries} / 1420`;
            document.getElementById('memory-temp').textContent = `${memoryTemp.toFixed(1)}°C`;
        }

        // Mettre à jour les métriques agent toutes les 10 secondes
        setInterval(updateAgentMetrics, 10000);

        // === VOCAL ===
        function testVoice() {
            showNotification('🎤 Test de la synthèse vocale...', 'info');

            // Simuler la synthèse vocale
            setTimeout(() => {
                showNotification('🔊 "Bonjour, je suis Louna, votre assistant IA. Test vocal réussi !"', 'success');
            }, 2000);
        }

        // Mise à jour des valeurs des sliders vocaux
        document.addEventListener('DOMContentLoaded', function() {
            // Sensibilité microphone
            const micSensitivity = document.getElementById('mic-sensitivity');
            const micSensitivityValue = document.getElementById('mic-sensitivity-value');
            if (micSensitivity && micSensitivityValue) {
                micSensitivity.addEventListener('input', function() {
                    micSensitivityValue.textContent = this.value + '%';
                });
            }

            // Vitesse de parole
            const speechRate = document.getElementById('speech-rate');
            const speechRateValue = document.getElementById('speech-rate-value');
            if (speechRate && speechRateValue) {
                speechRate.addEventListener('input', function() {
                    speechRateValue.textContent = this.value + 'x';
                });
            }

            // Volume voix
            const voiceVolume = document.getElementById('voice-volume');
            const voiceVolumeValue = document.getElementById('voice-volume-value');
            if (voiceVolume && voiceVolumeValue) {
                voiceVolume.addEventListener('input', function() {
                    voiceVolumeValue.textContent = this.value + '%';
                });
            }
        });

        // === MÉMOIRE ===
        function optimizeThermalMemory() {
            showNotification('🧠 Optimisation de la mémoire thermique en cours...', 'info');

            setTimeout(() => {
                // Simuler l'optimisation
                document.getElementById('thermal-temp').textContent = '39.8°C';
                document.getElementById('total-entries').textContent = '78 / 1420';
                document.getElementById('memory-efficiency').textContent = '97.1%';

                // Mettre à jour les zones
                document.getElementById('instant-zone').textContent = '8 / 100 entrées';
                document.getElementById('short-zone').textContent = '22 / 500 entrées';
                document.getElementById('working-zone').textContent = '38 / 1000 entrées';

                showNotification('✅ Mémoire thermique optimisée ! Température réduite, efficacité améliorée', 'success');
            }, 3500);
        }

        function forceCooling() {
            showNotification('❄️ Refroidissement forcé en cours...', 'info');

            setTimeout(() => {
                document.getElementById('thermal-temp').textContent = '35.2°C';
                showNotification('✅ Refroidissement terminé ! Température stabilisée', 'success');
            }, 4000);
        }

        // Mise à jour du seuil de refroidissement
        document.addEventListener('DOMContentLoaded', function() {
            const coolingThreshold = document.getElementById('cooling-threshold');
            const coolingThresholdValue = document.getElementById('cooling-threshold-value');
            if (coolingThreshold && coolingThresholdValue) {
                coolingThreshold.addEventListener('input', function() {
                    coolingThresholdValue.textContent = this.value + '°C';
                });
            }
        });

        // === KYBER ===
        function benchmarkKyber() {
            showNotification('⚡ Test de performance KYBER en cours...', 'info');

            setTimeout(() => {
                // Simuler les résultats du benchmark
                const results = {
                    processingSpeed: '3.2 THz',
                    energyEfficiency: '89.7%',
                    temperature: '36.8°C',
                    score: '98.3/100'
                };

                document.getElementById('processing-speed').textContent = results.processingSpeed;
                document.getElementById('energy-efficiency').textContent = results.energyEfficiency;
                document.getElementById('kyber-temp').textContent = results.temperature;

                showInfo(`⚡ Résultats du Benchmark KYBER:

🚀 Vitesse de traitement: ${results.processingSpeed}
⚡ Efficacité énergétique: ${results.energyEfficiency}
🌡️ Température: ${results.temperature}
📊 Score global: ${results.score}

🎯 Performance: EXCELLENTE !
Tous les accélérateurs fonctionnent de manière optimale.`);
            }, 3000);
        }

        // Mise à jour de la parallélisation
        document.addEventListener('DOMContentLoaded', function() {
            const parallelization = document.getElementById('parallelization');
            const parallelizationValue = document.getElementById('parallelization-value');
            if (parallelization && parallelizationValue) {
                parallelization.addEventListener('input', function() {
                    parallelizationValue.textContent = this.value + ' threads';
                });
            }
        });

        // === INTERFACE ===
        function applyTheme() {
            const theme = document.getElementById('main-theme').value;
            const primaryColor = document.getElementById('primary-color').value;
            const secondaryColor = document.getElementById('secondary-color').value;

            showNotification('🎨 Application du thème...', 'info');

            setTimeout(() => {
                // Simuler l'application du thème
                document.documentElement.style.setProperty('--primary-color', primaryColor);
                document.documentElement.style.setProperty('--secondary-color', secondaryColor);

                showNotification(`✅ Thème "${theme}" appliqué avec succès !`, 'success');
            }, 1500);
        }

        function testNotification() {
            const position = document.getElementById('notification-position').value;
            const duration = document.getElementById('notification-duration').value;

            showNotification(`🔔 Test de notification ! Position: ${position}, Durée: ${duration}s`, 'info');
        }

        function resetLayout() {
            showNotification('🔄 Réinitialisation du layout...', 'info');

            setTimeout(() => {
                // Réinitialiser les sélections
                document.getElementById('main-layout').value = 'sidebar';
                document.getElementById('font-size').value = 'normal';
                document.getElementById('font-family').value = 'roboto';

                showNotification('✅ Layout réinitialisé aux valeurs par défaut', 'success');
            }, 1000);
        }

        // Mise à jour des sliders d'interface
        document.addEventListener('DOMContentLoaded', function() {
            // Transparence interface
            const interfaceOpacity = document.getElementById('interface-opacity');
            const interfaceOpacityValue = document.getElementById('interface-opacity-value');
            if (interfaceOpacity && interfaceOpacityValue) {
                interfaceOpacity.addEventListener('input', function() {
                    interfaceOpacityValue.textContent = this.value + '%';
                });
            }

            // Durée notification
            const notificationDuration = document.getElementById('notification-duration');
            const notificationDurationValue = document.getElementById('notification-duration-value');
            if (notificationDuration && notificationDurationValue) {
                notificationDuration.addEventListener('input', function() {
                    notificationDurationValue.textContent = this.value + 's';
                });
            }
        });

        // Mise à jour des métriques en temps réel pour les nouvelles sections
        function updateNewSectionsMetrics() {
            // Métriques mémoire thermique
            const thermalTemp = document.getElementById('thermal-temp');
            if (thermalTemp) {
                const baseTemp = 42.3;
                const variation = (Math.random() - 0.5) * 2;
                const newTemp = Math.max(35, Math.min(50, baseTemp + variation));
                thermalTemp.textContent = newTemp.toFixed(1) + '°C';
            }

            // Métriques KYBER
            const kyberTemp = document.getElementById('kyber-temp');
            if (kyberTemp) {
                const baseTemp = 38.2;
                const variation = (Math.random() - 0.5) * 1.5;
                const newTemp = Math.max(35, Math.min(45, baseTemp + variation));
                kyberTemp.textContent = newTemp.toFixed(1) + '°C';
            }

            const processingSpeed = document.getElementById('processing-speed');
            if (processingSpeed) {
                const baseSpeed = 2.8;
                const variation = (Math.random() - 0.5) * 0.4;
                const newSpeed = Math.max(2.5, Math.min(3.5, baseSpeed + variation));
                processingSpeed.textContent = newSpeed.toFixed(1) + ' THz';
            }

            const energyEfficiency = document.getElementById('energy-efficiency');
            if (energyEfficiency) {
                const baseEff = 87.3;
                const variation = (Math.random() - 0.5) * 2;
                const newEff = Math.max(85, Math.min(95, baseEff + variation));
                energyEfficiency.textContent = newEff.toFixed(1) + '%';
            }
        }

        // Mettre à jour les nouvelles métriques toutes les 8 secondes
        setInterval(updateNewSectionsMetrics, 8000);

        // Mise à jour de la configuration par défaut avec les nouvelles sections
        Object.assign(defaultSettings, {
            network: {
                wifiEnabled: true,
                bluetoothEnabled: false,
                airdropEnabled: false,
                airdropVisibility: 'contacts'
            },
            security: {
                antivirusEnabled: true,
                antivirusLevel: 'standard',
                vpnEnabled: false,
                vpnServer: 'france',
                firewallEnabled: true,
                firewallLevel: 'balanced'
            },
            system: {
                autoOptimize: true,
                autoCleanup: true,
                autoUpdates: true
            },
            agent: {
                model: 'claude-sonnet-4',
                mcpMode: true,
                reflectionsVisible: true,
                autoEvolution: true
            },
            advanced: {
                autoBackup: true,
                backupFrequency: 'daily',
                detailedLogs: true,
                realtimeMetrics: true,
                debugLevel: 'info',
                developerMode: false
            }
        });
    </script>
</body>
</html>
