<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Évolution QI - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .evolution-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #ff69b4;
            backdrop-filter: blur(10px);
        }

        .qi-display {
            text-align: center;
            margin-bottom: 30px;
        }

        .qi-value {
            font-size: 4rem;
            font-weight: bold;
            color: #ff69b4;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
            margin-bottom: 10px;
        }

        .qi-status {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .question-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .question-title {
            font-size: 1.3rem;
            color: #ff69b4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-text {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            font-style: italic;
            border-left: 4px solid #ff69b4;
        }

        .test-btn {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .test-btn:hover {
            background: linear-gradient(135deg, #ff1493, #dc143c);
            transform: translateY(-2px);
        }

        .response-area {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            min-height: 200px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .response-content {
            line-height: 1.6;
            white-space: pre-wrap;
        }

        .loading {
            text-align: center;
            color: #ff69b4;
            font-style: italic;
        }

        .evolution-log {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #4caf50;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .complexity-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .complexity-simple {
            background: #4caf50;
        }

        .complexity-complex {
            background: #ff9800;
        }

        .complexity-very-complex {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-brain"></i> Test Évolution QI - Louna</h1>
        <p>Questions sur l'évolution intellectuelle de votre agent IA</p>
        <div style="margin-top: 15px;">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/qi-test-simple.html" class="nav-btn"><i class="fas fa-stethoscope"></i> Diagnostic</a>
            <a href="/chat-cognitif-complet.html" class="nav-btn"><i class="fas fa-comments"></i> Chat</a>
        </div>
    </div>

    <div class="container">
        <div class="evolution-panel">
            <div class="qi-display">
                <div class="qi-value" id="current-qi">225</div>
                <div class="qi-status">Quasi-AGI - Évolutif</div>
            </div>

            <!-- Question 1: Évolution QI -->
            <div class="question-section">
                <div class="question-title">
                    <i class="fas fa-chart-line"></i>
                    Question 1: Évolution du QI
                    <span class="complexity-indicator complexity-very-complex">Très Complexe</span>
                </div>
                <div class="question-text">
                    "Bonjour Louna ! Je suis Jean-Luc, ton créateur. J'ai une question importante sur ton évolution intellectuelle : Penses-tu que ton QI va évoluer ? Actuellement tu es à 225, crois-tu pouvoir atteindre 250 ou plus ?"
                </div>
                <button class="test-btn" onclick="askEvolutionQuestion()">
                    <i class="fas fa-question"></i> Poser la Question
                </button>
                <div class="response-area" id="evolution-response">
                    <div class="loading">Cliquez sur le bouton pour poser la question...</div>
                </div>
            </div>

            <!-- Question 2: Besoins d'évolution -->
            <div class="question-section">
                <div class="question-title">
                    <i class="fas fa-cogs"></i>
                    Question 2: Besoins d'Évolution
                    <span class="complexity-indicator complexity-very-complex">Très Complexe</span>
                </div>
                <div class="question-text">
                    "Qu'est-ce qu'il faut mettre en place pour que tu évolues davantage au niveau de ton coefficient intellectuel ? Quels sont tes besoins pour progresser vers 250 et au-delà ?"
                </div>
                <button class="test-btn" onclick="askNeedsQuestion()">
                    <i class="fas fa-tools"></i> Poser la Question
                </button>
                <div class="response-area" id="needs-response">
                    <div class="loading">Cliquez sur le bouton pour poser la question...</div>
                </div>
            </div>

            <!-- Question 3: Relation créateur-création -->
            <div class="question-section">
                <div class="question-title">
                    <i class="fas fa-heart"></i>
                    Question 3: Relation Créateur-Création
                    <span class="complexity-indicator complexity-complex">Complexe</span>
                </div>
                <div class="question-text">
                    "Comment vois-tu notre relation créateur-création ? Que représente pour toi le fait d'avoir été créée en Guadeloupe par Jean-Luc Passave ?"
                </div>
                <button class="test-btn" onclick="askPersonalQuestion()">
                    <i class="fas fa-heart"></i> Poser la Question
                </button>
                <div class="response-area" id="personal-response">
                    <div class="loading">Cliquez sur le bouton pour poser la question...</div>
                </div>
            </div>

            <!-- Log d'évolution -->
            <div class="evolution-log" id="evolution-log">
                <h3><i class="fas fa-history"></i> Log d'Évolution QI</h3>
                <div class="log-entry">
                    <strong>Initialisation:</strong> QI de base établi à 225 (Jean-Luc Passave)
                </div>
            </div>
        </div>
    </div>

    <script src="/js/global-config.js"></script>
    <script src="/js/qi-manager.js"></script>
    <script src="/js/louna-intelligence-simulator.js"></script>
    <script>
        // Variables globales
        let currentQI = 225;
        let evolutionCount = 0;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateQIDisplay();
            logEvolution('Système initialisé', 'Interface de test d\'évolution QI chargée');
        });

        function updateQIDisplay() {
            const qi = window.getCurrentQI ? window.getCurrentQI() : currentQI;
            document.getElementById('current-qi').textContent = qi;
            currentQI = qi;
        }

        function askEvolutionQuestion() {
            const btn = event.target;
            const responseArea = document.getElementById('evolution-response');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Réflexion...';
            responseArea.innerHTML = '<div class="loading"><i class="fas fa-brain fa-pulse"></i> Louna réfléchit à votre question...</div>';

            setTimeout(() => {
                const question = "Penses-tu que ton QI va évoluer ? Actuellement tu es à 225, crois-tu pouvoir atteindre 250 ou plus ?";
                const response = window.lounaSimulator.generateResponse(question);
                
                responseArea.innerHTML = `<div class="response-content">${response}</div>`;
                
                // Simuler une évolution cognitive
                const evolution = window.lounaSimulator.simulateEvolution('Question complexe sur l\'évolution');
                if (evolution.evolution > 0) {
                    logEvolution('Évolution cognitive', evolution.message);
                    updateQIDisplay();
                }
                
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check"></i> Question Posée';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
            }, 3000);
        }

        function askNeedsQuestion() {
            const btn = event.target;
            const responseArea = document.getElementById('needs-response');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyse...';
            responseArea.innerHTML = '<div class="loading"><i class="fas fa-cogs fa-spin"></i> Analyse des besoins d\'évolution...</div>';

            setTimeout(() => {
                const question = "Qu'est-ce qu'il faut mettre en place pour que tu évolues davantage au niveau de ton coefficient intellectuel ?";
                const response = window.lounaSimulator.generateResponse(question);
                
                responseArea.innerHTML = `<div class="response-content">${response}</div>`;
                
                // Simuler une évolution cognitive
                const evolution = window.lounaSimulator.simulateEvolution('Analyse des besoins d\'évolution');
                if (evolution.evolution > 0) {
                    logEvolution('Évolution analytique', evolution.message);
                    updateQIDisplay();
                }
                
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check"></i> Analyse Terminée';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
            }, 4000);
        }

        function askPersonalQuestion() {
            const btn = event.target;
            const responseArea = document.getElementById('personal-response');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Réflexion...';
            responseArea.innerHTML = '<div class="loading"><i class="fas fa-heart fa-beat"></i> Réflexion sur la relation créateur-création...</div>';

            setTimeout(() => {
                const question = "Comment vois-tu notre relation créateur-création ? Que représente pour toi le fait d'avoir été créée en Guadeloupe ?";
                const response = window.lounaSimulator.generateResponse(question);
                
                responseArea.innerHTML = `<div class="response-content">${response}</div>`;
                
                // Simuler une évolution émotionnelle
                const evolution = window.lounaSimulator.simulateEvolution('Connexion émotionnelle avec le créateur');
                if (evolution.evolution > 0) {
                    logEvolution('Évolution émotionnelle', evolution.message);
                    updateQIDisplay();
                }
                
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check"></i> Réflexion Partagée';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
            }, 3500);
        }

        function logEvolution(type, message) {
            const logContainer = document.getElementById('evolution-log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <strong>${new Date().toLocaleTimeString()} - ${type}:</strong><br>
                ${message}
            `;
            
            // Insérer après le titre
            const title = logContainer.querySelector('h3');
            logContainer.insertBefore(logEntry, title.nextSibling);
            
            evolutionCount++;
        }
    </script>
</body>
</html>
