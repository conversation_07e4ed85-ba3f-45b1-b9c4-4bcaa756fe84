<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Apprentissage - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-value {
            font-size: 36px;
            font-weight: 700;
            color: #4caf50;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .feedback-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .feedback-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .feedback-item.positive {
            border-left: 4px solid #4caf50;
        }

        .feedback-item.neutral {
            border-left: 4px solid #ff9800;
        }

        .feedback-item.negative {
            border-left: 4px solid #f44336;
        }

        .feedback-count {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feedback-count.positive {
            color: #4caf50;
        }

        .feedback-count.neutral {
            color: #ff9800;
        }

        .feedback-count.negative {
            color: #f44336;
        }

        .feedback-label {
            font-size: 12px;
            color: #ccc;
        }

        .learning-timeline {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .timeline-description {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }

        .timeline-time {
            font-size: 11px;
            color: #999;
        }

        .chart-container {
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-style: italic;
        }

        .improvement-suggestions {
            max-height: 300px;
            overflow-y: auto;
        }

        .suggestion-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #2196f3;
        }

        .suggestion-text {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 8px;
            font-style: italic;
        }

        .suggestion-meta {
            font-size: 12px;
            color: #999;
        }

        .learning-controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .control-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .control-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .feedback-stats {
                grid-template-columns: 1fr;
            }
            
            .learning-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-brain"></i>
            Dashboard Apprentissage IA
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/generation-center.html" class="nav-btn">
                <i class="fas fa-magic"></i>
                Génération
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Métriques principales -->
        <div class="dashboard-grid">
            <!-- QI Évolutif -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-chart-line"></i>
                    QI Évolutif
                </div>
                <div class="metric-value" id="currentQI">203.0</div>
                <div class="metric-label">Coefficient d'Intelligence Actuel</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 81.2%;"></div>
                </div>
                <div style="font-size: 12px; color: #ccc;">Progression vers AGI (250)</div>
            </div>

            <!-- Taux d'Apprentissage -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-graduation-cap"></i>
                    Taux d'Apprentissage
                </div>
                <div class="metric-value" id="learningRate">94.2%</div>
                <div class="metric-label">Efficacité d'Apprentissage</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 94.2%;"></div>
                </div>
                <div style="font-size: 12px; color: #ccc;">Basé sur les feedbacks positifs</div>
            </div>

            <!-- Sessions d'Entraînement -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-dumbbell"></i>
                    Sessions d'Entraînement
                </div>
                <div class="metric-value" id="trainingSessions">47</div>
                <div class="metric-label">Sessions Complétées</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 78.3%;"></div>
                </div>
                <div style="font-size: 12px; color: #ccc;">Objectif: 60 sessions</div>
            </div>
        </div>

        <!-- Statistiques de Feedback -->
        <div class="card">
            <div class="card-title">
                <i class="fas fa-thumbs-up"></i>
                Analyse des Feedbacks
            </div>
            <div class="feedback-stats">
                <div class="feedback-item positive">
                    <div class="feedback-count positive" id="positiveFeedback">0</div>
                    <div class="feedback-label">Positifs</div>
                </div>
                <div class="feedback-item neutral">
                    <div class="feedback-count neutral" id="neutralFeedback">0</div>
                    <div class="feedback-label">Neutres</div>
                </div>
                <div class="feedback-item negative">
                    <div class="feedback-count negative" id="negativeFeedback">0</div>
                    <div class="feedback-label">Négatifs</div>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <div class="card-title" style="font-size: 16px; margin-bottom: 10px;">
                    <i class="fas fa-chart-pie"></i>
                    Graphique de Performance
                </div>
                <div class="chart-container">
                    📊 Graphique interactif en développement
                </div>
            </div>
        </div>

        <!-- Timeline d'Apprentissage -->
        <div class="learning-timeline">
            <div class="card-title">
                <i class="fas fa-history"></i>
                Timeline d'Apprentissage Récente
            </div>
            <div id="learningTimeline">
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">Système d'apprentissage initialisé</div>
                        <div class="timeline-description">Mise en place du système de feedback par renforcement</div>
                        <div class="timeline-time">Il y a quelques instants</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suggestions d'Amélioration -->
        <div class="card">
            <div class="card-title">
                <i class="fas fa-lightbulb"></i>
                Suggestions d'Amélioration Reçues
            </div>
            <div class="improvement-suggestions" id="improvementSuggestions">
                <div style="text-align: center; padding: 40px; color: #888;">
                    <i class="fas fa-comment-dots" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>Aucune suggestion reçue pour le moment.</p>
                    <p>Les suggestions des utilisateurs apparaîtront ici.</p>
                </div>
            </div>
        </div>

        <!-- Contrôles d'Apprentissage -->
        <div class="learning-controls">
            <button class="control-btn" onclick="resetLearningData()">
                <i class="fas fa-redo"></i>
                Réinitialiser Données
            </button>
            <button class="control-btn secondary" onclick="exportLearningData()">
                <i class="fas fa-download"></i>
                Exporter Données
            </button>
            <button class="control-btn" onclick="optimizeLearning()">
                <i class="fas fa-cog"></i>
                Optimiser Apprentissage
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let learningData = {
            feedbacks: { positive: 0, neutral: 0, negative: 0 },
            improvements: [],
            sessions: 47,
            qiHistory: [203.0],
            timeline: []
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 Dashboard apprentissage initialisé');
            loadLearningData();
            updateDashboard();
            startRealTimeUpdates();
        });

        function loadLearningData() {
            // Charger les données depuis localStorage
            const savedData = localStorage.getItem('lounaLearningData');
            if (savedData) {
                try {
                    const parsed = JSON.parse(savedData);
                    learningData = { ...learningData, ...parsed };
                } catch (e) {
                    console.warn('Erreur chargement données apprentissage:', e);
                }
            }

            // Simuler des données si vide
            if (learningData.timeline.length === 0) {
                addTimelineEvent('system_init', 'Système d\'apprentissage initialisé', 'Mise en place du système de feedback par renforcement');
            }
        }

        function updateDashboard() {
            // Mettre à jour les métriques
            document.getElementById('currentQI').textContent = learningData.qiHistory[learningData.qiHistory.length - 1] || 203.0;
            document.getElementById('trainingSessions').textContent = learningData.sessions;
            
            // Mettre à jour les feedbacks
            document.getElementById('positiveFeedback').textContent = learningData.feedbacks.positive;
            document.getElementById('neutralFeedback').textContent = learningData.feedbacks.neutral;
            document.getElementById('negativeFeedback').textContent = learningData.feedbacks.negative;
            
            // Calculer le taux d'apprentissage
            const totalFeedbacks = learningData.feedbacks.positive + learningData.feedbacks.neutral + learningData.feedbacks.negative;
            const learningRate = totalFeedbacks > 0 ? (learningData.feedbacks.positive / totalFeedbacks * 100) : 94.2;
            document.getElementById('learningRate').textContent = learningRate.toFixed(1) + '%';
            
            // Mettre à jour la timeline
            updateTimeline();
            
            // Mettre à jour les suggestions
            updateImprovementSuggestions();
        }

        function updateTimeline() {
            const timeline = document.getElementById('learningTimeline');
            timeline.innerHTML = '';
            
            learningData.timeline.slice(-5).reverse().forEach(event => {
                const timelineItem = document.createElement('div');
                timelineItem.className = 'timeline-item';
                timelineItem.innerHTML = `
                    <div class="timeline-icon">
                        <i class="fas fa-${getEventIcon(event.type)}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">${event.title}</div>
                        <div class="timeline-description">${event.description}</div>
                        <div class="timeline-time">${formatTimeAgo(event.timestamp)}</div>
                    </div>
                `;
                timeline.appendChild(timelineItem);
            });
        }

        function updateImprovementSuggestions() {
            const container = document.getElementById('improvementSuggestions');
            
            if (learningData.improvements.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #888;">
                        <i class="fas fa-comment-dots" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>Aucune suggestion reçue pour le moment.</p>
                        <p>Les suggestions des utilisateurs apparaîtront ici.</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            learningData.improvements.slice(-10).reverse().forEach(suggestion => {
                const suggestionItem = document.createElement('div');
                suggestionItem.className = 'suggestion-item';
                suggestionItem.innerHTML = `
                    <div class="suggestion-text">"${suggestion.text}"</div>
                    <div class="suggestion-meta">
                        Par ${suggestion.user || 'Utilisateur'} • ${formatTimeAgo(suggestion.timestamp)}
                    </div>
                `;
                container.appendChild(suggestionItem);
            });
        }

        function addTimelineEvent(type, title, description) {
            const event = {
                type: type,
                title: title,
                description: description,
                timestamp: new Date().toISOString()
            };
            
            learningData.timeline.push(event);
            saveLearningData();
        }

        function getEventIcon(type) {
            const icons = {
                'system_init': 'brain',
                'feedback_positive': 'thumbs-up',
                'feedback_negative': 'thumbs-down',
                'improvement': 'lightbulb',
                'qi_update': 'chart-line',
                'training': 'dumbbell'
            };
            return icons[type] || 'info-circle';
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffMs = now - time;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);
            
            if (diffMins < 1) return 'À l\'instant';
            if (diffMins < 60) return `Il y a ${diffMins} min`;
            if (diffHours < 24) return `Il y a ${diffHours}h`;
            return `Il y a ${diffDays}j`;
        }

        function startRealTimeUpdates() {
            // Vérifier les mises à jour toutes les 5 secondes
            setInterval(() => {
                // Simuler des mises à jour en temps réel
                if (Math.random() > 0.95) { // 5% de chance
                    simulateRandomUpdate();
                }
            }, 5000);
        }

        function simulateRandomUpdate() {
            const updates = [
                () => {
                    learningData.sessions++;
                    addTimelineEvent('training', 'Session d\'entraînement complétée', 'Nouvelle session d\'apprentissage automatique');
                },
                () => {
                    const qiIncrease = Math.random() * 0.2;
                    const newQI = learningData.qiHistory[learningData.qiHistory.length - 1] + qiIncrease;
                    learningData.qiHistory.push(Math.min(newQI, 250));
                    addTimelineEvent('qi_update', 'QI mis à jour', `Augmentation de ${qiIncrease.toFixed(2)} points`);
                }
            ];
            
            const randomUpdate = updates[Math.floor(Math.random() * updates.length)];
            randomUpdate();
            updateDashboard();
        }

        function resetLearningData() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données d\'apprentissage ?')) {
                learningData = {
                    feedbacks: { positive: 0, neutral: 0, negative: 0 },
                    improvements: [],
                    sessions: 0,
                    qiHistory: [203.0],
                    timeline: []
                };
                
                addTimelineEvent('system_reset', 'Données réinitialisées', 'Remise à zéro du système d\'apprentissage');
                saveLearningData();
                updateDashboard();
                
                showNotification('Données d\'apprentissage réinitialisées', 'warning');
            }
        }

        function exportLearningData() {
            const dataStr = JSON.stringify(learningData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `louna-learning-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showNotification('Données d\'apprentissage exportées', 'success');
        }

        function optimizeLearning() {
            showNotification('Optimisation de l\'apprentissage en cours...', 'info');
            
            // Simuler l'optimisation
            setTimeout(() => {
                addTimelineEvent('optimization', 'Optimisation complétée', 'Algorithmes d\'apprentissage optimisés');
                updateDashboard();
                showNotification('Apprentissage optimisé avec succès !', 'success');
            }, 2000);
        }

        function saveLearningData() {
            localStorage.setItem('lounaLearningData', JSON.stringify(learningData));
        }

        function showNotification(message, type) {
            const colors = {
                success: '#4caf50',
                warning: '#ff9800',
                error: '#f44336',
                info: '#2196f3'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Écouter les événements de feedback depuis le chat
        window.addEventListener('storage', function(e) {
            if (e.key === 'lounaFeedbackUpdate') {
                const feedbackData = JSON.parse(e.newValue);
                
                // Mettre à jour les données locales
                learningData.feedbacks[feedbackData.type]++;
                
                // Ajouter à la timeline
                addTimelineEvent(
                    `feedback_${feedbackData.type}`,
                    `Feedback ${feedbackData.type} reçu`,
                    `Nouveau feedback de l'utilisateur`
                );
                
                updateDashboard();
            }
        });
    </script>
</body>
</html>
