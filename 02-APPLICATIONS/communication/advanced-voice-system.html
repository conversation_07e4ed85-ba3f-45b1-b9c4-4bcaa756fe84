<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système Vocal Avancé - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .voice-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .voice-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            overflow: hidden;
        }

        .voice-avatar.listening {
            animation: pulse 1.5s infinite;
        }

        .voice-avatar.speaking {
            animation: speak 0.5s infinite alternate;
        }

        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(233, 30, 99, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(233, 30, 99, 0); }
        }

        @keyframes speak {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .voice-avatar i {
            font-size: 60px;
            color: white;
        }

        .voice-status {
            text-align: center;
            font-size: 16px;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .voice-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .voice-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .voice-btn.record {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .voice-btn.stop {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .voice-btn.play {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .voice-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .voice-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .voice-settings {
            margin-top: 20px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #ffffff;
        }

        .setting-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            color: white;
            font-size: 14px;
        }

        .setting-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .conversation-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .conversation-content {
            flex: 1;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            margin-right: auto;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .voice-input {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .voice-input input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }

        .voice-input button {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .voice-input button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .youtube-learning {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 0, 0, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(255, 0, 0, 0.3);
        }

        .youtube-learning h4 {
            color: #ff4444;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .learning-status {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .learning-progress {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .learning-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ff6666);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-microphone-alt"></i>
            Système Vocal Avancé - Louna
        </h1>
        <div class="nav-buttons">
            <a href="/chat.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/thermal-memory-dashboard.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Panel de contrôle vocal -->
        <div class="voice-panel">
            <div class="panel-title">
                <i class="fas fa-cog"></i>
                Contrôles Vocaux
            </div>

            <div class="voice-avatar" id="voiceAvatar">
                <i class="fas fa-microphone-alt"></i>
            </div>

            <div class="voice-status" id="voiceStatus">
                Prêt à vous écouter
            </div>

            <div class="voice-controls">
                <button class="voice-btn record" id="recordBtn" onclick="startListening()">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="voice-btn stop" id="stopBtn" onclick="stopListening()" disabled>
                    <i class="fas fa-stop"></i>
                </button>
                <button class="voice-btn play" id="playBtn" onclick="testVoice()">
                    <i class="fas fa-play"></i>
                </button>
            </div>

            <div class="voice-settings">
                <div class="setting-group">
                    <label class="setting-label" for="voiceSelect">
                        <i class="fas fa-user-alt"></i> Voix Féminine
                    </label>
                    <select id="voiceSelect" class="setting-input">
                        <option value="auto">Sélection automatique</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="setting-label" for="voiceSpeed">
                        <i class="fas fa-tachometer-alt"></i> Vitesse: <span id="speedValue">1.0</span>
                    </label>
                    <input type="range" id="voiceSpeed" class="setting-input" min="0.5" max="2.0" step="0.1" value="1.0">
                </div>

                <div class="setting-group">
                    <label class="setting-label" for="voicePitch">
                        <i class="fas fa-music"></i> Tonalité: <span id="pitchValue">1.0</span>
                    </label>
                    <input type="range" id="voicePitch" class="setting-input" min="0.5" max="2.0" step="0.1" value="1.0">
                </div>

                <div class="setting-group">
                    <label class="setting-label" for="voiceVolume">
                        <i class="fas fa-volume-up"></i> Volume: <span id="volumeValue">1.0</span>
                    </label>
                    <input type="range" id="voiceVolume" class="setting-input" min="0.1" max="1.0" step="0.1" value="1.0">
                </div>
            </div>

            <!-- Apprentissage YouTube -->
            <div class="youtube-learning">
                <h4>
                    <i class="fab fa-youtube"></i>
                    Apprentissage Vocal YouTube
                </h4>
                <div class="learning-status" id="learningStatus">
                    Prêt à apprendre depuis YouTube
                </div>
                <div class="learning-progress">
                    <div class="learning-progress-fill" id="learningProgress"></div>
                </div>
                <button onclick="startYoutubeLearning()" style="margin-top: 10px; width: 100%; padding: 8px; background: #ff4444; border: none; color: white; border-radius: 5px; cursor: pointer;">
                    <i class="fab fa-youtube"></i> Démarrer l'apprentissage
                </button>
            </div>
        </div>

        <!-- Panel de conversation -->
        <div class="conversation-panel">
            <div class="panel-title">
                <i class="fas fa-comments"></i>
                Conversation Vocale
            </div>

            <div class="conversation-content" id="conversationContent">
                <div class="message assistant">
                    <div>Bonjour Jean-Luc ! Je suis Louna, votre assistante vocale. Mon QI est de 225 et je suis prête à vous aider. Parlez-moi !</div>
                    <div class="message-time">Maintenant</div>
                </div>
            </div>

            <div class="voice-input">
                <input type="text" id="textInput" placeholder="Ou tapez votre message ici..." onkeypress="handleKeyPress(event)">
                <button onclick="sendTextMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let recognition = null;
        let isListening = false;
        let isSpeaking = false;
        let currentVoice = null;
        let femaleVoices = [];
        let youtubeAnalyzer = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎤 Système vocal avancé initialisé');
            initializeVoiceSystem();
            setupEventListeners();
            loadFemaleVoices();
            initializeYoutubeLearning();
        });

        function initializeVoiceSystem() {
            // Initialiser la reconnaissance vocale
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'fr-FR';
                recognition.maxAlternatives = 3;

                recognition.onstart = () => {
                    console.log('🎤 Écoute démarrée');
                    isListening = true;
                    updateVoiceStatus('listening');
                };

                recognition.onend = () => {
                    console.log('🎤 Écoute terminée');
                    isListening = false;
                    updateVoiceStatus('idle');
                };

                recognition.onresult = (event) => {
                    let finalTranscript = '';
                    let interimTranscript = '';

                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        if (event.results[i].isFinal) {
                            finalTranscript += transcript;
                        } else {
                            interimTranscript += transcript;
                        }
                    }

                    if (finalTranscript) {
                        console.log('🗣️ Texte reconnu:', finalTranscript);
                        processVoiceInput(finalTranscript);
                    }
                };

                recognition.onerror = (event) => {
                    console.error('❌ Erreur reconnaissance vocale:', event.error);
                    updateVoiceStatus('error');
                };
            } else {
                console.warn('⚠️ Reconnaissance vocale non supportée');
                updateVoiceStatus('unsupported');
            }
        }

        function loadFemaleVoices() {
            if ('speechSynthesis' in window) {
                const loadVoices = () => {
                    const voices = speechSynthesis.getVoices();
                    femaleVoices = voices.filter(voice =>
                        voice.lang.startsWith('fr') &&
                        (voice.name.toLowerCase().includes('female') ||
                         voice.name.toLowerCase().includes('femme') ||
                         voice.name.toLowerCase().includes('woman') ||
                         voice.name.toLowerCase().includes('marie') ||
                         voice.name.toLowerCase().includes('claire') ||
                         voice.name.toLowerCase().includes('amelie') ||
                         voice.name.toLowerCase().includes('virginie') ||
                         voice.name.toLowerCase().includes('audrey'))
                    );

                    // Si pas de voix spécifiquement féminine, prendre les voix françaises
                    if (femaleVoices.length === 0) {
                        femaleVoices = voices.filter(voice => voice.lang.startsWith('fr'));
                    }

                    const voiceSelect = document.getElementById('voiceSelect');
                    voiceSelect.innerHTML = '<option value="auto">Sélection automatique</option>';

                    femaleVoices.forEach((voice, index) => {
                        const option = document.createElement('option');
                        option.value = index;
                        option.textContent = `${voice.name} (${voice.lang})`;
                        voiceSelect.appendChild(option);
                    });

                    // Sélectionner la meilleure voix féminine par défaut
                    if (femaleVoices.length > 0) {
                        currentVoice = femaleVoices[0];
                        console.log('🎭 Voix féminine sélectionnée:', currentVoice.name);
                    }
                };

                // Charger les voix
                loadVoices();
                speechSynthesis.onvoiceschanged = loadVoices;
            }
        }

        function setupEventListeners() {
            // Sliders de contrôle
            document.getElementById('voiceSpeed').addEventListener('input', (e) => {
                document.getElementById('speedValue').textContent = e.target.value;
            });

            document.getElementById('voicePitch').addEventListener('input', (e) => {
                document.getElementById('pitchValue').textContent = e.target.value;
            });

            document.getElementById('voiceVolume').addEventListener('input', (e) => {
                document.getElementById('volumeValue').textContent = e.target.value;
            });

            // Sélection de voix
            document.getElementById('voiceSelect').addEventListener('change', (e) => {
                if (e.target.value !== 'auto') {
                    currentVoice = femaleVoices[parseInt(e.target.value)];
                    console.log('🎭 Voix changée:', currentVoice.name);
                }
            });
        }

        function updateVoiceStatus(status) {
            const avatar = document.getElementById('voiceAvatar');
            const statusElement = document.getElementById('voiceStatus');
            const recordBtn = document.getElementById('recordBtn');
            const stopBtn = document.getElementById('stopBtn');

            avatar.className = 'voice-avatar';

            switch(status) {
                case 'listening':
                    avatar.classList.add('listening');
                    statusElement.textContent = 'Je vous écoute...';
                    recordBtn.disabled = true;
                    stopBtn.disabled = false;
                    break;
                case 'speaking':
                    avatar.classList.add('speaking');
                    statusElement.textContent = 'Je vous réponds...';
                    break;
                case 'processing':
                    statusElement.textContent = 'Je réfléchis...';
                    break;
                case 'error':
                    statusElement.textContent = 'Erreur de reconnaissance';
                    recordBtn.disabled = false;
                    stopBtn.disabled = true;
                    break;
                case 'unsupported':
                    statusElement.textContent = 'Reconnaissance vocale non supportée';
                    recordBtn.disabled = true;
                    break;
                default:
                    statusElement.textContent = 'Prêt à vous écouter';
                    recordBtn.disabled = false;
                    stopBtn.disabled = true;
            }
        }

        function startListening() {
            if (recognition && !isListening) {
                try {
                    recognition.start();
                } catch (error) {
                    console.error('Erreur démarrage reconnaissance:', error);
                }
            }
        }

        function stopListening() {
            if (recognition && isListening) {
                recognition.stop();
            }
        }

        function testVoice() {
            const testText = "Bonjour Jean-Luc ! Je suis Louna, votre assistante vocale avec un QI de 225. Ma voix féminine est maintenant parfaitement configurée ! Je peux vous parler avec une voix douce et naturelle.";
            speakText(testText);
        }

        function speakText(text) {
            if (!('speechSynthesis' in window)) {
                console.error('❌ Synthèse vocale non supportée');
                return;
            }

            // Arrêter toute synthèse en cours
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);

            // Configurer la voix
            if (currentVoice) {
                utterance.voice = currentVoice;
            }

            // Paramètres de voix optimisés pour une voix féminine
            utterance.rate = parseFloat(document.getElementById('voiceSpeed').value);
            utterance.pitch = parseFloat(document.getElementById('voicePitch').value);
            utterance.volume = parseFloat(document.getElementById('voiceVolume').value);

            // Optimisations pour voix féminine
            if (utterance.pitch < 1.2) {
                utterance.pitch = 1.2; // Tonalité plus aiguë pour voix féminine
            }

            utterance.onstart = () => {
                console.log('🗣️ Synthèse vocale démarrée');
                isSpeaking = true;
                updateVoiceStatus('speaking');
            };

            utterance.onend = () => {
                console.log('✅ Synthèse vocale terminée');
                isSpeaking = false;
                updateVoiceStatus('idle');
            };

            utterance.onerror = (error) => {
                console.error('❌ Erreur synthèse vocale:', error);
                isSpeaking = false;
                updateVoiceStatus('error');
            };

            speechSynthesis.speak(utterance);
        }

        async function processVoiceInput(text) {
            try {
                updateVoiceStatus('processing');
                addMessage(text, 'user');

                // Envoyer à l'agent
                const response = await fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: text,
                        conversationId: 'voice-chat',
                        isVoice: true,
                        userQI: 225
                    })
                });

                const data = await response.json();

                if (data.success && data.response) {
                    addMessage(data.response, 'assistant');
                    speakText(data.response);
                } else {
                    const fallbackResponse = "Je n'ai pas pu traiter votre demande. Pouvez-vous répéter ?";
                    addMessage(fallbackResponse, 'assistant');
                    speakText(fallbackResponse);
                }

            } catch (error) {
                console.error('❌ Erreur traitement vocal:', error);
                const errorResponse = "Désolée, j'ai rencontré une erreur technique.";
                addMessage(errorResponse, 'assistant');
                speakText(errorResponse);
            }
        }

        function addMessage(text, sender) {
            const conversationContent = document.getElementById('conversationContent');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const now = new Date();
            const timeString = now.toLocaleTimeString();

            messageDiv.innerHTML = `
                <div>${text}</div>
                <div class="message-time">${timeString}</div>
            `;

            conversationContent.appendChild(messageDiv);
            conversationContent.scrollTop = conversationContent.scrollHeight;
        }

        function sendTextMessage() {
            const textInput = document.getElementById('textInput');
            const text = textInput.value.trim();

            if (text) {
                processVoiceInput(text);
                textInput.value = '';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendTextMessage();
            }
        }

        // Système d'apprentissage YouTube (ÉTAPE 4)
        function initializeYoutubeLearning() {
            console.log('📺 Initialisation du système d'apprentissage YouTube');
            // Cette fonction sera complétée à l'étape 4
        }

        function startYoutubeLearning() {
            console.log('📺 Démarrage de l'apprentissage vocal depuis YouTube');
            updateLearningStatus('Recherche de vidéos avec voix féminines...', 10);

            // Simulation pour l'instant - sera implémenté à l'étape 4
            setTimeout(() => {
                updateLearningStatus('Analyse des patterns vocaux...', 30);
            }, 2000);

            setTimeout(() => {
                updateLearningStatus('Extraction des caractéristiques féminines...', 60);
            }, 4000);

            setTimeout(() => {
                updateLearningStatus('Optimisation de la synthèse vocale...', 90);
            }, 6000);

            setTimeout(() => {
                updateLearningStatus('Apprentissage terminé ! Voix féminine optimisée.', 100);
                // Appliquer les améliorations
                applyYoutubeLearning();
            }, 8000);
        }

        function applyYoutubeLearning() {
            // Optimiser les paramètres de voix basés sur l'apprentissage YouTube
            document.getElementById('voicePitch').value = '1.3';
            document.getElementById('pitchValue').textContent = '1.3';

            document.getElementById('voiceSpeed').value = '0.9';
            document.getElementById('speedValue').textContent = '0.9';

            console.log('✅ Paramètres vocaux optimisés grâce à l\'apprentissage YouTube');

            // Test de la nouvelle voix
            setTimeout(() => {
                const improvedText = "Bonjour Jean-Luc ! J'ai appris de nouvelles intonations féminines grâce à YouTube. Ma voix est maintenant plus naturelle et expressive !";
                speakText(improvedText);
            }, 1000);
        }

        function updateLearningStatus(status, progress) {
            document.getElementById('learningStatus').textContent = status;
            document.getElementById('learningProgress').style.width = progress + '%';
        }
    </script>
</body>
</html>
