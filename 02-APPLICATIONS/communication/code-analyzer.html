<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyseur de Code | Mémoire Thermique</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/css/code-analyzer.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
</head>
<body>
    <div class="analyzer-container">
        <div class="analyzer-header">
            <h1 class="analyzer-title">Analyseur de Code Avancé</h1>
            <div class="system-status">
                <span class="status-label">État:</span>
                <span id="analyzerStatus" class="status-badge status-active">Actif</span>
                <a href="/" class="nav-link"><i class="fas fa-home"></i> Accueil</a>
                <a href="/code-evolution.html" class="nav-link"><i class="fas fa-brain"></i> Évolution</a>
            </div>
        </div>

        <div class="analyzer-content">
            <div class="code-editor-section">
                <div class="section-header">
                    <h2>Éditeur de Code</h2>
                    <div class="language-selector">
                        <label for="languageSelect">Langage:</label>
                        <select id="languageSelect" class="form-control">
                            <option value="python">Python</option>
                            <option value="javascript">JavaScript</option>
                            <option value="java">Java</option>
                            <option value="cpp">C++</option>
                            <option value="html">HTML</option>
                            <option value="css">CSS</option>
                        </select>
                    </div>
                </div>
                <div class="code-editor">
                    <textarea id="codeInput" placeholder="// Entrez votre code ici pour analyse"></textarea>
                </div>
                <div class="editor-actions">
                    <button id="analyzeBtn" class="primary-btn">Analyser</button>
                    <button id="optimizeBtn" class="secondary-btn">Suggérer des optimisations</button>
                    <button id="refactorBtn" class="secondary-btn">Suggérer des refactorisations</button>
                    <button id="clearBtn" class="neutral-btn">Effacer</button>
                </div>
            </div>

            <div class="results-section">
                <div class="section-header">
                    <h2>Résultats d'Analyse</h2>
                    <div class="results-tabs">
                        <span class="tab-item active" data-tab="metrics">Métriques</span>
                        <span class="tab-item" data-tab="suggestions">Suggestions</span>
                        <span class="tab-item" data-tab="visual">Visualisation</span>
                    </div>
                </div>

                <div class="tab-content">
                    <div id="metricsTab" class="tab-pane active">
                        <div class="metrics-container">
                            <div class="metric-card">
                                <div class="metric-header">
                                    <h3>Qualité Globale</h3>
                                </div>
                                <div class="quality-score">
                                    <div id="qualityGauge" class="gauge-container">
                                        <div class="gauge-value">0</div>
                                        <svg viewBox="0 0 100 100" class="gauge">
                                            <circle cx="50" cy="50" r="45" class="gauge-bg"></circle>
                                            <circle cx="50" cy="50" r="45" class="gauge-fill" style="stroke-dasharray: 0 283"></circle>
                                        </svg>
                                    </div>
                                    <div class="quality-label" id="qualityLabel">À analyser</div>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-header">
                                    <h3>Complexité</h3>
                                </div>
                                <div class="complexity-meter">
                                    <div class="meter-container">
                                        <div id="complexityMeter" class="meter" style="width: 0%"></div>
                                    </div>
                                    <div class="complexity-value" id="complexityValue">0</div>
                                    <div class="complexity-label" id="complexityLabel">Simple</div>
                                </div>
                            </div>

                            <div class="metric-card full-width">
                                <div class="metric-header">
                                    <h3>Statistiques du Code</h3>
                                </div>
                                <div class="code-stats">
                                    <div class="stat-item">
                                        <div class="stat-label">Lignes de code</div>
                                        <div class="stat-value" id="totalLines">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Commentaires</div>
                                        <div class="stat-value" id="commentLines">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Ratio commentaires</div>
                                        <div class="stat-value" id="commentRatio">0%</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Fonctions</div>
                                        <div class="stat-value" id="functionCount">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Classes</div>
                                        <div class="stat-value" id="classCount">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Longueur max</div>
                                        <div class="stat-value" id="maxLineLength">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="suggestionsTab" class="tab-pane">
                        <div class="suggestions-container">
                            <div id="noSuggestions" class="no-data-message">
                                Analysez d'abord le code pour recevoir des suggestions
                            </div>
                            <div id="suggestionsList" class="suggestions-list"></div>
                        </div>
                    </div>

                    <div id="visualTab" class="tab-pane">
                        <div class="visual-container">
                            <div class="chart-container">
                                <canvas id="metricsChart"></canvas>
                            </div>
                            <div class="code-structure">
                                <h3>Structure du Code</h3>
                                <div id="codeStructure" class="structure-tree">
                                    <div class="no-data-message">
                                        Analysez d'abord le code pour voir sa structure
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Section d'apprentissage thermique -->
            <div class="analyzer-section" id="thermalLearningSection">
                <h3 class="section-title">
                    <i class="fas fa-brain"></i>
                    Apprentissage Thermique
                </h3>
                <div class="thermal-learning-container">
                    <div class="thermal-learning-stats">
                        <div class="stat-card">
                            <div class="stat-title">Patterns Appris</div>
                            <div class="stat-value" id="patternsLearned">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-title">Température Moyenne</div>
                            <div class="stat-value" id="averageTemperature">37.0°C</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-title">Qualité Moyenne</div>
                            <div class="stat-value" id="averageQuality">0%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-title">Sessions d'Analyse</div>
                            <div class="stat-value" id="analysisCount">0</div>
                        </div>
                    </div>
                    
                    <div class="thermal-learning-categories">
                        <h4>Catégories de Patterns</h4>
                        <div class="categories-grid" id="patternCategories">
                            <!-- Généré dynamiquement -->
                        </div>
                    </div>
                    
                    <div class="thermal-learning-controls">
                        <label class="toggle-switch">
                            <input type="checkbox" id="autonomousLearningToggle" checked>
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Apprentissage Autonome</span>
                        </label>
                        <button id="viewPatternLibrary" class="action-button">
                            <i class="fas fa-book"></i>
                            Bibliothèque de Patterns
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Templates pour les suggestions -->
    <template id="suggestionTemplate">
        <div class="suggestion-item">
            <div class="suggestion-severity"></div>
            <div class="suggestion-content">
                <div class="suggestion-type"></div>
                <div class="suggestion-message"></div>
            </div>
        </div>
    </template>

    <!-- Templates pour la structure du code -->
    <template id="structureItemTemplate">
        <div class="structure-item">
            <div class="structure-icon"></div>
            <div class="structure-name"></div>
            <div class="structure-children"></div>
        </div>
    </template>

    <!-- Modal pour la bibliothèque de patterns -->
    <div id="patternLibraryModal" class="pattern-library-modal">
        <div class="pattern-library-content">
            <div class="pattern-library-header">
                <h2 class="pattern-library-title">Bibliothèque de Patterns d'Apprentissage</h2>
                <button id="closePatternLibrary" class="pattern-library-close">&times;</button>
            </div>
            <div class="pattern-filters">
                <select id="patternTypeFilter" class="dropdown-filter">
                    <option value="all">Tous les types</option>
                    <option value="design_pattern">Design Patterns</option>
                    <option value="function_pattern">Fonctions</option>
                    <option value="class_pattern">Classes</option>
                    <option value="meta_pattern">Méta-patterns</option>
                </select>
                <select id="patternTempFilter" class="dropdown-filter">
                    <option value="all">Toutes températures</option>
                    <option value="hot">Chauds (>38°C)</option>
                    <option value="warm">Tièdes (37-38°C)</option>
                    <option value="cool">Frais (<37°C)</option>
                </select>
                <select id="patternLangFilter" class="dropdown-filter">
                    <option value="all">Tous langages</option>
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="java">Java</option>
                    <option value="csharp">C#</option>
                    <option value="multi">Multi-langage</option>
                </select>
            </div>
            <div id="patternGrid" class="pattern-grid">
                <!-- Les patterns seront générés dynamiquement ici -->
                <div class="no-patterns-message" id="noPatternsMessage">
                    Pas encore de patterns appris. Analysez du code pour commencer l'apprentissage.
                </div>
            </div>
        </div>
    </div>

    <script src="/js/neural-animation.js"></script>
    <script src="/js/code-analyzer.js"></script>
    <script src="/js/code-memory-connector.js"></script>
    <script>
        // Configuration de l'animation neuronale
        document.addEventListener('DOMContentLoaded', () => {
            // Initialiser l'animation neuronale
            if (window.initNeuralAnimation) {
                initNeuralAnimation();
            }
        });
    </script>
</body>
</html>
