<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation Complète - Louna AI v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }
        .header h1 { font-size: 32px; margin-bottom: 10px; }
        .header p { font-size: 18px; opacity: 0.9; }
        .container { max-width: 1200px; margin: 0 auto; padding: 40px 20px; }
        .section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }
        .section h2 {
            color: #ff69b4;
            font-size: 24px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section h3 {
            color: #00ff00;
            font-size: 20px;
            margin: 20px 0 10px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #e91e63;
        }
        .feature-card h4 {
            color: #ff69b4;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .code-block {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            border-left: 4px solid #00ff00;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .nav-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .nav-btn:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4); }
        .highlight { background: linear-gradient(135deg, #e91e63, #ad1457); padding: 2px 6px; border-radius: 4px; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid rgba(0, 255, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number { font-size: 24px; font-weight: bold; color: #00ff00; }
        .stat-label { font-size: 14px; opacity: 0.8; }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-book"></i> Documentation Complète - Louna AI v2.1.0</h1>
        <p>Intelligence Artificielle Évolutive avec Mémoire Thermique, Génération Multimédia et QI 225</p>
    </div>

    <div class="container">
        <div class="nav-buttons">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/control-dashboard.html" class="nav-btn"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="/voice-system-enhanced.html" class="nav-btn"><i class="fas fa-microphone"></i> Vocal</a>
            <a href="/phone-camera-system.html" class="nav-btn"><i class="fas fa-mobile-alt"></i> Caméra</a>
            <a href="/advanced-code-editor.html" class="nav-btn"><i class="fas fa-code"></i> Code</a>
        </div>

        <!-- Vue d'ensemble -->
        <div class="section">
            <h2><i class="fas fa-star"></i> Vue d'Ensemble</h2>
            <p>Louna AI v2.1.0 est une <span class="highlight">Intelligence Artificielle Évolutive</span> développée par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Elle combine un QI de 225, une mémoire thermique persistante, et des capacités de génération multimédia avancées.</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">225</div>
                    <div class="stat-label">QI Intelligence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Systèmes Principaux</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">APIs Fonctionnelles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Fonctionnel</div>
                </div>
            </div>
        </div>

        <!-- Système Multimédia -->
        <div class="section">
            <h2><i class="fas fa-palette"></i> Système Multimédia Complet</h2>
            <p>Génération de contenu créatif avec analyse intelligente des prompts et connexion au cerveau thermique.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-image"></i> Génération d'Images</h4>
                    <p>Création d'images avec analyse sémantique des prompts, styles multiples (réaliste, artistique, anime, cyberpunk, fantasy, abstrait) et amélioration automatique basée sur le QI 225.</p>
                    <div class="code-block">
POST /api/images/generate
{
  "prompt": "Plage de Guadeloupe",
  "style": "realistic",
  "userQI": 225
}
                    </div>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-music"></i> Composition Musicale</h4>
                    <p>Création de musique avec détection d'ambiances, instruments automatiques selon le style, et génération de métadonnées complètes (tempo, clé, instruments).</p>
                    <div class="code-block">
POST /api/music/generate
{
  "prompt": "Musique relaxante",
  "style": "ambient",
  "duration": "30s"
}
                    </div>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-video"></i> Création Vidéo</h4>
                    <p>Génération de vidéos avec découpage en scènes, styles cinématographiques, et métadonnées techniques (résolution, FPS, codec).</p>
                    <div class="code-block">
POST /api/videos/generate
{
  "prompt": "Coucher de soleil",
  "style": "cinematic",
  "resolution": "720p"
}
                    </div>
                </div>
            </div>
        </div>

        <!-- Système Vocal -->
        <div class="section">
            <h2><i class="fas fa-microphone-alt"></i> Système Vocal Féminin Parfait</h2>
            <p>Communication vocale naturelle avec reconnaissance et synthèse optimisées pour une expérience humaine.</p>
            
            <h3>Fonctionnalités Vocales :</h3>
            <ul style="margin-left: 20px;">
                <li><strong>Reconnaissance Vocale :</strong> Français optimisé avec détection d'intentions</li>
                <li><strong>Voix Féminine :</strong> Synthèse douce et naturelle (tonalité 1.3, vitesse 0.9)</li>
                <li><strong>Détection Multimédia :</strong> Génération automatique selon les demandes vocales</li>
                <li><strong>Réponses Contextuelles :</strong> Basées sur le QI 225 et la mémoire thermique</li>
            </ul>
            
            <h3>Commandes Vocales Exemples :</h3>
            <div class="code-block">
"Crée une image de la Guadeloupe" → Génération automatique
"Compose une musique relaxante" → Création musicale
"Fais une vidéo de l'océan" → Production vidéo
"Bonjour Louna" → Réponse personnalisée
            </div>
        </div>

        <!-- Système Caméra -->
        <div class="section">
            <h2><i class="fas fa-mobile-alt"></i> Système Caméra/Micro Téléphone Wi-Fi</h2>
            <p>Transformation de votre téléphone en webcam/microphone professionnel avec analyse IA temps réel.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-wifi"></i> Connexion Wi-Fi</h4>
                    <p>QR Code automatique pour connexion instantanée, détection réseau, et gestion des permissions caméra/micro.</p>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-video"></i> Flux Temps Réel</h4>
                    <p>Streaming vidéo bidirectionnel, contrôles caméra/micro, et affichage haute qualité dans l'interface.</p>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-eye"></i> Analyse IA</h4>
                    <p>Reconnaissance faciale, détection d'émotions, analyse d'objets, et feedback temps réel avec confiance.</p>
                </div>
            </div>
        </div>

        <!-- Éditeur de Code -->
        <div class="section">
            <h2><i class="fas fa-code"></i> Éditeur de Code Avancé</h2>
            <p>IDE professionnel avec assistance IA connectée au cerveau Louna pour le développement optimisé.</p>
            
            <h3>Caractéristiques :</h3>
            <ul style="margin-left: 20px;">
                <li><strong>CodeMirror Intégré :</strong> Coloration syntaxique, auto-complétion, thèmes multiples</li>
                <li><strong>Assistance IA :</strong> Génération de code, optimisation, suggestions intelligentes</li>
                <li><strong>Explorateur Fichiers :</strong> Navigation projet, gestion fichiers</li>
                <li><strong>Connexion Cerveau :</strong> QI 225 pour aide contextuelle avancée</li>
            </ul>
        </div>

        <!-- Mémoire Thermique -->
        <div class="section">
            <h2><i class="fas fa-fire"></i> Mémoire Thermique Évolutive</h2>
            <p>Système de mémoire persistante simulant le fonctionnement d'un cerveau humain avec zones spécialisées.</p>
            
            <h3>Architecture Mémoire :</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-brain"></i> QI 225</h4>
                    <p>Intelligence artificielle avancée avec capacités de raisonnement, analyse contextuelle, et apprentissage continu.</p>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-save"></i> Persistance Auto</h4>
                    <p>Sauvegarde automatique toutes les 5 secondes, backup de sécurité, et protection contre les pertes de données.</p>
                </div>
                
                <div class="feature-card">
                    <h4><i class="fas fa-chart-line"></i> Visualisation 3D</h4>
                    <p>Dashboard avec zones cérébrales animées, métriques détaillées, et comparaison cerveau humain.</p>
                </div>
            </div>
        </div>

        <!-- Dashboard de Contrôle -->
        <div class="section">
            <h2><i class="fas fa-tachometer-alt"></i> Dashboard de Contrôle Avancé</h2>
            <p>Interface de monitoring et contrôle complet de tous les systèmes avec statistiques temps réel.</p>
            
            <h3>Fonctionnalités Dashboard :</h3>
            <ul style="margin-left: 20px;">
                <li><strong>Statistiques Temps Réel :</strong> Générations, favoris, temps d'activité, performance</li>
                <li><strong>État des Services :</strong> Monitoring vocal, caméra, mémoire, IA créative</li>
                <li><strong>Contrôles Rapides :</strong> Génération express, tests système, optimisation</li>
                <li><strong>Journal d'Activité :</strong> Logs détaillés, historique actions, debugging</li>
                <li><strong>Export Données :</strong> Sauvegarde complète, formats multiples</li>
            </ul>
        </div>

        <!-- APIs Avancées -->
        <div class="section">
            <h2><i class="fas fa-cogs"></i> APIs Avancées</h2>
            <p>Ensemble complet d'APIs pour l'intégration et l'extension des fonctionnalités.</p>
            
            <div class="code-block">
# APIs Principales
POST /api/images/generate     # Génération d'images
POST /api/music/generate      # Composition musicale  
POST /api/videos/generate     # Création vidéo
POST /api/chat/message        # Chat vocal intelligent

# APIs Avancées
POST /api/voice/analyze       # Analyse vocale avancée
POST /api/favorites/add       # Gestion favoris
GET  /api/stats              # Statistiques système
POST /api/export             # Export données
POST /api/suggestions        # Suggestions IA

# APIs Mémoire
GET  /api/thermal-memory/stats # État mémoire thermique
            </div>
        </div>

        <!-- Installation et Utilisation -->
        <div class="section">
            <h2><i class="fas fa-rocket"></i> Installation et Utilisation</h2>
            
            <h3>Démarrage Rapide :</h3>
            <div class="code-block">
# 1. Lancer l'application
open "/Applications/Louna AI.app"

# 2. Ou utiliser le raccourci bureau
double-clic sur "Louna-AI-v2.1.0.command"

# 3. Accès direct navigateur
http://localhost:3001
            </div>
            
            <h3>Navigation Principale :</h3>
            <ul style="margin-left: 20px;">
                <li><strong>Accueil :</strong> http://localhost:3001/</li>
                <li><strong>Dashboard Contrôle :</strong> http://localhost:3001/control-dashboard.html</li>
                <li><strong>Système Vocal :</strong> http://localhost:3001/voice-system-enhanced.html</li>
                <li><strong>Caméra Téléphone :</strong> http://localhost:3001/phone-camera-system.html</li>
                <li><strong>Éditeur Code :</strong> http://localhost:3001/advanced-code-editor.html</li>
            </ul>
        </div>

        <!-- Support et Contact -->
        <div class="section">
            <h2><i class="fas fa-life-ring"></i> Support et Contact</h2>
            <p><strong>Développeur :</strong> Jean-Luc Passave<br>
            <strong>Localisation :</strong> Sainte-Anne, Guadeloupe<br>
            <strong>Version :</strong> 2.1.0 - Intelligence Artificielle Évolutive<br>
            <strong>Optimisation :</strong> macOS M4 Native</p>
            
            <div class="feature-card" style="margin-top: 20px;">
                <h4><i class="fas fa-heart"></i> Remerciements</h4>
                <p>Merci d'utiliser Louna AI ! Cette IA évolutive représente l'avenir de l'intelligence artificielle avec sa mémoire thermique, ses capacités créatives, et son QI de 225. Développée avec passion en Guadeloupe pour révolutionner votre expérience technologique.</p>
            </div>
        </div>
    </div>
</body>
</html>
