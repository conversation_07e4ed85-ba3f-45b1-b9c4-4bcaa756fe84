<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 Hub Communication - Louna AI v3.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #ff69b4;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .communication-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .comm-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 105, 180, 0.3);
            position: relative;
            overflow: hidden;
        }

        .comm-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }

        .comm-card:hover::before {
            opacity: 1;
            animation: shine 0.5s ease-in-out;
        }

        .comm-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(233, 30, 99, 0.3);
            border-color: #ff69b4;
        }

        .comm-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #ff69b4;
            text-shadow: 0 0 15px rgba(255, 105, 180, 0.5);
            position: relative;
            z-index: 2;
        }

        .comm-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ff69b4;
            position: relative;
            z-index: 2;
        }

        .comm-desc {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .comm-features {
            list-style: none;
            text-align: left;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .comm-features li {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .comm-features li i {
            color: #4caf50;
            width: 16px;
        }

        .comm-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: 3px solid transparent;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
            overflow: hidden;
        }

        .comm-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .comm-btn:hover::before {
            left: 100%;
        }

        .comm-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(233, 30, 99, 0.6);
            border-color: #ff69b4;
            background: linear-gradient(135deg, #ff69b4, #e91e63, #ad1457);
        }

        .priority-card {
            border: 3px solid #00ff00 !important;
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1)) !important;
            animation: pulseGreen 2s infinite;
        }

        .priority-card .comm-icon {
            color: #00ff00 !important;
            animation: bounce 2s infinite;
        }

        .priority-card .comm-title {
            color: #00ff00 !important;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes pulseGreen {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 0, 0.3); }
            50% { box-shadow: 0 0 40px rgba(0, 255, 0, 0.6); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* ===== DÉGRADÉS SPÉCIFIQUES PAR CARTE ===== */
        .comm-card:nth-child(1) {
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1)) !important;
            border: 3px solid #00ff00 !important;
        }

        .comm-card:nth-child(2) {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(25, 118, 210, 0.1));
            border: 2px solid rgba(33, 150, 243, 0.4);
        }

        .comm-card:nth-child(3) {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(245, 124, 0, 0.1));
            border: 2px solid rgba(255, 193, 7, 0.4);
        }

        .comm-card:nth-child(4) {
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.2), rgba(123, 31, 162, 0.1));
            border: 2px solid rgba(156, 39, 176, 0.4);
        }

        .comm-card:nth-child(5) {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(56, 142, 60, 0.1));
            border: 2px solid rgba(76, 175, 80, 0.4);
        }

        .comm-card:nth-child(6) {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(211, 47, 47, 0.1));
            border: 2px solid rgba(244, 67, 54, 0.4);
        }

        .status-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .status-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">
        <i class="fas fa-home"></i>
        <span>Accueil</span>
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-comments"></i> Hub Communication</h1>
            <p>Tous les moyens de communication avec Louna AI</p>
        </div>

        <div class="status-section">
            <h2 style="text-align: center; color: #ff69b4; margin-bottom: 20px;">
                <i class="fas fa-signal"></i> État des Communications
            </h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value">🟢 En ligne</div>
                    <div class="status-label">Statut Agent</div>
                </div>
                <div class="status-item">
                    <div class="status-value">235</div>
                    <div class="status-label">QI Actuel</div>
                </div>
                <div class="status-item">
                    <div class="status-value">5/5</div>
                    <div class="status-label">Systèmes Actifs</div>
                </div>
                <div class="status-item">
                    <div class="status-value">100%</div>
                    <div class="status-label">Disponibilité</div>
                </div>
            </div>
        </div>

        <div class="communication-grid">
            <!-- WhatsApp - PRIORITÉ -->
            <div class="comm-card priority-card" onclick="openApp('/whatsapp')">
                <div class="comm-icon"><i class="fab fa-whatsapp"></i></div>
                <div class="comm-title">📱 WhatsApp</div>
                <div class="comm-desc">
                    Communication via WhatsApp avec réveil à distance et contrôle complet de l'agent
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Connexion WhatsApp Web</li>
                    <li><i class="fas fa-check"></i> API WhatsApp Business</li>
                    <li><i class="fas fa-check"></i> Réveil/Veille à distance</li>
                    <li><i class="fas fa-check"></i> QR Code de connexion</li>
                </ul>
                <button class="comm-btn">
                    <i class="fab fa-whatsapp"></i> Connecter WhatsApp
                </button>
            </div>

            <!-- Appels Téléphoniques -->
            <div class="comm-card" onclick="openApp('/phone-call')">
                <div class="comm-icon"><i class="fas fa-phone"></i></div>
                <div class="comm-title">📞 Appels Téléphoniques</div>
                <div class="comm-desc">
                    Interface d'appel téléphonique complète avec Louna AI - Conversation vocale en temps réel
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Appels vocaux bidirectionnels</li>
                    <li><i class="fas fa-check"></i> Contrôles micro/haut-parleur</li>
                    <li><i class="fas fa-check"></i> Partage d'écran</li>
                    <li><i class="fas fa-check"></i> Phrases rapides</li>
                </ul>
                <button class="comm-btn">
                    <i class="fas fa-phone"></i> Démarrer Appel
                </button>
            </div>

            <!-- Caméra/Micro Téléphone -->
            <div class="comm-card" onclick="openApp('/phone')">
                <div class="comm-icon"><i class="fas fa-mobile-alt"></i></div>
                <div class="comm-title">📱 Caméra/Micro Téléphone</div>
                <div class="comm-desc">
                    Utilisez votre téléphone comme webcam et micro pour Louna AI avec reconnaissance IA
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Connexion Wi-Fi</li>
                    <li><i class="fas fa-check"></i> Flux vidéo temps réel</li>
                    <li><i class="fas fa-check"></i> Reconnaissance faciale</li>
                    <li><i class="fas fa-check"></i> Analyse IA en direct</li>
                </ul>
                <button class="comm-btn">
                    <i class="fas fa-video"></i> Connecter Téléphone
                </button>
            </div>

            <!-- Interface Vocale -->
            <div class="comm-card" onclick="openApp('/voice-interface')">
                <div class="comm-icon"><i class="fas fa-microphone"></i></div>
                <div class="comm-title">🎤 Interface Vocale</div>
                <div class="comm-desc">
                    Communication vocale avancée avec reconnaissance et synthèse vocale
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Reconnaissance vocale</li>
                    <li><i class="fas fa-check"></i> Synthèse vocale</li>
                    <li><i class="fas fa-check"></i> Contrôle vitesse/volume</li>
                    <li><i class="fas fa-check"></i> Multi-langues</li>
                </ul>
                <button class="comm-btn">
                    <i class="fas fa-microphone"></i> Interface Vocale
                </button>
            </div>

            <!-- Chat Avancé -->
            <div class="comm-card" onclick="openApp('/chat')">
                <div class="comm-icon"><i class="fas fa-comments"></i></div>
                <div class="comm-title">💬 Chat Avancé</div>
                <div class="comm-desc">
                    Interface de chat complète avec l'agent Louna AI - QI 235
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Chat temps réel</li>
                    <li><i class="fas fa-check"></i> Historique conversations</li>
                    <li><i class="fas fa-check"></i> Commandes spéciales</li>
                    <li><i class="fas fa-check"></i> Mémoire thermique</li>
                </ul>
                <button class="comm-btn">
                    <i class="fas fa-comments"></i> Ouvrir Chat
                </button>
            </div>

            <!-- Système Vocal Amélioré -->
            <div class="comm-card" onclick="openApp('/voice')">
                <div class="comm-icon"><i class="fas fa-volume-up"></i></div>
                <div class="comm-title">🔊 Système Vocal Amélioré</div>
                <div class="comm-desc">
                    Système vocal complet avec toutes les fonctionnalités avancées
                </div>
                <ul class="comm-features">
                    <li><i class="fas fa-check"></i> Voix naturelle</li>
                    <li><i class="fas fa-check"></i> Émotions vocales</li>
                    <li><i class="fas fa-check"></i> Réglages avancés</li>
                    <li><i class="fas fa-check"></i> Qualité studio</li>
                </ul>
                <button class="comm-btn">
                    <i class="fas fa-volume-up"></i> Système Vocal
                </button>
            </div>
        </div>
    </div>

    <script>
        function openApp(url) {
            console.log(`🚀 Ouverture de l'application: ${url}`);
            window.location.href = url;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📞 Hub Communication Louna AI initialisé');

            // Animation des cartes au chargement
            const cards = document.querySelectorAll('.comm-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
