<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Sauvegarde Système - Louna</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ff69b4;
        }

        .header h1 {
            color: #ff69b4;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .status-panel {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid #00ff00;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .status-panel h2 {
            color: #00ff00;
            margin-bottom: 15px;
            font-size: 1.8em;
        }

        .optimal-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn-save {
            background: linear-gradient(45deg, #00ff00, #32cd32);
            color: #000;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 255, 0, 0.3);
        }

        .btn-restore {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: #fff;
        }

        .btn-restore:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 105, 180, 0.3);
        }

        .btn-list {
            background: linear-gradient(45deg, #4169e1, #1e90ff);
            color: #fff;
        }

        .btn-list:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(65, 105, 225, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .backups-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .backups-section h3 {
            color: #ff69b4;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .backup-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #4169e1;
            transition: all 0.3s ease;
        }

        .backup-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .backup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .backup-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #4169e1;
        }

        .backup-date {
            color: #ccc;
            font-size: 0.9em;
        }

        .backup-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .backup-feature {
            background: rgba(0, 255, 0, 0.1);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9em;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .stats-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.9em;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #ff69b4;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }

        .message.success {
            background: rgba(0, 255, 0, 0.2);
            border: 2px solid #00ff00;
            color: #00ff00;
        }

        .message.error {
            background: rgba(255, 0, 0, 0.2);
            border: 2px solid #ff0000;
            color: #ff0000;
        }

        .message.warning {
            background: rgba(255, 165, 0, 0.2);
            border: 2px solid #ffa500;
            color: #ffa500;
        }

        .nav-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .backup-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="nav-button">← Retour Accueil</a>

    <div class="container">
        <div class="header">
            <h1>🛡️ Gestionnaire de Sauvegarde Système</h1>
            <p>Préservez et restaurez la configuration optimale de Louna</p>
        </div>

        <div class="status-panel">
            <h2>✅ État Optimal Actuel</h2>
            <p><strong>Configuration Ultra-Rapide</strong> - Réponses en 3-6ms</p>
            <div class="optimal-features">
                <div class="feature-item">⚡ Timeout réduit à 8s</div>
                <div class="feature-item">🚀 Mode turbo automatique</div>
                <div class="feature-item">🧠 APIs cérébrales corrigées</div>
                <div class="feature-item">🛡️ Système de fallback intelligent</div>
                <div class="feature-item">⚙️ Accélérateurs KYBER actifs</div>
                <div class="feature-item">📊 Réponses ultra-rapides</div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-save" onclick="saveOptimalState()">
                💾 Sauvegarder État Optimal
            </button>
            <button class="btn btn-restore" onclick="restoreOptimalState()">
                🔄 Restaurer État Optimal
            </button>
            <button class="btn btn-list" onclick="loadBackups()">
                📋 Voir Sauvegardes
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Traitement en cours...</p>
        </div>

        <div id="messages"></div>

        <div class="stats-panel">
            <h3>📊 Statistiques du Système</h3>
            <div class="stats-grid" id="statsGrid">
                <!-- Les statistiques seront chargées ici -->
            </div>
        </div>

        <div class="backups-section">
            <h3>📁 Sauvegardes Disponibles</h3>
            <div id="backupsList">
                <p style="text-align: center; color: #ccc;">Cliquez sur "Voir Sauvegardes" pour charger la liste</p>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let backups = [];
        let stats = {};

        // Charger les statistiques au démarrage
        window.addEventListener('load', () => {
            loadStats();
        });

        /**
         * Affiche un message
         */
        function showMessage(text, type = 'success') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            
            messagesDiv.appendChild(messageDiv);
            
            // Supprimer le message après 5 secondes
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        /**
         * Affiche/cache le loading
         */
        function toggleLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        /**
         * Sauvegarde l'état optimal
         */
        async function saveOptimalState() {
            try {
                toggleLoading(true);
                
                const response = await fetch('/api/system-backup/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(`✅ État optimal sauvegardé avec succès ! (${result.filesCount} fichiers)`, 'success');
                    loadStats();
                    loadBackups();
                } else {
                    showMessage(`❌ Erreur lors de la sauvegarde: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showMessage(`❌ Erreur de communication: ${error.message}`, 'error');
            } finally {
                toggleLoading(false);
            }
        }

        /**
         * Restaure l'état optimal
         */
        async function restoreOptimalState(backupName = null) {
            if (!backupName && backups.length === 0) {
                showMessage('⚠️ Aucune sauvegarde disponible. Créez d\'abord une sauvegarde.', 'warning');
                return;
            }
            
            const confirmMessage = backupName 
                ? `Voulez-vous restaurer la sauvegarde "${backupName}" ?`
                : 'Voulez-vous restaurer la sauvegarde la plus récente ?';
                
            if (!confirm(confirmMessage + '\n\nCela remplacera la configuration actuelle.')) {
                return;
            }
            
            try {
                toggleLoading(true);
                
                const response = await fetch('/api/system-backup/restore', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ backupName })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(`✅ Restauration réussie ! (${result.restoredFiles} fichiers)`, 'success');
                    showMessage('🔄 Redémarrage de l\'application recommandé', 'warning');
                    loadStats();
                } else {
                    showMessage(`❌ Erreur lors de la restauration: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showMessage(`❌ Erreur de communication: ${error.message}`, 'error');
            } finally {
                toggleLoading(false);
            }
        }

        /**
         * Charge la liste des sauvegardes
         */
        async function loadBackups() {
            try {
                toggleLoading(true);
                
                const response = await fetch('/api/system-backup/list');
                const result = await response.json();
                
                if (result.success) {
                    backups = result.backups;
                    displayBackups();
                    showMessage(`📋 ${backups.length} sauvegarde(s) trouvée(s)`, 'success');
                } else {
                    showMessage(`❌ Erreur lors du chargement: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showMessage(`❌ Erreur de communication: ${error.message}`, 'error');
            } finally {
                toggleLoading(false);
            }
        }

        /**
         * Affiche les sauvegardes
         */
        function displayBackups() {
            const backupsList = document.getElementById('backupsList');
            
            if (backups.length === 0) {
                backupsList.innerHTML = '<p style="text-align: center; color: #ccc;">Aucune sauvegarde trouvée</p>';
                return;
            }
            
            backupsList.innerHTML = backups.map(backup => `
                <div class="backup-item">
                    <div class="backup-header">
                        <div class="backup-name">${backup.displayName || backup.name}</div>
                        <div class="backup-date">${new Date(backup.timestamp).toLocaleString('fr-FR')}</div>
                    </div>
                    <p><strong>Version:</strong> ${backup.version || 'N/A'}</p>
                    <p><strong>Description:</strong> ${backup.description || 'Aucune description'}</p>
                    <p><strong>Fichiers:</strong> ${backup.filesCount || 0}</p>
                    ${backup.features ? `
                        <div class="backup-features">
                            ${backup.features.map(feature => `<div class="backup-feature">${feature}</div>`).join('')}
                        </div>
                    ` : ''}
                    <div style="margin-top: 15px;">
                        <button class="btn btn-restore" style="padding: 8px 16px; font-size: 0.9em;" 
                                onclick="restoreOptimalState('${backup.name}')">
                            🔄 Restaurer cette sauvegarde
                        </button>
                    </div>
                </div>
            `).join('');
        }

        /**
         * Charge les statistiques
         */
        async function loadStats() {
            try {
                const response = await fetch('/api/system-backup/stats');
                const result = await response.json();
                
                if (result.success) {
                    stats = result.stats;
                    displayStats();
                }
                
            } catch (error) {
                console.error('Erreur chargement stats:', error);
            }
        }

        /**
         * Affiche les statistiques
         */
        function displayStats() {
            const statsGrid = document.getElementById('statsGrid');
            
            statsGrid.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${stats.totalBackups || 0}</div>
                    <div class="stat-label">Sauvegardes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.criticalFiles || 0}</div>
                    <div class="stat-label">Fichiers Critiques</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.totalSize || 0} MB</div>
                    <div class="stat-label">Taille Totale</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.latestBackup ? '✅' : '❌'}</div>
                    <div class="stat-label">Dernière Sauvegarde</div>
                </div>
            `;
        }
    </script>
</body>
</html>
