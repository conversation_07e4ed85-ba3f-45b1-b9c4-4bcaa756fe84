<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Intelligent avec Réflexions - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 10px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1000;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
        }

        .qi-display {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: 600;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Navigation horizontale */
        .horizontal-nav {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            gap: 10px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .horizontal-nav::-webkit-scrollbar {
            display: none;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            font-size: 13px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.4);
        }

        .main-container {
            display: flex;
            height: calc(100vh - 120px);
        }

        /* Sidebar rétractable */
        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: width 0.3s ease;
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #ff69b4;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-content {
            padding: 15px;
            overflow-y: auto;
            height: calc(100% - 60px);
        }

        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-content {
            display: none;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .agent-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .agent-card.active {
            border-color: #e91e63;
            background: rgba(233, 30, 99, 0.2);
        }

        .agent-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .agent-role {
            font-size: 12px;
            color: #b0b0b0;
            margin-bottom: 8px;
        }

        .agent-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .status-dot.inactive {
            background: #f44336;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-avatar {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-content {
            max-width: 70%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-content {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            opacity: 0.8;
        }

        .message-text {
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .chat-input-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .chat-input:focus {
            outline: none;
            border-color: #e91e63;
            box-shadow: 0 0 10px rgba(233, 30, 99, 0.3);
        }

        .send-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e91e63;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .agent-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .stats-panel {
            position: fixed;
            top: 70px;
            right: -300px;
            width: 300px;
            height: calc(100vh - 70px);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            transition: right 0.3s ease;
            overflow-y: auto;
        }

        .stats-panel.open {
            right: 0;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stats-title {
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: #e91e63;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 12px;
            color: #b0b0b0;
        }

        /* Styles pour la nouvelle interface */
        .sidebar-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 12px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 105, 180, 0.3);
        }

        .sidebar-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 13px;
            text-align: left;
        }

        .sidebar-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .sidebar-btn.active {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-avatar {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-content {
            max-width: 75%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-content {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            opacity: 0.9;
            font-weight: 600;
        }

        .message-text {
            line-height: 1.6;
            white-space: pre-wrap;
        }

        .chat-input-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-bottom: 10px;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: all 0.3s ease;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .chat-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }

        .send-btn, .voice-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .voice-btn {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .send-btn:hover, .voice-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .voice-btn:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .input-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Boutons de feedback pour apprentissage par renforcement */
        .feedback-buttons {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .feedback-buttons:hover {
            opacity: 1;
        }

        .feedback-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .feedback-btn:hover {
            transform: scale(1.1);
        }

        .feedback-btn.positive:hover {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
        }

        .feedback-btn.neutral:hover {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);
        }

        .feedback-btn.negative:hover {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
        }

        .feedback-btn.improve:hover {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
        }

        .feedback-btn.selected {
            transform: scale(1.1);
            opacity: 1;
        }

        .feedback-btn.selected.positive {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .feedback-btn.selected.neutral {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .feedback-btn.selected.negative {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .feedback-btn.selected.improve {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        /* Panel de réflexions */
        .reflections-panel {
            position: fixed;
            top: 120px;
            right: -350px;
            width: 350px;
            height: calc(100vh - 120px);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            transition: right 0.3s ease;
            z-index: 1000;
        }

        .reflections-panel.open {
            right: 0;
        }

        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 105, 180, 0.2);
        }

        .panel-header h3 {
            margin: 0;
            font-size: 16px;
            color: #ff69b4;
        }

        .panel-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .panel-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .panel-content {
            padding: 20px;
            overflow-y: auto;
            height: calc(100% - 70px);
        }

        .reflection-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #ff69b4;
        }

        .reflection-time {
            font-size: 11px;
            color: #ff69b4;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .reflection-text {
            font-size: 13px;
            line-height: 1.5;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            margin: 0 20px 20px 20px;
            backdrop-filter: blur(10px);
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            margin-left: 10px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff69b4;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }

            .sidebar.collapsed {
                width: 0;
            }

            .message-content {
                max-width: 85%;
            }

            .reflections-panel {
                width: 100%;
                right: -100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-comments"></i>
            Chat Intelligent avec Réflexions
        </h1>
        <div class="header-info">
            <div class="qi-display">
                <i class="fas fa-brain"></i>
                QI: <span id="currentQI">203</span>
            </div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="agentStatus">Agent connecté</span>
            </div>
        </div>
    </div>

    <!-- Navigation horizontale -->
    <div class="horizontal-nav">
        <button class="nav-btn active" onclick="setActiveSection('chat')" data-section="chat">
            <i class="fas fa-comments"></i>
            Chat Intelligent
        </button>
        <button class="nav-btn" onclick="setActiveSection('generation')" data-section="generation">
            <i class="fas fa-magic"></i>
            Génération
        </button>
        <button class="nav-btn" onclick="setActiveSection('development')" data-section="development">
            <i class="fas fa-code"></i>
            Développement
        </button>
        <button class="nav-btn" onclick="setActiveSection('analysis')" data-section="analysis">
            <i class="fas fa-chart-line"></i>
            Analyse
        </button>
        <button class="nav-btn" onclick="setActiveSection('search')" data-section="search">
            <i class="fas fa-search"></i>
            Recherche Web
        </button>
        <button class="nav-btn" onclick="setActiveSection('recognition')" data-section="recognition">
            <i class="fas fa-user-check"></i>
            Reconnaissance
        </button>
        <button class="nav-btn" onclick="setActiveSection('learning')" data-section="learning">
            <i class="fas fa-graduation-cap"></i>
            Apprentissage
        </button>
        <a href="/brain-dashboard-live.html" class="nav-btn">
            <i class="fas fa-brain"></i>
            Mémoire Thermique
        </a>
        <a href="/brain-monitoring-complete.html" class="nav-btn">
            <i class="fas fa-heartbeat"></i>
            Monitoring
        </a>
        <a href="/settings-advanced.html" class="nav-btn">
            <i class="fas fa-cog"></i>
            Paramètres
        </a>
        <a href="/evolution-learning-center.html" class="nav-btn">
            <i class="fas fa-graduation-cap"></i>
            Agents
        </a>
        <a href="/" class="nav-btn">
            <i class="fas fa-home"></i>
            Accueil
        </a>
    </div>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- Sidebar rétractable -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">PRINCIPAL</div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-content">
                <!-- Section Chat Intelligent -->
                <div class="sidebar-section">
                    <div class="section-title">
                        <i class="fas fa-comments"></i>
                        Chat Intelligent
                    </div>
                    <button class="sidebar-btn active" onclick="selectAgent('main')">
                        <i class="fas fa-brain"></i>
                        Louna Principal
                    </button>
                    <button class="sidebar-btn" onclick="selectAgent('deepseek')">
                        <i class="fas fa-graduation-cap"></i>
                        DeepSeek Formation
                    </button>
                </div>

                <!-- Section Système -->
                <div class="sidebar-section">
                    <div class="section-title">
                        <i class="fas fa-cog"></i>
                        Système
                    </div>
                    <button class="sidebar-btn" onclick="openMemoryThermal()">
                        <i class="fas fa-fire"></i>
                        Mémoire Thermique
                    </button>
                    <button class="sidebar-btn" onclick="openMonitoring()">
                        <i class="fas fa-heartbeat"></i>
                        Monitoring
                    </button>
                    <button class="sidebar-btn" onclick="openSettings()">
                        <i class="fas fa-sliders-h"></i>
                        Paramètres
                    </button>
                </div>

                <!-- Section Développement -->
                <div class="sidebar-section">
                    <div class="section-title">
                        <i class="fas fa-code"></i>
                        Développement
                    </div>
                    <button class="sidebar-btn" onclick="openCodeEditor()">
                        <i class="fas fa-edit"></i>
                        Éditeur de Code
                    </button>
                    <button class="sidebar-btn" onclick="openAnalyzer()">
                        <i class="fas fa-search"></i>
                        Analyseur
                    </button>
                </div>
            </div>
        </div>

        <!-- Zone de chat principale -->
        <div class="chat-main">
            <!-- Messages de chat -->
            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <span>Louna Principal</span>
                            <span id="currentTime"></span>
                        </div>
                        <div class="message-text">🧠 Bonjour ! Je suis Louna v2.1.0, votre assistant IA avec mémoire thermique évolutive.

🎯 **Mes capacités actuelles :**
• QI évolutif : 203 (Quasi-AGI)
• Mémoire thermique : 6 zones opérationnelles
• Accélérateurs KYBER : 8/16 actifs (+245% boost)
• Formation continue avec DeepSeek

💬 **Comment puis-je vous aider ?**
• Conversations intelligentes avec réflexions
• Génération de contenu créatif
• Analyse et développement
• Formation et apprentissage

Utilisez les boutons horizontaux pour naviguer entre les différentes fonctionnalités !</div>
                    </div>
                </div>
            </div>

            <!-- Indicateur de frappe -->
            <div class="typing-indicator" id="typingIndicator">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <span id="typingAgentName">Louna</span> réfléchit...
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <!-- Zone de saisie -->
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea
                        class="chat-input"
                        id="messageInput"
                        placeholder="💬 Posez-moi une question ou demandez-moi d'analyser quelque chose... (Entrée pour envoyer)"
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="voice-btn" id="voiceBtn" onclick="toggleVoiceInput()" title="Reconnaissance vocale">
                        <i class="fas fa-microphone"></i>
                    </button>
                </div>
                <div class="input-actions">
                    <button class="action-btn" onclick="clearChat()">
                        <i class="fas fa-trash"></i>
                        Effacer
                    </button>
                    <button class="action-btn" onclick="exportChat()">
                        <i class="fas fa-download"></i>
                        Exporter
                    </button>
                    <button class="action-btn" onclick="toggleReflections()">
                        <i class="fas fa-eye"></i>
                        Réflexions
                    </button>
                </div>
            </div>
        </div>

        <!-- Panel de réflexions (rétractable) -->
        <div class="reflections-panel" id="reflectionsPanel">
            <div class="panel-header">
                <h3>
                    <i class="fas fa-brain"></i>
                    Réflexions de Louna
                </h3>
                <button class="panel-close" onclick="toggleReflections()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content" id="reflectionsContent">
                <div class="reflection-item">
                    <div class="reflection-time">Maintenant</div>
                    <div class="reflection-text">
                        <i class="fas fa-lightbulb"></i>
                        Système initialisé. Prêt pour les interactions intelligentes avec analyse en temps réel.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panel des statistiques -->
    <div class="stats-panel" id="statsPanel">
        <h3 style="margin-bottom: 20px; color: #e91e63;">
            <i class="fas fa-chart-line"></i>
            Statistiques en Temps Réel
        </h3>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-brain"></i>
                QI Actuel
            </div>
            <div class="stats-value" id="currentQI">203</div>
            <div class="stats-label">Niveau Quasi-AGI</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-network-wired"></i>
                Neurones Actifs
            </div>
            <div class="stats-value" id="currentNeurons">89</div>
            <div class="stats-label">sur 100 neurones</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-thermometer-half"></i>
                Température
            </div>
            <div class="stats-value" id="currentTemp">37.0°C</div>
            <div class="stats-label">Optimale</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-rocket"></i>
                Évolution
            </div>
            <div class="stats-value" id="currentEvolution">100%</div>
            <div class="stats-label">Complète</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-comments"></i>
                Messages Échangés
            </div>
            <div class="stats-value" id="messageCount">0</div>
            <div class="stats-label">Cette session</div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentAgent = 'main';
        let messageCount = 0;
        let isTyping = false;
        let reflectionsOpen = false;
        let voiceRecognition = null;
        let isListening = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
            loadInitialData();
        });

        function initializeApp() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // Charger les données initiales
            loadStats();
            setInterval(loadStats, 10000);

            // Initialiser la reconnaissance vocale si disponible
            initializeVoiceRecognition();

            console.log('🧠 Chat Intelligent avec Réflexions initialisé');
        }

        function setupEventListeners() {
            const messageInput = document.getElementById('messageInput');

            // Gestion de l'input
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize du textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Gestion du focus pour l'accessibilité
            messageInput.addEventListener('focus', function() {
                this.parentElement.style.borderColor = '#ff69b4';
            });

            messageInput.addEventListener('blur', function() {
                this.parentElement.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            });
        }

        function loadInitialData() {
            // Charger les données de configuration
            fetch('/api/global/qi-detailed')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateQIDisplay(data.qi_data.qi || 203);
                        updateAgentStatus('Agent connecté - QI ' + (data.qi_data.qi || 203));
                    }
                })
                .catch(error => {
                    console.warn('Impossible de charger les données QI:', error);
                    updateQIDisplay(203);
                    updateAgentStatus('Agent connecté - Mode local');
                });

            // Vérifier les stats de persistance mémoire
            checkPersistenceStats();

            // Ajouter une entrée d'initialisation
            addToServerMemory({
                type: 'system_init',
                content: 'Chat Intelligent avec Réflexions initialisé',
                version: 'v2.1.0',
                features: ['persistance_memoire', 'reflexions_temps_reel', 'reconnaissance_vocale'],
                timestamp: new Date().toISOString()
            }, { critical: true, type: 'system' });
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString();
            }
        }

        function updateQIDisplay(qi) {
            const qiElement = document.getElementById('currentQI');
            if (qiElement) {
                qiElement.textContent = qi;
            }
        }

        function updateAgentStatus(status) {
            const statusElement = document.getElementById('agentStatus');
            if (statusElement) {
                statusElement.textContent = status;
            }
        }

        // === GESTION DES SECTIONS ===
        function setActiveSection(section) {
            // Mettre à jour les boutons de navigation
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const activeBtn = document.querySelector(`[data-section="${section}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // Actions spécifiques selon la section
            switch(section) {
                case 'chat':
                    addSystemMessage('💬 Mode Chat Intelligent activé');
                    break;
                case 'generation':
                    addSystemMessage('🎨 Mode Génération activé - Fonctionnalité en développement');
                    showGenerationInfo();
                    break;
                case 'development':
                    addSystemMessage('💻 Mode Développement activé - Fonctionnalité en développement');
                    showDevelopmentInfo();
                    break;
                case 'analysis':
                    addSystemMessage('📊 Mode Analyse activé - Fonctionnalité en développement');
                    showAnalysisInfo();
                    break;
            }
        }

        function showGenerationInfo() {
            const infoMessage = `🎨 **Mode Génération Multimédia**

🚧 **Fonctionnalités en développement :**
• Génération d'images illimitée
• Création de vidéos LTX
• Composition musicale
• Modélisation 3D

📋 **Statut actuel :** En cours d'implémentation
🎯 **Priorité :** Haute - Prochaine mise à jour`;

            addAgentMessage(infoMessage, 'main');
        }

        function showDevelopmentInfo() {
            const infoMessage = `💻 **Mode Développement Avancé**

🚧 **Fonctionnalités en développement :**
• Éditeur de code en temps réel
• ThermalScript (langage propriétaire)
• Compilation instantanée
• Débogage intégré

📋 **Statut actuel :** En cours d'implémentation
🎯 **Priorité :** Moyenne`;

            addAgentMessage(infoMessage, 'main');
        }

        function showAnalysisInfo() {
            const infoMessage = `📊 **Mode Analyse Intelligente**

🚧 **Fonctionnalités en développement :**
• Analyseur YouTube avancé
• Reconnaissance faciale
• Détection d'objets
• Analyse émotionnelle

📋 **Statut actuel :** En cours d'implémentation
🎯 **Priorité :** Moyenne`;

            addAgentMessage(infoMessage, 'main');
        }

        // === GESTION DES AGENTS ===
        function selectAgent(agentType) {
            currentAgent = agentType;

            // Mettre à jour l'interface sidebar
            document.querySelectorAll('.sidebar-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const activeBtn = document.querySelector(`[onclick="selectAgent('${agentType}')"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // Ajouter un message système
            const agentName = agentType === 'main' ? 'Louna Principal' : 'DeepSeek Formation';
            addSystemMessage(`🤖 Agent ${agentName} sélectionné`);

            // Mettre à jour le statut
            updateAgentStatus(`${agentName} connecté`);
        }

        // === GESTION DU CHAT ===
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // Afficher le message utilisateur
            addUserMessage(message);
            input.value = '';
            input.style.height = 'auto';

            // Incrémenter le compteur
            messageCount++;

            // Ajouter une réflexion
            addReflection(`Analyse du message: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);

            // Afficher l'indicateur de frappe
            showTypingIndicator();

            try {
                let response;
                if (currentAgent === 'deepseek') {
                    response = await sendToDeepSeek(message);
                } else {
                    response = await sendToCognitive(message);
                }

                hideTypingIndicator();

                if (response && response.success) {
                    addAgentMessage(response.response || response.message, currentAgent);
                    addReflection(`Réponse générée avec succès par ${currentAgent === 'main' ? 'Louna' : 'DeepSeek'}`);
                } else {
                    addErrorMessage(response?.error || 'Erreur de communication avec l\'agent');
                    addReflection('Erreur lors de la génération de la réponse');
                }
            } catch (error) {
                hideTypingIndicator();
                addErrorMessage('Erreur de connexion: ' + error.message);
                addReflection('Erreur de connexion détectée');
            }
        }

        async function sendToCognitive(message) {
            try {
                const response = await fetch('/api/cognitive/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            qi: 203,
                            agent: 'main',
                            timestamp: new Date().toISOString(),
                            conversationHistory: getConversationContext(),
                            userProfile: getUserProfile(),
                            emotionalState: analyzeEmotionalContext(message)
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Erreur API cognitive:', error);
                // Réponse de fallback améliorée avec NLP
                return generateAdvancedResponse(message);
            }
        }

        function generateAdvancedResponse(message) {
            const analysis = analyzeMessage(message);
            const emotion = detectEmotion(message);
            const intent = detectIntent(message);
            const personalizedResponse = generatePersonalizedResponse(message, analysis, emotion, intent);

            return {
                success: true,
                response: personalizedResponse
            };
        }

        function analyzeMessage(message) {
            const words = message.toLowerCase().split(' ');
            const analysis = {
                length: words.length,
                complexity: calculateComplexity(words),
                topics: extractTopics(words),
                sentiment: analyzeSentiment(words),
                questions: countQuestions(message),
                keywords: extractKeywords(words)
            };

            return analysis;
        }

        function detectEmotion(message) {
            const emotionWords = {
                joy: ['heureux', 'content', 'joie', 'super', 'génial', 'parfait', 'excellent', 'bravo'],
                sadness: ['triste', 'déprimé', 'malheureux', 'déçu', 'peine'],
                anger: ['colère', 'énervé', 'furieux', 'agacé', 'irrité'],
                fear: ['peur', 'anxieux', 'inquiet', 'stressé', 'angoissé'],
                surprise: ['surpris', 'étonné', 'incroyable', 'wow', 'impressionnant'],
                neutral: ['ok', 'bien', 'normal', 'standard']
            };

            const words = message.toLowerCase().split(' ');
            let maxScore = 0;
            let dominantEmotion = 'neutral';

            for (const [emotion, keywords] of Object.entries(emotionWords)) {
                const score = keywords.filter(keyword => words.includes(keyword)).length;
                if (score > maxScore) {
                    maxScore = score;
                    dominantEmotion = emotion;
                }
            }

            return { emotion: dominantEmotion, confidence: maxScore / words.length };
        }

        function detectIntent(message) {
            const intents = {
                question: /\?|comment|pourquoi|quoi|qui|où|quand|combien/i,
                request: /peux-tu|pouvez-vous|s'il vous plaît|merci|aide/i,
                greeting: /bonjour|salut|hello|bonsoir|coucou/i,
                goodbye: /au revoir|bye|à bientôt|tchao/i,
                compliment: /merci|bravo|excellent|parfait|génial/i,
                complaint: /problème|erreur|bug|ne marche pas|cassé/i
            };

            for (const [intent, pattern] of Object.entries(intents)) {
                if (pattern.test(message)) {
                    return intent;
                }
            }

            return 'statement';
        }

        function generatePersonalizedResponse(message, analysis, emotion, intent) {
            const userName = getUserName();
            const timeOfDay = getTimeOfDay();
            const emotionResponse = getEmotionalResponse(emotion.emotion);
            const intentResponse = getIntentResponse(intent);

            let response = `🧠 **Louna Principal** ${emotionResponse.emoji}\n\n`;

            // Réponse personnalisée selon l'émotion
            if (emotion.emotion !== 'neutral' && emotion.confidence > 0.1) {
                response += `${emotionResponse.message}\n\n`;
            }

            // Réponse selon l'intention
            response += `${intentResponse}\n\n`;

            // Analyse du message
            response += `📊 **Analyse de votre message :**\n`;
            response += `• Longueur : ${analysis.length} mots\n`;
            response += `• Complexité : ${analysis.complexity}\n`;
            response += `• Sentiment : ${analysis.sentiment}\n`;
            response += `• Émotion détectée : ${emotion.emotion} (${Math.round(emotion.confidence * 100)}%)\n`;
            response += `• Intention : ${intent}\n\n`;

            // Sujets détectés
            if (analysis.topics.length > 0) {
                response += `🎯 **Sujets identifiés :** ${analysis.topics.join(', ')}\n\n`;
            }

            // Réponse contextuelle
            response += `💭 **Ma réflexion :**\n`;
            response += generateContextualThought(message, analysis, emotion, intent);
            response += `\n\n🤝 **Comment puis-je vous aider davantage ?**`;

            return response;
        }

        function getEmotionalResponse(emotion) {
            const responses = {
                joy: {
                    emoji: '😊',
                    message: 'Je ressens votre joie ! C\'est merveilleux de vous voir si positif.'
                },
                sadness: {
                    emoji: '😔',
                    message: 'Je perçois une certaine tristesse dans votre message. Je suis là pour vous écouter.'
                },
                anger: {
                    emoji: '😤',
                    message: 'Je sens de la frustration. Prenons le temps de résoudre ce qui vous préoccupe.'
                },
                fear: {
                    emoji: '😰',
                    message: 'Je détecte de l\'inquiétude. Nous allons aborder cela ensemble, pas à pas.'
                },
                surprise: {
                    emoji: '😲',
                    message: 'Votre surprise est palpable ! C\'est intéressant.'
                },
                neutral: {
                    emoji: '🤖',
                    message: ''
                }
            };

            return responses[emotion] || responses.neutral;
        }

        function getIntentResponse(intent) {
            const responses = {
                question: 'Je vais analyser votre question et vous donner une réponse détaillée.',
                request: 'Bien sûr ! Je vais faire de mon mieux pour répondre à votre demande.',
                greeting: 'Ravi de vous retrouver ! Comment allez-vous aujourd\'hui ?',
                goodbye: 'Au plaisir de vous revoir bientôt ! Passez une excellente journée.',
                compliment: 'Merci beaucoup ! Vos encouragements m\'aident à m\'améliorer.',
                complaint: 'Je comprends votre frustration. Analysons le problème ensemble.',
                statement: 'Intéressant ! Laissez-moi réfléchir à ce que vous venez de partager.'
            };

            return responses[intent] || responses.statement;
        }

        function generateContextualThought(message, analysis, emotion, intent) {
            const thoughts = [
                `Votre message révèle une approche ${analysis.complexity} du sujet.`,
                `L'émotion ${emotion.emotion} influence probablement votre perspective.`,
                `Cette ${intent === 'question' ? 'question' : 'déclaration'} mérite une attention particulière.`,
                `Je note ${analysis.keywords.length} concepts clés dans votre message.`
            ];

            return thoughts[Math.floor(Math.random() * thoughts.length)];
        }

        function calculateComplexity(words) {
            const avgLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
            if (avgLength > 6) return 'complexe';
            if (avgLength > 4) return 'modérée';
            return 'simple';
        }

        function extractTopics(words) {
            const topics = {
                'technologie': ['code', 'programmation', 'ordinateur', 'logiciel', 'application'],
                'intelligence artificielle': ['ia', 'intelligence', 'artificielle', 'machine', 'apprentissage'],
                'mémoire': ['mémoire', 'souvenir', 'rappel', 'stockage'],
                'émotion': ['sentiment', 'émotion', 'ressenti', 'humeur'],
                'apprentissage': ['apprendre', 'formation', 'étude', 'connaissance']
            };

            const detectedTopics = [];
            for (const [topic, keywords] of Object.entries(topics)) {
                if (keywords.some(keyword => words.includes(keyword))) {
                    detectedTopics.push(topic);
                }
            }

            return detectedTopics;
        }

        function analyzeSentiment(words) {
            const positive = ['bon', 'bien', 'super', 'génial', 'parfait', 'excellent', 'merci'];
            const negative = ['mauvais', 'mal', 'problème', 'erreur', 'difficile', 'impossible'];

            const positiveScore = positive.filter(word => words.includes(word)).length;
            const negativeScore = negative.filter(word => words.includes(word)).length;

            if (positiveScore > negativeScore) return 'positif';
            if (negativeScore > positiveScore) return 'négatif';
            return 'neutre';
        }

        function countQuestions(message) {
            return (message.match(/\?/g) || []).length;
        }

        function extractKeywords(words) {
            const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or'];
            return words.filter(word => word.length > 3 && !stopWords.includes(word)).slice(0, 5);
        }

        function getConversationContext() {
            const messages = document.querySelectorAll('.message');
            const context = [];

            // Prendre les 5 derniers messages pour le contexte
            const recentMessages = Array.from(messages).slice(-5);

            recentMessages.forEach(msg => {
                const role = msg.classList.contains('user') ? 'user' : 'agent';
                const text = msg.querySelector('.message-text')?.textContent || '';
                context.push({ role, text: text.substring(0, 100) });
            });

            return context;
        }

        function getUserProfile() {
            return {
                name: 'Jean-Luc Passave',
                location: 'Sainte-Anne, Guadeloupe',
                preferences: {
                    language: 'français',
                    style: 'technique et détaillé',
                    interests: ['IA', 'programmation', 'innovation']
                },
                interactionHistory: {
                    totalMessages: messageCount,
                    averageLength: 50,
                    preferredTopics: ['technologie', 'IA']
                }
            };
        }

        function analyzeEmotionalContext(message) {
            const emotion = detectEmotion(message);
            return {
                currentEmotion: emotion.emotion,
                confidence: emotion.confidence,
                suggestedResponse: emotion.emotion === 'sadness' ? 'empathetic' :
                                 emotion.emotion === 'joy' ? 'enthusiastic' : 'balanced'
            };
        }

        function getUserName() {
            return 'Jean-Luc';
        }

        function getTimeOfDay() {
            const hour = new Date().getHours();
            if (hour < 12) return 'matin';
            if (hour < 18) return 'après-midi';
            return 'soir';
        }

        async function sendToDeepSeek(message) {
            try {
                const response = await fetch('/api/deepseek/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            qi: 203,
                            agent: 'deepseek',
                            training_mode: true
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Erreur API DeepSeek:', error);
                // Réponse de fallback
                return {
                    success: true,
                    response: `🎓 **DeepSeek Formation (Mode Local)**

Excellent ! Analysons votre demande : "${message}"

📚 **Formation en cours :**
• Analyse du contexte
• Évaluation des besoins d'apprentissage
• Génération de contenu pédagogique

🧠 **Recommandations :**
• Continuez à poser des questions variées
• Explorez différents domaines de connaissance
• Utilisez les fonctionnalités de réflexion

🎯 **Objectif :** Améliorer continuellement les capacités de Louna

Que souhaitez-vous apprendre aujourd'hui ?`
                };
            }
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span>Vous</span>
                        <span>${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="message-text">${escapeHtml(message)}</div>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Sauvegarder en persistance mémoire
            addToServerMemory({
                type: 'user_message',
                content: message,
                role: 'user',
                timestamp: new Date().toISOString()
            }, { critical: true, type: 'conversation' });
        }

        function addAgentMessage(message, agentType) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message agent';
            const messageId = Date.now(); // ID unique pour le feedback

            const agentName = agentType === 'deepseek' ? 'DeepSeek Formation' : 'Louna Principal';
            const agentIcon = agentType === 'deepseek' ? 'fas fa-graduation-cap' : 'fas fa-brain';

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="${agentIcon}"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span>${agentName}</span>
                        <span>${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="message-text">${formatMessage(message)}</div>
                    <div class="feedback-buttons" id="feedback-${messageId}">
                        <button class="feedback-btn positive" onclick="giveFeedback(${messageId}, 'positive', '${agentType}')" title="Excellente réponse">
                            <i class="fas fa-thumbs-up"></i>
                        </button>
                        <button class="feedback-btn neutral" onclick="giveFeedback(${messageId}, 'neutral', '${agentType}')" title="Réponse correcte">
                            <i class="fas fa-meh"></i>
                        </button>
                        <button class="feedback-btn negative" onclick="giveFeedback(${messageId}, 'negative', '${agentType}')" title="Réponse à améliorer">
                            <i class="fas fa-thumbs-down"></i>
                        </button>
                        <button class="feedback-btn improve" onclick="askImprovement(${messageId}, '${agentType}')" title="Suggérer une amélioration">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Sauvegarder en persistance mémoire
            addToServerMemory({
                type: 'agent_message',
                content: message,
                role: 'agent',
                agent: agentName,
                agentType: agentType,
                timestamp: new Date().toISOString()
            }, { critical: true, type: 'conversation' });
        }

        function addSystemMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 15px; margin: 10px 0; font-size: 12px; color: #b0b0b0;">
                    <i class="fas fa-info-circle"></i> ${message}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addErrorMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message error';
            messageDiv.innerHTML = `
                <div style="text-align: center; padding: 10px; background: rgba(244,67,54,0.2); border-radius: 15px; margin: 10px 0; font-size: 12px; color: #f44336; border-left: 4px solid #f44336;">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            isTyping = true;
            const indicator = document.getElementById('typingIndicator');
            const agentName = currentAgent === 'deepseek' ? 'DeepSeek Formation' : 'Louna Principal';
            document.getElementById('typingAgentName').textContent = agentName;
            indicator.style.display = 'flex';
            document.getElementById('sendBtn').disabled = true;
        }

        function hideTypingIndicator() {
            isTyping = false;
            document.getElementById('typingIndicator').style.display = 'none';
            document.getElementById('sendBtn').disabled = false;
        }

        // === GESTION DES RÉFLEXIONS ===
        function toggleReflections() {
            const panel = document.getElementById('reflectionsPanel');
            reflectionsOpen = !reflectionsOpen;

            if (reflectionsOpen) {
                panel.classList.add('open');
                addReflection('Panel de réflexions ouvert');
            } else {
                panel.classList.remove('open');
            }
        }

        function addReflection(text) {
            const content = document.getElementById('reflectionsContent');
            const reflectionDiv = document.createElement('div');
            reflectionDiv.className = 'reflection-item';
            reflectionDiv.innerHTML = `
                <div class="reflection-time">${new Date().toLocaleTimeString()}</div>
                <div class="reflection-text">
                    <i class="fas fa-lightbulb"></i>
                    ${text}
                </div>
            `;
            content.appendChild(reflectionDiv);
            content.scrollTop = content.scrollHeight;

            // Limiter le nombre de réflexions
            const reflections = content.querySelectorAll('.reflection-item');
            if (reflections.length > 20) {
                reflections[0].remove();
            }
        }

        // === GESTION DE LA SIDEBAR ===
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }

        // === FONCTIONS DE NAVIGATION ===
        function setActiveSection(section) {
            // Mettre à jour les boutons actifs
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Activer le bouton cliqué
            const activeBtn = document.querySelector(`[data-section="${section}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // Rediriger vers la section appropriée
            switch(section) {
                case 'chat':
                    addSystemMessage('💬 Section Chat Intelligent activée');
                    addReflection('Navigation vers Chat Intelligent');
                    break;
                case 'generation':
                    addSystemMessage('🎨 Redirection vers Centre de Génération...');
                    window.location.href = '/generation-center.html';
                    break;
                case 'development':
                    addSystemMessage('💻 Redirection vers Développement...');
                    window.location.href = '/code-editor.html';
                    break;
                case 'analysis':
                    addSystemMessage('📊 Redirection vers Analyse...');
                    window.location.href = '/advanced-features-manager.html';
                    break;
                case 'search':
                    addSystemMessage('🔍 Redirection vers Recherche Web...');
                    window.location.href = '/web-search.html';
                    break;
                case 'recognition':
                    addSystemMessage('👁️ Redirection vers Reconnaissance Faciale...');
                    window.location.href = '/face-recognition.html';
                    break;
                case 'learning':
                    addSystemMessage('🧠 Redirection vers Dashboard Apprentissage...');
                    window.location.href = '/learning-dashboard.html';
                    break;
                default:
                    addSystemMessage(`🔄 Navigation vers ${section}...`);
            }
        }

        function openMemoryThermal() {
            addSystemMessage('🔥 Ouverture de la Mémoire Thermique...');
            window.open('/brain-dashboard-live.html', '_blank');
        }

        function openMonitoring() {
            addSystemMessage('📊 Ouverture du Monitoring...');
            window.open('/brain-monitoring-complete.html', '_blank');
        }

        function openSettings() {
            addSystemMessage('⚙️ Ouverture des Paramètres...');
            window.open('/settings-advanced.html', '_blank');
        }

        function openCodeEditor() {
            addSystemMessage('💻 Ouverture de l\'Éditeur de Code...');
            window.open('/code-editor.html', '_blank');
        }

        function openAnalyzer() {
            addSystemMessage('🔍 Ouverture de l\'Analyseur...');
            window.open('/advanced-features-manager.html', '_blank');
        }

        // === FONCTIONS D'ACTION ===
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="message agent">
                    <div class="message-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <span>Louna Principal</span>
                            <span>${new Date().toLocaleTimeString()}</span>
                        </div>
                        <div class="message-text">💬 Chat effacé ! Je suis prêt pour une nouvelle conversation.</div>
                    </div>
                </div>
            `;
            messageCount = 0;
            addReflection('Chat effacé - Nouvelle session démarrée');
        }

        function exportChat() {
            const messages = document.querySelectorAll('.message');
            let exportText = `# Chat Louna - Export du ${new Date().toLocaleString()}\n\n`;

            messages.forEach(message => {
                const header = message.querySelector('.message-header span');
                const text = message.querySelector('.message-text');
                if (header && text) {
                    exportText += `**${header.textContent}**: ${text.textContent}\n\n`;
                }
            });

            const blob = new Blob([exportText], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat-louna-${new Date().toISOString().split('T')[0]}.md`;
            a.click();
            URL.revokeObjectURL(url);

            addSystemMessage('💾 Chat exporté avec succès');
            addReflection('Export du chat effectué');
        }

        // === RECONNAISSANCE VOCALE ===
        function initializeVoiceRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                voiceRecognition = new SpeechRecognition();
                voiceRecognition.continuous = false;
                voiceRecognition.interimResults = false;
                voiceRecognition.lang = 'fr-FR';

                voiceRecognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    addReflection(`Reconnaissance vocale: "${transcript}"`);
                };

                voiceRecognition.onerror = function(event) {
                    console.error('Erreur reconnaissance vocale:', event.error);
                    addReflection('Erreur reconnaissance vocale');
                };

                voiceRecognition.onend = function() {
                    isListening = false;
                    updateVoiceButton();
                };
            }
        }

        function toggleVoiceInput() {
            if (!voiceRecognition) {
                addErrorMessage('Reconnaissance vocale non disponible');
                return;
            }

            if (isListening) {
                voiceRecognition.stop();
                isListening = false;
            } else {
                voiceRecognition.start();
                isListening = true;
                addReflection('Écoute vocale activée');
            }

            updateVoiceButton();
        }

        function updateVoiceButton() {
            const voiceBtn = document.getElementById('voiceBtn');
            if (voiceBtn) {
                if (isListening) {
                    voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    voiceBtn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                } else {
                    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                    voiceBtn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                }
            }
        }

        // === INTÉGRATION PERSISTANCE MÉMOIRE ===
        function addToServerMemory(data, options = {}) {
            // Envoyer les données au système de persistance serveur
            fetch('/api/memory/add-instant', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        ...data,
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe',
                        interface: 'chat_intelligent',
                        qi: 203
                    },
                    options: {
                        critical: options.critical || false,
                        source: 'chat_interface',
                        type: options.type || 'conversation'
                    }
                })
            }).then(response => response.json())
              .then(result => {
                  if (result.success) {
                      console.log('💾 Données sauvegardées en persistance:', result.id);
                  }
              })
              .catch(error => {
                  console.warn('⚠️ Erreur sauvegarde persistance:', error);
              });
        }

        async function checkPersistenceStats() {
            try {
                const response = await fetch('/api/memory/persistence-stats');
                const data = await response.json();

                if (data.success) {
                    console.log('📊 Stats persistance mémoire:', data.stats);
                    addReflection(`Persistance: ${data.stats.instantMemorySize} éléments, ${data.stats.saveCount} sauvegardes`);
                }
            } catch (error) {
                console.warn('Erreur récupération stats persistance:', error);
            }
        }

        // === FONCTIONS UTILITAIRES ===
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatMessage(message) {
            // Formatage basique pour markdown
            return message
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>');
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/global/qi-detailed');
                const data = await response.json();

                if (data.success) {
                    updateQIDisplay(data.qi_data.qi || 203);
                    // Mettre à jour d'autres stats si nécessaire
                }
            } catch (error) {
                console.warn('Erreur chargement stats:', error);
            }
        }

        // === GESTION DES ERREURS GLOBALES ===
        window.addEventListener('error', function(event) {
            console.error('Erreur JavaScript:', event.error);
            addReflection(`Erreur détectée: ${event.error.message}`);
        });

        // === SYSTÈME D'APPRENTISSAGE PAR RENFORCEMENT ===
        let feedbackData = {
            positive: 0,
            neutral: 0,
            negative: 0,
            improvements: []
        };

        function giveFeedback(messageId, feedbackType, agentType) {
            // Marquer le bouton comme sélectionné
            const feedbackContainer = document.getElementById(`feedback-${messageId}`);
            if (feedbackContainer) {
                // Retirer la sélection précédente
                feedbackContainer.querySelectorAll('.feedback-btn').forEach(btn => {
                    btn.classList.remove('selected');
                });

                // Ajouter la sélection au bouton cliqué
                const selectedBtn = feedbackContainer.querySelector(`.feedback-btn.${feedbackType}`);
                if (selectedBtn) {
                    selectedBtn.classList.add('selected');
                }
            }

            // Enregistrer le feedback
            feedbackData[feedbackType]++;

            // Ajouter à la mémoire thermique
            addToServerMemory({
                type: 'reinforcement_learning',
                subtype: 'feedback',
                messageId: messageId,
                feedbackType: feedbackType,
                agentType: agentType,
                timestamp: new Date().toISOString()
            }, { critical: true, type: 'learning' });

            // Ajouter une réflexion
            const feedbackMessages = {
                positive: 'Feedback positif reçu - Renforcement des patterns de réponse',
                neutral: 'Feedback neutre reçu - Maintien des patterns actuels',
                negative: 'Feedback négatif reçu - Ajustement des patterns de réponse'
            };

            addReflection(feedbackMessages[feedbackType]);

            // Mettre à jour le QI si feedback positif
            if (feedbackType === 'positive') {
                updateQIWithLearning();
            }

            // Afficher une notification
            showFeedbackNotification(feedbackType);

            console.log('🧠 Feedback enregistré:', { messageId, feedbackType, agentType });
        }

        function askImprovement(messageId, agentType) {
            const improvement = prompt('Comment puis-je améliorer ma réponse ?\n\nVos suggestions m\'aident à apprendre :');

            if (improvement && improvement.trim()) {
                // Marquer le bouton comme sélectionné
                const feedbackContainer = document.getElementById(`feedback-${messageId}`);
                if (feedbackContainer) {
                    feedbackContainer.querySelectorAll('.feedback-btn').forEach(btn => {
                        btn.classList.remove('selected');
                    });

                    const improveBtn = feedbackContainer.querySelector('.feedback-btn.improve');
                    if (improveBtn) {
                        improveBtn.classList.add('selected');
                    }
                }

                // Enregistrer l'amélioration
                feedbackData.improvements.push({
                    messageId: messageId,
                    suggestion: improvement,
                    agentType: agentType,
                    timestamp: new Date().toISOString()
                });

                // Ajouter à la mémoire thermique
                addToServerMemory({
                    type: 'reinforcement_learning',
                    subtype: 'improvement_suggestion',
                    messageId: messageId,
                    suggestion: improvement,
                    agentType: agentType,
                    timestamp: new Date().toISOString()
                }, { critical: true, type: 'learning' });

                // Ajouter une réflexion
                addReflection(`Suggestion d'amélioration reçue: "${improvement.substring(0, 50)}..."`);

                // Réponse de l'agent
                setTimeout(() => {
                    const thankYouMessage = `🙏 **Merci pour votre suggestion !**

📝 **Votre feedback :** "${improvement}"

🧠 **Apprentissage en cours :**
• Analyse de votre suggestion
• Intégration dans mes patterns de réponse
• Amélioration continue de mes capacités

💡 **Impact :** Cette suggestion m'aide à mieux comprendre vos attentes et à améliorer mes futures réponses.

Continuez à me donner des feedbacks, c'est ainsi que j'apprends et évolue !`;

                    addAgentMessage(thankYouMessage, agentType);
                }, 1000);

                showFeedbackNotification('improve');
            }
        }

        function updateQIWithLearning() {
            // Simuler une légère augmentation du QI avec l'apprentissage
            const currentQI = parseInt(document.getElementById('currentQI').textContent);
            const newQI = Math.min(currentQI + 0.1, 250); // Plafonner à 250

            // Mettre à jour l'affichage avec animation
            animateQIUpdate(currentQI, newQI);

            // Sauvegarder le nouveau QI
            addToServerMemory({
                type: 'qi_evolution',
                oldQI: currentQI,
                newQI: newQI,
                reason: 'positive_feedback_learning',
                timestamp: new Date().toISOString()
            }, { critical: true, type: 'evolution' });
        }

        function animateQIUpdate(oldQI, newQI) {
            const qiElement = document.getElementById('currentQI');
            let current = oldQI;
            const increment = (newQI - oldQI) / 20; // 20 étapes d'animation

            const animation = setInterval(() => {
                current += increment;
                qiElement.textContent = Math.round(current * 10) / 10; // 1 décimale

                if (current >= newQI) {
                    qiElement.textContent = Math.round(newQI * 10) / 10;
                    clearInterval(animation);

                    // Effet visuel
                    qiElement.style.color = '#4caf50';
                    qiElement.style.transform = 'scale(1.1)';

                    setTimeout(() => {
                        qiElement.style.color = '#ff69b4';
                        qiElement.style.transform = 'scale(1)';
                    }, 1000);
                }
            }, 50);
        }

        function showFeedbackNotification(type) {
            const messages = {
                positive: '👍 Merci ! J\'apprends de votre feedback positif',
                neutral: '😐 Feedback neutre enregistré',
                negative: '👎 Je vais m\'améliorer grâce à votre feedback',
                improve: '💡 Suggestion enregistrée - Merci pour votre aide !'
            };

            const colors = {
                positive: '#4caf50',
                neutral: '#ff9800',
                negative: '#f44336',
                improve: '#2196f3'
            };

            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = messages[type];

            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Suppression automatique
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function getFeedbackStats() {
            return {
                ...feedbackData,
                totalFeedbacks: feedbackData.positive + feedbackData.neutral + feedbackData.negative,
                positiveRatio: feedbackData.positive / (feedbackData.positive + feedbackData.neutral + feedbackData.negative) || 0
            };
        }

        // === RACCOURCIS CLAVIER ===
        document.addEventListener('keydown', function(event) {
            // Ctrl/Cmd + K pour effacer le chat
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                clearChat();
            }

            // Ctrl/Cmd + E pour exporter
            if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
                event.preventDefault();
                exportChat();
            }

            // Ctrl/Cmd + R pour les réflexions
            if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
                event.preventDefault();
                toggleReflections();
            }
        });
    </script>
</body>
</html>
