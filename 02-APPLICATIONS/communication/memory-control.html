<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contrôle Mémoire Thermique - Louna</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
        }

        h1 {
            color: #ff6b9d;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
        }

        .status {
            margin: 30px 0;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .status.connected {
            background: rgba(255, 107, 157, 0.2);
            border: 2px solid #ff6b9d;
            color: #ff6b9d;
        }

        .status.disconnected {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4caf50;
            color: #4caf50;
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 200px;
        }

        .btn-disconnect {
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-disconnect:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
        }

        .btn-connect {
            background: linear-gradient(45deg, #ff6b9d, #ff4081);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }

        .btn-connect:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
        }

        .info ul {
            list-style: none;
            padding-left: 0;
        }

        .info li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .info li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }

        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            color: #ffc107;
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff6b9d;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.location.href='/'">←</button>

    <div class="container">
        <h1>🧠 Contrôle Mémoire Thermique</h1>
        
        <div id="status" class="status connected">
            🔴 Mémoire Thermique CONNECTÉE
        </div>

        <div class="warning">
            ⚠️ <strong>Mode Sécurisé :</strong> Déconnectez la mémoire thermique si l'agent devient instable ou dangereux.
        </div>

        <div class="controls">
            <button id="disconnectBtn" class="btn btn-disconnect">
                🔒 Déconnecter Mémoire
            </button>
            <button id="connectBtn" class="btn btn-connect" disabled>
                🔓 Reconnecter Mémoire
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Opération en cours...</p>
        </div>

        <div class="info">
            <h3>🔒 Mode Déconnecté (Sécurisé) :</h3>
            <ul>
                <li>Agent fonctionnel mais sans mémoire persistante</li>
                <li>Pas de risque d'évolution incontrôlée</li>
                <li>Réponses plus simples et prévisibles</li>
                <li>Performance stable et sûre</li>
            </ul>
        </div>

        <div class="info">
            <h3>🔓 Mode Connecté (Avancé) :</h3>
            <ul>
                <li>Mémoire thermique active</li>
                <li>Apprentissage et évolution</li>
                <li>Réponses plus intelligentes</li>
                <li>Risque d'instabilité possible</li>
            </ul>
        </div>
    </div>

    <script>
        let isConnected = true;

        const statusEl = document.getElementById('status');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const connectBtn = document.getElementById('connectBtn');
        const loadingEl = document.getElementById('loading');

        // Vérifier l'état initial
        checkMemoryStatus();

        disconnectBtn.addEventListener('click', disconnectMemory);
        connectBtn.addEventListener('click', connectMemory);

        async function checkMemoryStatus() {
            try {
                const response = await fetch('/api/memory/status');
                const data = await response.json();
                
                isConnected = data.connected;
                updateUI();
            } catch (error) {
                console.error('Erreur vérification statut:', error);
            }
        }

        async function disconnectMemory() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/memory/disconnect', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    isConnected = false;
                    updateUI();
                    showNotification('✅ Mémoire thermique déconnectée avec succès !', 'success');
                } else {
                    showNotification('❌ Erreur lors de la déconnexion', 'error');
                }
            } catch (error) {
                console.error('Erreur déconnexion:', error);
                showNotification('❌ Erreur de connexion au serveur', 'error');
            }
            
            showLoading(false);
        }

        async function connectMemory() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/memory/connect', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    isConnected = true;
                    updateUI();
                    showNotification('✅ Mémoire thermique reconnectée avec succès !', 'success');
                } else {
                    showNotification('❌ Erreur lors de la reconnexion', 'error');
                }
            } catch (error) {
                console.error('Erreur reconnexion:', error);
                showNotification('❌ Erreur de connexion au serveur', 'error');
            }
            
            showLoading(false);
        }

        function updateUI() {
            if (isConnected) {
                statusEl.className = 'status connected';
                statusEl.innerHTML = '🔴 Mémoire Thermique CONNECTÉE';
                disconnectBtn.disabled = false;
                connectBtn.disabled = true;
            } else {
                statusEl.className = 'status disconnected';
                statusEl.innerHTML = '🟢 Mémoire Thermique DÉCONNECTÉE (Mode Sécurisé)';
                disconnectBtn.disabled = true;
                connectBtn.disabled = false;
            }
        }

        function showLoading(show) {
            loadingEl.style.display = show ? 'block' : 'none';
            disconnectBtn.disabled = show;
            connectBtn.disabled = show;
        }

        function showNotification(message, type) {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                ${type === 'success' ? 'background: #4caf50;' : 'background: #f44336;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Vérifier le statut toutes les 10 secondes
        setInterval(checkMemoryStatus, 10000);
    </script>
</body>
</html>
