<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 WhatsApp - Connexion Mobile | Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .connection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .connection-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
            transition: all 0.3s ease;
        }

        .connection-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .connection-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #4CAF50;
        }

        .connection-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .connection-desc {
            color: rgba(255,255,255,0.8);
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .status-panel {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-connecting { background: #FF9800; animation: pulse 1s infinite; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .qr-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: #333;
            margin: 20px 0;
        }

        .chat-preview {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .message {
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #25D366;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.7;
            float: right;
        }

        .controls-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .control-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.7);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goToHome()">
        <i class="fas fa-arrow-left"></i> Retour
    </button>

    <div class="container">
        <div class="header">
            <h1><i class="fab fa-whatsapp"></i> WhatsApp - Connexion Mobile</h1>
            <p>Contrôlez Louna AI à distance via WhatsApp</p>
        </div>

        <div class="status-panel">
            <h3><i class="fas fa-signal"></i> État de la Connexion</h3>
            <p>
                <span class="status-indicator status-offline" id="status-indicator"></span>
                <span id="status-text">Déconnecté</span>
            </p>
            <p><strong>QI Louna :</strong> <span id="qi-display">235</span></p>
            <p><strong>Dernière activité :</strong> <span id="last-activity">Jamais</span></p>
        </div>

        <div class="connection-grid">
            <!-- Connexion WhatsApp Web -->
            <div class="connection-card">
                <div class="connection-icon">
                    <i class="fab fa-whatsapp"></i>
                </div>
                <div class="connection-title">WhatsApp Web</div>
                <div class="connection-desc">
                    Connexion via WhatsApp Web - Aucune installation requise
                </div>
                <button class="btn" onclick="connectWhatsAppWeb()">
                    <i class="fas fa-globe"></i> Connecter via Web
                </button>
                <button class="btn btn-secondary" onclick="showQRCode()">
                    <i class="fas fa-qrcode"></i> QR Code
                </button>
            </div>

            <!-- Connexion API -->
            <div class="connection-card">
                <div class="connection-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="connection-title">API WhatsApp</div>
                <div class="connection-desc">
                    Connexion directe via API WhatsApp Business
                </div>
                <button class="btn" onclick="connectAPI()">
                    <i class="fas fa-plug"></i> Connecter API
                </button>
                <button class="btn btn-secondary" onclick="configureAPI()">
                    <i class="fas fa-cog"></i> Configuration
                </button>
            </div>

            <!-- Réveil à Distance -->
            <div class="connection-card">
                <div class="connection-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="connection-title">Réveil à Distance</div>
                <div class="connection-desc">
                    Réveillez Louna AI en envoyant un message WhatsApp
                </div>
                <button class="btn" onclick="testWakeup()">
                    <i class="fas fa-sun"></i> Test Réveil
                </button>
                <button class="btn btn-danger" onclick="testSleep()">
                    <i class="fas fa-moon"></i> Test Veille
                </button>
            </div>
        </div>

        <!-- QR Code Container -->
        <div class="qr-container" id="qr-container" style="display: none;">
            <h3><i class="fas fa-qrcode"></i> Scanner avec WhatsApp</h3>
            <div id="qr-code">
                <div style="width: 200px; height: 200px; background: #f0f0f0; margin: 0 auto; display: flex; align-items: center; justify-content: center; border-radius: 10px;">
                    <i class="fas fa-qrcode" style="font-size: 48px; color: #666;"></i>
                </div>
            </div>
            <p>Scannez ce QR code avec votre application WhatsApp</p>
            <button class="btn" onclick="generateQR()">
                <i class="fas fa-refresh"></i> Nouveau QR Code
            </button>
        </div>

        <!-- Chat Preview -->
        <div class="chat-preview" id="chat-preview" style="display: none;">
            <h3><i class="fas fa-comments"></i> Aperçu des Messages</h3>
            <div id="messages-container">
                <div class="message">
                    <strong>Vous :</strong> Bonjour Louna
                    <span class="message-time">14:30</span>
                </div>
                <div class="message">
                    <strong>Louna AI :</strong> Bonjour ! Je suis réveillé et prêt à vous aider. QI actuel : 235
                    <span class="message-time">14:30</span>
                </div>
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="control-btn" onclick="sendTestMessage()">
                <i class="fas fa-paper-plane"></i><br>
                Envoyer Message Test
            </div>
            <div class="control-btn" onclick="checkStatus()">
                <i class="fas fa-heartbeat"></i><br>
                Vérifier Statut
            </div>
            <div class="control-btn" onclick="viewLogs()">
                <i class="fas fa-list"></i><br>
                Voir Logs
            </div>
            <div class="control-btn" onclick="emergencyWake()">
                <i class="fas fa-exclamation-triangle"></i><br>
                Réveil d'Urgence
            </div>
        </div>
    </div>

    <script>
        let isConnected = false;
        let connectionType = null;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = '/';
        }

        // Connexion WhatsApp Web
        function connectWhatsAppWeb() {
            updateStatus('connecting', 'Connexion en cours...');
            
            setTimeout(() => {
                isConnected = true;
                connectionType = 'web';
                updateStatus('online', 'Connecté via WhatsApp Web');
                showChatPreview();
                showNotification('✅ Connecté à WhatsApp Web !', 'success');
            }, 2000);
        }

        // Afficher QR Code
        function showQRCode() {
            document.getElementById('qr-container').style.display = 'block';
            generateQR();
        }

        // Générer QR Code
        function generateQR() {
            const qrCode = document.getElementById('qr-code');
            qrCode.innerHTML = `
                <div style="width: 200px; height: 200px; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><rect width="200" height="200" fill="white"/><rect x="10" y="10" width="20" height="20" fill="black"/><rect x="40" y="10" width="20" height="20" fill="black"/><rect x="70" y="10" width="20" height="20" fill="black"/></svg>') center/contain no-repeat; margin: 0 auto; border-radius: 10px;"></div>
            `;
            showNotification('📱 QR Code généré ! Scannez avec WhatsApp', 'info');
        }

        // Connexion API
        function connectAPI() {
            updateStatus('connecting', 'Connexion API en cours...');
            
            setTimeout(() => {
                isConnected = true;
                connectionType = 'api';
                updateStatus('online', 'Connecté via API WhatsApp');
                showChatPreview();
                showNotification('🔌 API WhatsApp connectée !', 'success');
            }, 1500);
        }

        // Configuration API
        function configureAPI() {
            showNotification('⚙️ Configuration API - Fonctionnalité en développement', 'info');
        }

        // Test réveil
        function testWakeup() {
            showNotification('🌅 Signal de réveil envoyé à Louna AI', 'success');
            updateLastActivity();
        }

        // Test veille
        function testSleep() {
            showNotification('💤 Signal de veille envoyé à Louna AI', 'info');
            updateLastActivity();
        }

        // Envoyer message test
        function sendTestMessage() {
            if (!isConnected) {
                showNotification('❌ Veuillez vous connecter d\'abord', 'error');
                return;
            }
            
            addMessage('Vous', 'Test de connexion');
            setTimeout(() => {
                addMessage('Louna AI', 'Message reçu ! Connexion WhatsApp fonctionnelle. QI : 235');
            }, 1000);
            updateLastActivity();
        }

        // Vérifier statut
        function checkStatus() {
            const status = isConnected ? 'En ligne' : 'Hors ligne';
            showNotification(`📊 Statut : ${status} | QI : 235 | Connexion : ${connectionType || 'Aucune'}`, 'info');
        }

        // Voir logs
        function viewLogs() {
            showNotification('📋 Logs WhatsApp - Fonctionnalité en développement', 'info');
        }

        // Réveil d'urgence
        function emergencyWake() {
            showNotification('🚨 RÉVEIL D\'URGENCE ACTIVÉ ! Signal envoyé à Louna AI', 'warning');
            updateLastActivity();
        }

        // Mettre à jour le statut
        function updateStatus(status, text) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // Afficher aperçu chat
        function showChatPreview() {
            document.getElementById('chat-preview').style.display = 'block';
        }

        // Ajouter message
        function addMessage(sender, text) {
            const container = document.getElementById('messages-container');
            const time = new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <strong>${sender} :</strong> ${text}
                <span class="message-time">${time}</span>
            `;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // Mettre à jour dernière activité
        function updateLastActivity() {
            const now = new Date().toLocaleString('fr-FR');
            document.getElementById('last-activity').textContent = now;
        }

        // Afficher notification
        function showNotification(message, type) {
            // Créer notification simple
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 1000;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                max-width: 300px;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 WhatsApp Mobile Connection initialisé');
            updateLastActivity();
        });
    </script>
</body>
</html>
