<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Transfert d'Informations - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .transfer-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto 1fr;
            gap: 20px;
            padding: 20px;
            height: calc(100vh - 120px);
        }

        .metrics-panel {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .metric-unit {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .transfer-visualization {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .network-diagram {
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .network-layer {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }

        .network-node {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .network-node:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.3);
        }

        .network-node.active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #ff69b4, #4ecdc4);
            opacity: 0.6;
            animation: dataFlow 2s infinite;
        }

        @keyframes dataFlow {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }

        .data-flow {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: flowAnimation 3s infinite linear;
        }

        @keyframes flowAnimation {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(300px); opacity: 0; }
        }

        .thermal-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .thermal-zones {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .thermal-zone {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .zone-temp {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .zone-name {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .temp-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }

        .temp-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ecdc4, #ff69b4);
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .control-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .stats-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            min-width: 200px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 0.9rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
        }

        .stat-value {
            color: #4ecdc4;
            font-weight: 600;
        }

        .loading-indicator {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.6);
        }

        .spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff69b4;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .transfer-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                padding: 10px;
                height: auto;
            }

            .metrics-panel {
                grid-template-columns: repeat(2, 1fr);
            }

            .thermal-zones {
                grid-template-columns: 1fr;
            }

            .control-panel {
                position: relative;
                bottom: auto;
                right: auto;
                justify-content: center;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-exchange-alt louna-header-icon"></i>
                <h1>Transfert d'Informations</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/brain-monitoring-complete.html" class="louna-nav-btn">
                    <i class="fas fa-brain"></i>
                    <span>Monitoring</span>
                </a>
                <a href="/futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Transfert actif</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="transfer-container" id="transfer-container">
        <div class="loading-indicator">
            <div class="spinner"></div>
            <div>Chargement des données de transfert...</div>
        </div>
    </div>

    <!-- Panneau de contrôle -->
    <div class="control-panel">
        <button class="control-btn" onclick="toggleMonitoring()" title="Pause/Reprendre">
            <i class="fas fa-pause" id="monitor-icon"></i>
        </button>
        <button class="control-btn secondary" onclick="resetData()" title="Réinitialiser">
            <i class="fas fa-redo"></i>
        </button>
        <button class="control-btn" onclick="exportData()" title="Exporter">
            <i class="fas fa-download"></i>
        </button>
    </div>

    <!-- Scripts -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>

    <script>
        let transferData = null;
        let updateInterval = null;
        let isMonitoring = true;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadTransferData();
            startAutoUpdate();

            showSuccess('🔄 Monitoring de transfert initialisé !');
        });

        // Charger les données de transfert
        async function loadTransferData() {
            try {
                const response = await fetch('/api/brain/information-transfer');
                const data = await response.json();

                if (data.success) {
                    transferData = data;
                    renderTransferInterface();
                } else {
                    throw new Error(data.error || 'Erreur lors du chargement');
                }
            } catch (error) {
                console.error('❌ Erreur chargement données:', error);
                showError('Erreur de chargement des données de transfert');

                // Données simulées en cas d'erreur
                transferData = createSimulatedTransferData();
                renderTransferInterface();
            }
        }

        // Créer des données simulées
        function createSimulatedTransferData() {
            return {
                transfer: {
                    inputRate: 850,
                    outputRate: 650,
                    processingSpeed: 0.85,
                    bandwidthUtilization: 0.65,
                    latency: 45,
                    throughput: 1200
                },
                thermal: {
                    globalTemperature: 0.52,
                    zoneTemperatures: {
                        sensory: 0.3,
                        working: 0.7,
                        longTerm: 0.8,
                        emotional: 0.4,
                        procedural: 0.3,
                        creative: 0.6
                    }
                },
                networks: [
                    { name: 'sensory', count: 15, activity: 0.8, efficiency: 0.85, temperature: 0.3 },
                    { name: 'working', count: 12, activity: 0.7, efficiency: 0.82, temperature: 0.7 },
                    { name: 'longTerm', count: 20, activity: 0.6, efficiency: 0.88, temperature: 0.8 },
                    { name: 'emotional', count: 10, activity: 0.5, efficiency: 0.75, temperature: 0.4 },
                    { name: 'executive', count: 8, activity: 0.9, efficiency: 0.90, temperature: 0.3 },
                    { name: 'creative', count: 6, activity: 0.4, efficiency: 0.70, temperature: 0.6 }
                ]
            };
        }

        // Rendre l'interface de transfert
        function renderTransferInterface() {
            const container = document.getElementById('transfer-container');

            container.innerHTML = `
                <!-- Panneau de métriques -->
                <div class="metrics-panel">
                    ${createMetricsCards()}
                </div>

                <!-- Visualisation du transfert -->
                <div class="transfer-visualization">
                    <h3 style="margin-top: 0; color: #ff69b4;">
                        <i class="fas fa-project-diagram"></i>
                        Flux de Données Neuronales
                    </h3>
                    ${createNetworkDiagram()}
                    ${createStatsOverlay()}
                </div>

                <!-- Panneau thermique -->
                <div class="thermal-panel">
                    <h3 style="margin-top: 0; color: #4ecdc4;">
                        <i class="fas fa-thermometer-half"></i>
                        État Thermique des Zones
                    </h3>
                    ${createThermalZones()}
                </div>
            `;

            // Démarrer les animations
            startAnimations();
        }

        // Créer les cartes de métriques
        function createMetricsCards() {
            const transfer = transferData.transfer;

            return `
                <div class="metric-card">
                    <div class="metric-value">${transfer.inputRate}</div>
                    <div class="metric-label">Taux d'Entrée</div>
                    <div class="metric-unit">ops/sec</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${transfer.outputRate}</div>
                    <div class="metric-label">Taux de Sortie</div>
                    <div class="metric-unit">ops/sec</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${(transfer.processingSpeed * 100).toFixed(0)}%</div>
                    <div class="metric-label">Vitesse de Traitement</div>
                    <div class="metric-unit">efficacité</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${transfer.latency}</div>
                    <div class="metric-label">Latence</div>
                    <div class="metric-unit">ms</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${(transfer.bandwidthUtilization * 100).toFixed(0)}%</div>
                    <div class="metric-label">Utilisation Bande Passante</div>
                    <div class="metric-unit">capacité</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${transfer.throughput}</div>
                    <div class="metric-label">Débit Global</div>
                    <div class="metric-unit">ops/sec</div>
                </div>
            `;
        }

        // Créer le diagramme de réseau
        function createNetworkDiagram() {
            const networks = transferData.networks;

            return `
                <div class="network-diagram">
                    <div class="network-layer">
                        ${networks.slice(0, 3).map((network, index) => `
                            <div class="network-node ${network.activity > 0.7 ? 'active' : ''}"
                                 onclick="showNetworkDetails('${network.name}')"
                                 title="${network.name}: ${network.count} neurones">
                                ${network.count}
                            </div>
                        `).join('')}
                    </div>

                    <div class="network-layer">
                        ${networks.slice(3, 6).map((network, index) => `
                            <div class="network-node ${network.activity > 0.7 ? 'active' : ''}"
                                 onclick="showNetworkDetails('${network.name}')"
                                 title="${network.name}: ${network.count} neurones">
                                ${network.count}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Créer l'overlay de statistiques
        function createStatsOverlay() {
            const transfer = transferData.transfer;
            const totalNodes = transferData.networks.reduce((sum, net) => sum + net.count, 0);
            const avgEfficiency = transferData.networks.reduce((sum, net) => sum + net.efficiency, 0) / transferData.networks.length;

            return `
                <div class="stats-overlay">
                    <div class="stat-item">
                        <span class="stat-label">Nœuds Totaux:</span>
                        <span class="stat-value">${totalNodes}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Efficacité Moy:</span>
                        <span class="stat-value">${(avgEfficiency * 100).toFixed(1)}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Flux Actifs:</span>
                        <span class="stat-value">${transferData.networks.filter(n => n.activity > 0.5).length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Temp. Globale:</span>
                        <span class="stat-value">${transferData.thermal.globalTemperature.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        // Créer les zones thermiques
        function createThermalZones() {
            const zones = transferData.thermal.zoneTemperatures;

            return `
                <div class="thermal-zones">
                    ${Object.entries(zones).map(([name, temp]) => `
                        <div class="thermal-zone">
                            <div class="zone-temp">${temp.toFixed(2)}</div>
                            <div class="zone-name">${name}</div>
                            <div class="temp-bar">
                                <div class="temp-fill" style="width: ${temp * 100}%"></div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Démarrer les animations
        function startAnimations() {
            // Ajouter des particules de flux de données
            const diagram = document.querySelector('.network-diagram');
            if (diagram) {
                setInterval(() => {
                    if (isMonitoring) {
                        createDataFlowParticle();
                    }
                }, 1000);
            }
        }

        // Créer une particule de flux de données
        function createDataFlowParticle() {
            const diagram = document.querySelector('.network-diagram');
            if (!diagram) return;

            const particle = document.createElement('div');
            particle.className = 'data-flow';
            particle.style.left = '10%';
            particle.style.top = Math.random() * 80 + 10 + '%';

            diagram.appendChild(particle);

            // Supprimer la particule après l'animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }

        // Afficher les détails d'un réseau
        function showNetworkDetails(networkName) {
            const network = transferData.networks.find(n => n.name === networkName);
            if (network) {
                showInfo(`🧠 Réseau ${networkName}: ${network.count} neurones, ${(network.efficiency * 100).toFixed(1)}% efficacité, ${(network.activity * 100).toFixed(1)}% activité`);
            }
        }

        // Basculer le monitoring
        function toggleMonitoring() {
            isMonitoring = !isMonitoring;
            const icon = document.getElementById('monitor-icon');

            if (isMonitoring) {
                icon.className = 'fas fa-pause';
                startAutoUpdate();
                showSuccess('🔄 Monitoring repris');
            } else {
                icon.className = 'fas fa-play';
                stopAutoUpdate();
                showWarning('⏸️ Monitoring en pause');
            }
        }

        // Réinitialiser les données
        function resetData() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser les données ?')) {
                showLoading('🔄 Réinitialisation en cours...');
                setTimeout(() => {
                    loadTransferData();
                    showSuccess('✅ Données réinitialisées');
                }, 1000);
            }
        }

        // Exporter les données
        function exportData() {
            const dataStr = JSON.stringify(transferData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `transfer-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            showSuccess('📁 Données de transfert exportées');
        }

        // Démarrer la mise à jour automatique
        function startAutoUpdate() {
            if (updateInterval) return;

            updateInterval = setInterval(() => {
                if (isMonitoring) {
                    loadTransferData();
                }
            }, 5000); // Toutes les 5 secondes
        }

        // Arrêter la mise à jour automatique
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // Nettoyer lors de la fermeture
        window.addEventListener('beforeunload', stopAutoUpdate);
    </script>
</body>
</html>
