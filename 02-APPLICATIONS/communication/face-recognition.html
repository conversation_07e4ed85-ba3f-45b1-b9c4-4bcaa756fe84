<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reconnaissance Faciale - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .camera-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .camera-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 4/3;
            margin-bottom: 20px;
        }

        .camera-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .face-box {
            position: absolute;
            border: 2px solid #ff69b4;
            border-radius: 5px;
            background: rgba(255, 105, 180, 0.1);
        }

        .face-label {
            position: absolute;
            top: -25px;
            left: 0;
            background: #ff69b4;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
        }

        .camera-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #888;
            background: linear-gradient(45deg, #333, #555);
        }

        .camera-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .camera-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .control-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .control-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .control-btn.danger:hover {
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }

        .status-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .status-text {
            font-size: 14px;
            color: #ccc;
        }

        .recognition-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .faces-list {
            flex: 1;
            overflow-y: auto;
            max-height: 400px;
        }

        .face-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .face-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .face-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
        }

        .face-info {
            flex: 1;
        }

        .face-name {
            font-size: 16px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .face-details {
            font-size: 12px;
            color: #ccc;
        }

        .face-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 10px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .settings-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .setting-label {
            font-size: 14px;
            color: #ccc;
        }

        .setting-toggle {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .setting-toggle.active {
            background: #ff69b4;
        }

        .setting-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .setting-toggle.active::after {
            left: 27px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-user-check"></i>
            Reconnaissance Faciale
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/image-generator-simple.html" class="nav-btn">
                <i class="fas fa-image"></i>
                Images
            </a>
            <a href="/web-search.html" class="nav-btn">
                <i class="fas fa-search"></i>
                Recherche
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Section caméra -->
        <div class="camera-section">
            <div class="section-title">
                <i class="fas fa-camera"></i>
                Caméra et Détection
            </div>

            <div class="camera-container">
                <video class="camera-video" id="cameraVideo" autoplay muted style="display: none;"></video>
                <canvas class="camera-overlay" id="cameraOverlay"></canvas>
                <div class="camera-placeholder" id="cameraPlaceholder">
                    <i class="fas fa-camera"></i>
                    <p>Caméra non activée</p>
                    <p>Cliquez sur "Démarrer" pour commencer</p>
                </div>
            </div>

            <div class="camera-controls">
                <button class="control-btn" id="startBtn" onclick="startCamera()">
                    <i class="fas fa-play"></i>
                    Démarrer
                </button>
                <button class="control-btn secondary" id="captureBtn" onclick="capturePhoto()" disabled>
                    <i class="fas fa-camera"></i>
                    Capturer
                </button>
                <button class="control-btn danger" id="stopBtn" onclick="stopCamera()" disabled>
                    <i class="fas fa-stop"></i>
                    Arrêter
                </button>
            </div>

            <div class="status-display" id="statusDisplay">
                <div class="status-icon">
                    <i class="fas fa-info-circle" style="color: #2196f3;"></i>
                </div>
                <div class="status-text">Système prêt - Cliquez sur Démarrer</div>
            </div>

            <div class="settings-section">
                <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                    <i class="fas fa-cog"></i>
                    Paramètres
                </div>
                <div class="setting-item">
                    <span class="setting-label">Détection automatique</span>
                    <div class="setting-toggle active" onclick="toggleSetting(this)" data-setting="autoDetect"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Sauvegarde automatique</span>
                    <div class="setting-toggle" onclick="toggleSetting(this)" data-setting="autoSave"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Notifications sonores</span>
                    <div class="setting-toggle" onclick="toggleSetting(this)" data-setting="soundNotif"></div>
                </div>
            </div>
        </div>

        <!-- Section reconnaissance -->
        <div class="recognition-section">
            <div class="section-title">
                <i class="fas fa-users"></i>
                Visages Reconnus
            </div>

            <div class="faces-list" id="facesList">
                <div class="face-item">
                    <div class="face-avatar">JL</div>
                    <div class="face-info">
                        <div class="face-name">Jean-Luc Passave</div>
                        <div class="face-details">Créateur • Dernière vue: Maintenant • Confiance: 98%</div>
                    </div>
                    <div class="face-actions">
                        <button class="action-btn" onclick="editFace('jean-luc')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn" onclick="deleteFace('jean-luc')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="face-item">
                    <div class="face-avatar">?</div>
                    <div class="face-info">
                        <div class="face-name">Visage Inconnu #1</div>
                        <div class="face-details">Détecté: Il y a 5 min • Confiance: 85%</div>
                    </div>
                    <div class="face-actions">
                        <button class="action-btn" onclick="identifyFace('unknown-1')">
                            <i class="fas fa-user-plus"></i>
                        </button>
                        <button class="action-btn" onclick="ignoreFace('unknown-1')">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="camera-controls" style="margin-top: 20px;">
                <button class="control-btn secondary" onclick="addNewFace()">
                    <i class="fas fa-user-plus"></i>
                    Ajouter Visage
                </button>
                <button class="control-btn" onclick="trainModel()">
                    <i class="fas fa-brain"></i>
                    Entraîner Modèle
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let cameraStream = null;
        let isDetecting = false;
        let detectionInterval = null;
        let recognizedFaces = [];
        let settings = {
            autoDetect: true,
            autoSave: false,
            soundNotif: false
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('👁️ Système de reconnaissance faciale initialisé');
            loadSettings();
            loadRecognizedFaces();
        });

        async function startCamera() {
            try {
                updateStatus('Démarrage de la caméra...', 'info');
                
                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                };

                cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
                
                const video = document.getElementById('cameraVideo');
                const placeholder = document.getElementById('cameraPlaceholder');
                
                video.srcObject = cameraStream;
                video.style.display = 'block';
                placeholder.style.display = 'none';
                
                // Mettre à jour les boutons
                document.getElementById('startBtn').disabled = true;
                document.getElementById('captureBtn').disabled = false;
                document.getElementById('stopBtn').disabled = false;
                
                updateStatus('Caméra active - Détection en cours', 'success');
                
                // Démarrer la détection si activée
                if (settings.autoDetect) {
                    startDetection();
                }
                
                showNotification('Caméra démarrée avec succès', 'success');
                
            } catch (error) {
                console.error('Erreur accès caméra:', error);
                updateStatus('Erreur: Impossible d\'accéder à la caméra', 'error');
                showNotification('Erreur d\'accès à la caméra', 'error');
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }
            
            stopDetection();
            
            const video = document.getElementById('cameraVideo');
            const placeholder = document.getElementById('cameraPlaceholder');
            
            video.style.display = 'none';
            placeholder.style.display = 'flex';
            
            // Mettre à jour les boutons
            document.getElementById('startBtn').disabled = false;
            document.getElementById('captureBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            
            updateStatus('Caméra arrêtée', 'info');
            showNotification('Caméra arrêtée', 'warning');
        }

        function startDetection() {
            if (isDetecting) return;
            
            isDetecting = true;
            detectionInterval = setInterval(() => {
                detectFaces();
            }, 1000); // Détection chaque seconde
            
            console.log('🔍 Détection faciale démarrée');
        }

        function stopDetection() {
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
            isDetecting = false;
            
            console.log('⏹️ Détection faciale arrêtée');
        }

        function detectFaces() {
            // Simulation de détection faciale
            const video = document.getElementById('cameraVideo');
            if (!video.srcObject) return;
            
            // Simuler la détection de Jean-Luc Passave
            if (Math.random() > 0.7) {
                const faceData = {
                    name: 'Jean-Luc Passave',
                    confidence: 95 + Math.random() * 5,
                    timestamp: new Date().toISOString(),
                    location: 'Sainte-Anne, Guadeloupe'
                };
                
                onFaceDetected(faceData);
            }
        }

        function onFaceDetected(faceData) {
            console.log('👤 Visage détecté:', faceData);
            
            // Mettre à jour le statut
            updateStatus(`Visage reconnu: ${faceData.name} (${Math.round(faceData.confidence)}%)`, 'success');
            
            // Ajouter à la mémoire thermique si disponible
            addToThermalMemory(faceData);
            
            // Notification sonore si activée
            if (settings.soundNotif) {
                playNotificationSound();
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraVideo');
            if (!video.srcObject) return;
            
            // Créer un canvas pour capturer l'image
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            // Convertir en blob et sauvegarder
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `capture_${Date.now()}.jpg`;
                a.click();
                
                showNotification('Photo capturée et téléchargée', 'success');
            }, 'image/jpeg', 0.9);
        }

        function updateStatus(message, type) {
            const statusDisplay = document.getElementById('statusDisplay');
            const icon = statusDisplay.querySelector('.status-icon i');
            const text = statusDisplay.querySelector('.status-text');
            
            text.textContent = message;
            
            // Mettre à jour l'icône selon le type
            icon.className = `fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}`;
            icon.style.color = type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3';
        }

        function toggleSetting(toggle) {
            const setting = toggle.dataset.setting;
            const isActive = toggle.classList.contains('active');
            
            if (isActive) {
                toggle.classList.remove('active');
                settings[setting] = false;
            } else {
                toggle.classList.add('active');
                settings[setting] = true;
            }
            
            saveSettings();
            
            // Actions spécifiques selon le paramètre
            if (setting === 'autoDetect') {
                if (settings[setting] && cameraStream) {
                    startDetection();
                } else {
                    stopDetection();
                }
            }
        }

        function addNewFace() {
            const name = prompt('Nom de la personne:');
            if (name) {
                const newFace = {
                    id: Date.now(),
                    name: name,
                    confidence: 100,
                    timestamp: new Date().toISOString(),
                    isNew: true
                };
                
                recognizedFaces.unshift(newFace);
                updateFacesList();
                saveRecognizedFaces();
                
                showNotification(`Visage "${name}" ajouté`, 'success');
            }
        }

        function trainModel() {
            showNotification('Entraînement du modèle en cours...', 'warning');
            
            // Simuler l'entraînement
            setTimeout(() => {
                showNotification('Modèle entraîné avec succès!', 'success');
            }, 3000);
        }

        function editFace(faceId) {
            showNotification('Fonction d\'édition en développement', 'warning');
        }

        function deleteFace(faceId) {
            if (confirm('Supprimer ce visage de la base de données?')) {
                showNotification('Visage supprimé', 'warning');
            }
        }

        function identifyFace(faceId) {
            const name = prompt('Identifier ce visage:');
            if (name) {
                showNotification(`Visage identifié comme "${name}"`, 'success');
            }
        }

        function ignoreFace(faceId) {
            showNotification('Visage ignoré', 'warning');
        }

        function updateFacesList() {
            // Mise à jour de la liste des visages (simulation)
            console.log('📝 Mise à jour liste des visages');
        }

        function addToThermalMemory(faceData) {
            // Simulation d'ajout à la mémoire thermique
            console.log('💾 Ajout à la mémoire thermique:', faceData);
        }

        function playNotificationSound() {
            // Simulation de notification sonore
            console.log('🔊 Notification sonore');
        }

        function saveSettings() {
            localStorage.setItem('lounaFaceRecognitionSettings', JSON.stringify(settings));
        }

        function loadSettings() {
            const saved = localStorage.getItem('lounaFaceRecognitionSettings');
            if (saved) {
                settings = { ...settings, ...JSON.parse(saved) };
                
                // Appliquer les paramètres à l'interface
                Object.keys(settings).forEach(key => {
                    const toggle = document.querySelector(`[data-setting="${key}"]`);
                    if (toggle) {
                        if (settings[key]) {
                            toggle.classList.add('active');
                        } else {
                            toggle.classList.remove('active');
                        }
                    }
                });
            }
        }

        function saveRecognizedFaces() {
            localStorage.setItem('lounaRecognizedFaces', JSON.stringify(recognizedFaces));
        }

        function loadRecognizedFaces() {
            const saved = localStorage.getItem('lounaRecognizedFaces');
            if (saved) {
                recognizedFaces = JSON.parse(saved);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
