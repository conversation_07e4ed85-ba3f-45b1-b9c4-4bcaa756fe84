<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activité Cérébrale | Agent à Mémoire Thermique</title>
    
    <!-- Styles principaux -->
    <link rel="stylesheet" href="/css/reset.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/neural-animations.css">
    <link rel="stylesheet" href="/css/brain-activity-visualizations.css">
    <link rel="stylesheet" href="/css/enhanced-neural-animation.css">
    <link rel="stylesheet" href="/css/futuristic-interface.css">
    <link rel="stylesheet" href="/css/modern-components.css">
    <link rel="stylesheet" href="/css/advanced-visualizations.css">
    
    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        body {
            background: radial-gradient(circle at center, #0a192f 0%, #05071b 100%);
            color: white;
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100vh;
            font-family: 'Roboto', sans-serif;
        }
        
        .activity-page-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(10, 25, 47, 0.8);
            padding: 1rem;
            z-index: 10;
            border-bottom: 1px solid rgba(52, 152, 219, 0.3);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo i {
            font-size: 1.5rem;
            color: #3498db;
        }
        
        .logo h1 {
            font-size: 1.2rem;
            margin: 0;
        }
        
        .kyber-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #2ecc71;
        }
        
        nav {
            background-color: rgba(10, 25, 47, 0.8);
            padding: 0.5rem;
            z-index: 10;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            padding: 0;
            margin: 0;
            justify-content: center;
        }
        
        nav ul li {
            margin: 0 1rem;
        }
        
        nav ul li a {
            display: flex;
            align-items: center;
            gap: 5px;
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        nav ul li a:hover, nav ul li a.active {
            background-color: rgba(52, 152, 219, 0.3);
        }
        
        .neural-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .content-container {
            flex: 1;
            position: relative;
            overflow: hidden;
            z-index: 5;
            padding: 2rem;
        }
        
        .brain-activity-dashboard {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: auto auto;
            gap: 1.5rem;
            height: 100%;
            position: relative;
            z-index: 5;
        }
        
        /* Styles pour la visualisation 3D */
        .brain-visualization-card {
            grid-column: span 2;
            display: flex;
            flex-direction: column;
            height: 400px;
        }
        
        #brain-visualization-container {
            flex: 1;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background-color: rgba(10, 25, 47, 0.3);
        }
        
        .brain-controls {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .brain-control-button {
            background-color: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .brain-control-button:hover {
            background-color: rgba(52, 152, 219, 0.5);
        }
        
        .thoughts-panel {
            position: absolute;
            right: 20px;
            top: 20px;
            width: 300px;
            max-height: 360px;
            overflow-y: auto;
            background-color: rgba(10, 25, 47, 0.8);
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10;
            backdrop-filter: blur(5px);
        }
        
        .thought-item {
            background-color: rgba(255, 255, 255, 0.05);
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 3px solid #3498db;
        }
        
        .thought-header {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            margin-bottom: 5px;
        }
        
        .thought-category {
            color: #3498db;
            font-weight: bold;
        }
        
        .thought-time {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .thought-content {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .thought-metadata {
            font-size: 0.8rem;
            display: flex;
            justify-content: space-between;
        }
        
        .thought-importance {
            display: flex;
            align-items: center;
        }
        
        .importance-bar {
            display: flex;
            margin-left: 5px;
        }
        
        .importance-segment {
            width: 4px;
            height: 8px;
            margin-right: 2px;
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .importance-segment.active {
            background-color: #3498db;
        }
        
        .thought-source {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .category-memory { border-left-color: #3498db; }
        .category-reasoning { border-left-color: #e74c3c; }
        .category-learning { border-left-color: #2ecc71; }
        .category-perception { border-left-color: #f39c12; }
        .category-planning { border-left-color: #9b59b6; }
        
        .dashboard-card {
            background-color: rgba(10, 25, 47, 0.7);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .dashboard-card-header {
            margin-bottom: 1rem;
        }
        
        .dashboard-card-title {
            font-size: 1.1rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 7px;
        }
        
        .dashboard-card-title i {
            color: #3498db;
        }
        
        .metrics-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1rem;
            height: 100%;
        }
        
        .metric-box {
            background-color: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3498db;
            margin: 0.5rem 0;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .large-chart {
            grid-column: span 2;
        }
        
        .brain-chart-container {
            width: 100%;
            height: 250px;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="activity-page-container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>Agent à Mémoire Thermique</h1>
            </div>
            <div class="kyber-status">
                <span id="kyber-indicator" class="status-indicator"></span>
                <span id="kyber-label">Kyber Actif</span>
                <button id="kyber-boost" class="kyber-boost-btn" title="Activer le boost Kyber"><i class="fas fa-bolt"></i></button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="index.html"><i class="fas fa-arrow-left"></i> Retour</a></li>
                <li><a href="#activity" class="active"><i class="fas fa-brain"></i> Activité Cérébrale</a></li>
                <li><a href="kyber-dashboard.html"><i class="fas fa-tachometer-alt"></i> Kyber</a></li>
            </ul>
        </nav>
        
        <div class="neural-container" id="neural-container">
            <!-- L'animation neuronale sera injectée ici -->
        </div>
        
        <div class="content-container">
            <div class="brain-activity-dashboard">
                <!-- Nouvelle visualisation 3D du cerveau -->
                <div class="dashboard-card brain-visualization-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">
                            <i class="fas fa-brain"></i> Visualisation Avancée du Cerveau
                        </div>
                    </div>
                    <div id="brain-visualization-container"></div>
                    <div class="thoughts-panel">
                        <h3>Pensées Cognitives</h3>
                        <div id="thoughts-list"></div>
                    </div>
                    <div class="brain-controls">
                        <button class="brain-control-button" id="toggle-regions"><i class="fas fa-layer-group"></i> Afficher/Masquer Régions</button>
                        <button class="brain-control-button" id="toggle-connections"><i class="fas fa-project-diagram"></i> Afficher/Masquer Connexions</button>
                        <button class="brain-control-button" id="toggle-thoughts-panel"><i class="fas fa-comment-dots"></i> Afficher/Masquer Pensées</button>
                        <button class="brain-control-button" id="reset-camera"><i class="fas fa-sync"></i> Réinitialiser</button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">
                            <i class="fas fa-microchip"></i> Métriques d'Activité
                        </div>
                    </div>
                    <div class="metrics-container">
                        <div class="metric-box">
                            <div class="metric-label">Activité Neuronale</div>
                            <div class="metric-value">85%</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-label">Connexions Synaptiques</div>
                            <div class="metric-value">1,248</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-label">Charge CPU</div>
                            <div class="metric-value">42%</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-label">Utilisation Mémoire</div>
                            <div class="metric-value">37%</div>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">
                            <i class="fas fa-thermometer-half"></i> Distribution d'Activité
                        </div>
                    </div>
                    <canvas id="activity-distribution" width="400" height="200"></canvas>
                </div>
                
                <div class="dashboard-card large-chart">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">
                            <i class="fas fa-chart-line"></i> Activité Cérébrale (30 dernières minutes)
                        </div>
                    </div>
                    <div class="brain-chart-container">
                        <canvas id="activity-chart" width="800" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="/js/neural-animation.js"></script>
    
    <!-- Three.js pour la visualisation 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <script src="/js/advanced-brain-visualization.js"></script>
    
    <!-- Socket.IO pour les mises à jour en temps réel (mode strictement local) -->
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.4.1/dist/socket.io.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser l'animation neuronale
            const neuralAnimation = new NeuralAnimation('neural-container', {
                nodeCount: 150,
                connectionCount: 300,
                nodeColor: '#2980b9',
                activeNodeColor: '#e74c3c',
                connectionColor: 'rgba(41, 128, 185, 0.5)',
                activeConnectionColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(0, 0, 0, 0)',
                nodeSize: 4,
                maxDistance: 200,
                animate: true,
                veilleMode: false,
                pulseFrequency: 1000,
                activityLevel: 0.8
            });
            
            // Activer beaucoup de nœuds pour montrer une activité intense
            for (let i = 0; i < 30; i++) {
                setTimeout(() => {
                    const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                    neuralAnimation.activateNode(randomNodeId, 1500 + Math.random() * 2000);
                }, i * 100);
            }
            
            // Initialiser les graphiques
            const activityCtx = document.getElementById('activity-chart').getContext('2d');
            const activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 30}, (_, i) => `-${30-i} min`),
                    datasets: [
                        {
                            label: 'Activité Neuronale',
                            data: Array.from({length: 30}, () => Math.floor(60 + Math.random() * 40)),
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Charge Cognitive',
                            data: Array.from({length: 30}, () => Math.floor(40 + Math.random() * 40)),
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        }
                    }
                }
            });
            
            const distributionCtx = document.getElementById('activity-distribution').getContext('2d');
            const distributionChart = new Chart(distributionCtx, {
                type: 'pie',
                data: {
                    labels: ['Analyse', 'Raisonnement', 'Mémoire', 'Apprentissage', 'Planification'],
                    datasets: [{
                        data: [25, 30, 15, 20, 10],
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(155, 89, 182, 0.7)',
                            'rgba(230, 126, 34, 0.7)',
                            'rgba(231, 76, 60, 0.7)'
                        ],
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        }
                    }
                }
            });
            
            // Simuler des mises à jour périodiques des données
            setInterval(() => {
                // Mettre à jour le graphique d'activité
                activityChart.data.datasets.forEach(dataset => {
                    dataset.data.shift();
                    if (dataset.label === 'Activité Neuronale') {
                        dataset.data.push(Math.floor(60 + Math.random() * 40));
                    } else {
                        dataset.data.push(Math.floor(40 + Math.random() * 40));
                    }
                });
                activityChart.update();
                
                // Activer quelques nœuds pour maintenir l'animation
                for (let i = 0; i < 5; i++) {
                    const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                    neuralAnimation.activateNode(randomNodeId, 1000 + Math.random() * 1000);
                }
            }, 3000);
            
            // Initialiser les contrôles de la visualisation 3D
            initBrainVisualizationControls();
        });
        
        // Fonctions de contrôle pour la visualisation 3D du cerveau
        function initBrainVisualizationControls() {
            // Récupérer les références aux boutons
            const toggleRegionsBtn = document.getElementById('toggle-regions');
            const toggleConnectionsBtn = document.getElementById('toggle-connections');
            const toggleThoughtsPanelBtn = document.getElementById('toggle-thoughts-panel');
            const resetCameraBtn = document.getElementById('reset-camera');
            const thoughtsPanel = document.querySelector('.thoughts-panel');
            
            // État des éléments visuels
            let regionsVisible = true;
            let connectionsVisible = true;
            let thoughtsPanelVisible = true;
            
            // Gestion de l'affichage des régions
            toggleRegionsBtn.addEventListener('click', () => {
                if (window.brainVisualization) {
                    regionsVisible = !regionsVisible;
                    
                    // Afficher/masquer toutes les régions
                    for (const regionName in window.brainVisualization.activeRegions) {
                        const region = window.brainVisualization.activeRegions[regionName];
                        if (region && region.mesh) {
                            region.mesh.visible = regionsVisible;
                        }
                    }
                    
                    toggleRegionsBtn.innerHTML = regionsVisible ?
                        '<i class="fas fa-layer-group"></i> Masquer Régions' :
                        '<i class="fas fa-layer-group"></i> Afficher Régions';
                }
            });
            
            // Gestion de l'affichage des connexions
            toggleConnectionsBtn.addEventListener('click', () => {
                if (window.brainVisualization) {
                    connectionsVisible = !connectionsVisible;
                    
                    // Afficher/masquer toutes les connexions
                    for (const connection of window.brainVisualization.connections) {
                        if (connection && connection.mesh) {
                            connection.mesh.visible = connectionsVisible;
                        }
                    }
                    
                    toggleConnectionsBtn.innerHTML = connectionsVisible ?
                        '<i class="fas fa-project-diagram"></i> Masquer Connexions' :
                        '<i class="fas fa-project-diagram"></i> Afficher Connexions';
                }
            });
            
            // Gestion de l'affichage du panneau de pensées
            toggleThoughtsPanelBtn.addEventListener('click', () => {
                thoughtsPanelVisible = !thoughtsPanelVisible;
                
                if (thoughtsPanel) {
                    thoughtsPanel.style.display = thoughtsPanelVisible ? 'block' : 'none';
                }
                
                toggleThoughtsPanelBtn.innerHTML = thoughtsPanelVisible ?
                    '<i class="fas fa-comment-dots"></i> Masquer Pensées' :
                    '<i class="fas fa-comment-dots"></i> Afficher Pensées';
            });
            
            // Réinitialisation de la caméra
            resetCameraBtn.addEventListener('click', () => {
                if (window.brainVisualization && window.brainVisualization.camera && window.brainVisualization.controls) {
                    // Réinitialiser la position de la caméra
                    window.brainVisualization.camera.position.set(0, 0, 30);
                    window.brainVisualization.camera.lookAt(0, 0, 0);
                    window.brainVisualization.controls.reset();
                }
            });
            
            // Créer une socket locale pour la communication en temps réel
            // IMPORTANT: Nous utilisons le mode strictement local, la socket
            // ne communique qu'avec le serveur local, jamais avec l'extérieur
            const socket = io({
                // Limiter les connexions au serveur local uniquement
                transports: ['websocket'],
                // Garantir qu'aucune tentative de reconnexion externe n'est faite
                reconnectionAttempts: 5
            });
            
            // Écouter les pensées cognitives en temps réel
            socket.on('cognitive_thought', (data) => {
                // Vérifier si la visualisation 3D est prête
                if (window.brainVisualization) {
                    window.brainVisualization.processCognitiveData(data);
                }
            });
            
            // Écouter les mises à jour d'activité cérébrale
            socket.on('brain_activity', (data) => {
                // Vérifier si la visualisation 3D est prête
                if (window.brainVisualization) {
                    window.brainVisualization.updateBrainActivity(data);
                }
                
                // Mettre à jour les métriques d'activité sur l'interface
                updateActivityMetrics(data);
            });
            
            // S'abonner au canal des processus cognitifs
            socket.emit('subscribe', 'cognitive_processes');
        }
        
        // Fonction pour mettre à jour les métriques d'activité
        function updateActivityMetrics(data) {
            // Mettre à jour les valeurs des métriques
            const metrics = document.querySelectorAll('.metric-value');
            
            if (data.neural_activity) {
                metrics[0].textContent = `${Math.round(data.neural_activity * 100)}%`;
            }
            
            if (data.synaptic_connections) {
                metrics[1].textContent = data.synaptic_connections.toLocaleString();
            }
            
            if (data.cpu_load) {
                metrics[2].textContent = `${Math.round(data.cpu_load * 100)}%`;
            }
            
            if (data.memory_usage) {
                metrics[3].textContent = `${Math.round(data.memory_usage * 100)}%`;
            }
        }
    </script>
</body>
</html>
