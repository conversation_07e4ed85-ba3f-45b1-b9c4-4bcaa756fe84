<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Thermique - Interface Minimale</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #121212;
            color: #eee;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #1e1e1e;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }
        h1 {
            color: #0f9;
            text-align: center;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .status h2 {
            margin-top: 0;
            color: #0cf;
        }
        button {
            background-color: #0f9;
            color: #121212;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0cf;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Agent Thermique - Interface Minimale</h1>
        
        <div class="status">
            <h2>État du Système</h2>
            <p>Cette interface minimaliste confirme que votre serveur fonctionne correctement.</p>
            <p>Le moteur d'évolution est actif et analyse votre code en arrière-plan.</p>
            <p><strong>Mode de fonctionnement:</strong> 100% local (aucun partage de données)</p>
        </div>
        
        <div class="status">
            <h2>Diagnostique</h2>
            <p>Si vous voyez cette page, le serveur Web fonctionne correctement.</p>
            <p>Problème possible avec les autres interfaces:</p>
            <ul>
                <li>Fichiers JavaScript manquants ou incorrects</li>
                <li>Problèmes de chemins relatifs</li>
                <li>Erreurs dans les scripts d'interface</li>
            </ul>
        </div>
        
        <div class="status">
            <h2>Actions</h2>
            <button id="testBtn">Tester l'API</button>
            <div id="apiResult" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        document.getElementById('testBtn').addEventListener('click', function() {
            fetch('/api/evolution/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('apiResult').innerHTML = 
                        '<pre style="background:#2d2d2d;padding:10px;overflow:auto;">' + 
                        JSON.stringify(data, null, 2) + 
                        '</pre>';
                })
                .catch(error => {
                    document.getElementById('apiResult').innerHTML = 
                        '<p style="color:red;">Erreur: ' + error.message + '</p>';
                });
        });
    </script>
</body>
</html>
