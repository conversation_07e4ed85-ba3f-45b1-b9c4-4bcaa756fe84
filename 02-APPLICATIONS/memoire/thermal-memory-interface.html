<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Mémoire Thermique - Louna AI V3.0</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .temperature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .temp-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .temp-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .temp-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            color: #ff6b35;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .temp-value {
            font-size: 2.5em;
            font-weight: bold;
            margin: 15px 0;
            text-align: center;
        }
        
        .temp-system { color: #4CAF50; }
        .temp-memory { color: #FF9800; }
        .temp-zone { color: #2196F3; }
        
        .temp-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .temp-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 6px;
        }
        
        .temp-fill.system {
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
        }
        
        .temp-fill.memory {
            background: linear-gradient(90deg, #FF9800, #FFC107);
        }
        
        .temp-fill.zone {
            background: linear-gradient(90deg, #2196F3, #03DAC6);
        }
        
        .zones-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .zone-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .zone-card:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .zone-name {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #ff6b35;
        }
        
        .zone-temp {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .zone-entries {
            font-size: 0.9em;
            color: #a0a0ff;
            margin: 5px 0;
        }
        
        .zone-slider {
            width: 100%;
            margin: 15px 0;
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
        }
        
        .zone-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff6b35;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.5);
        }
        
        .zone-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff6b35;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.5);
        }
        
        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            margin-top: 30px;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #FFC107);
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .stats-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            margin-top: 30px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .stat-value {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .real-time-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            text-decoration: none;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="real-time-indicator">
        🔥 TEMPS RÉEL
    </div>

    <div class="navigation">
        <a href="/" class="nav-btn">🏠 Accueil</a>
        <a href="/presentation" class="nav-btn">🧠 Interface</a>
        <a href="/monitoring" class="nav-btn">📊 Monitoring</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔥 Mémoire Thermique</h1>
            <p>Système de mémoire biologique connecté à la température système</p>
        </div>

        <!-- Températures Système -->
        <div class="temperature-grid">
            <div class="temp-card">
                <div class="temp-title">
                    🌡️ Température Système
                </div>
                <div class="temp-value temp-system" id="system-temp">--°C</div>
                <div class="temp-bar">
                    <div class="temp-fill system" id="system-temp-bar" style="width: 0%"></div>
                </div>
                <div style="font-size: 0.9em; color: #a0a0ff;">
                    CPU Principal • <span id="system-cores">-- cœurs</span>
                </div>
            </div>

            <div class="temp-card">
                <div class="temp-title">
                    🔥 Température Mémoire
                </div>
                <div class="temp-value temp-memory" id="memory-temp">37.0°C</div>
                <div class="temp-bar">
                    <div class="temp-fill memory" id="memory-temp-bar" style="width: 37%"></div>
                </div>
                <div style="font-size: 0.9em; color: #a0a0ff;">
                    Mémoire Thermique • <span id="memory-entries">-- entrées</span>
                </div>
            </div>

            <div class="temp-card">
                <div class="temp-title">
                    ⚡ Température Moyenne
                </div>
                <div class="temp-value temp-zone" id="average-temp">--°C</div>
                <div class="temp-bar">
                    <div class="temp-fill zone" id="average-temp-bar" style="width: 0%"></div>
                </div>
                <div style="font-size: 0.9em; color: #a0a0ff;">
                    Toutes zones • <span id="zone-count">6 zones</span>
                </div>
            </div>
        </div>

        <!-- Zones de Mémoire avec Contrôles -->
        <div class="zones-container">
            <div class="zone-card">
                <div class="zone-name">🔥 Zone Instant</div>
                <div class="zone-temp" id="zone-instant-temp">100°C</div>
                <input type="range" class="zone-slider" id="zone-instant-slider" 
                       min="0" max="100" value="100" 
                       onchange="updateZoneTemperature('instant', this.value)">
                <div class="zone-entries" id="zone-instant-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-name">⚡ Zone Court Terme</div>
                <div class="zone-temp" id="zone-short-temp">80°C</div>
                <input type="range" class="zone-slider" id="zone-short-slider" 
                       min="0" max="100" value="80"
                       onchange="updateZoneTemperature('shortTerm', this.value)">
                <div class="zone-entries" id="zone-short-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-name">🧠 Zone Travail</div>
                <div class="zone-temp" id="zone-working-temp">60°C</div>
                <input type="range" class="zone-slider" id="zone-working-slider" 
                       min="0" max="100" value="60"
                       onchange="updateZoneTemperature('working', this.value)">
                <div class="zone-entries" id="zone-working-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-name">📚 Zone Moyen Terme</div>
                <div class="zone-temp" id="zone-medium-temp">40°C</div>
                <input type="range" class="zone-slider" id="zone-medium-slider" 
                       min="0" max="100" value="40"
                       onchange="updateZoneTemperature('mediumTerm', this.value)">
                <div class="zone-entries" id="zone-medium-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-name">🏛️ Zone Long Terme</div>
                <div class="zone-temp" id="zone-long-temp">20°C</div>
                <input type="range" class="zone-slider" id="zone-long-slider" 
                       min="0" max="100" value="20"
                       onchange="updateZoneTemperature('longTerm', this.value)">
                <div class="zone-entries" id="zone-long-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-name">🎨 Zone Créative</div>
                <div class="zone-temp" id="zone-creative-temp">10°C</div>
                <input type="range" class="zone-slider" id="zone-creative-slider" 
                       min="0" max="100" value="10"
                       onchange="updateZoneTemperature('creative', this.value)">
                <div class="zone-entries" id="zone-creative-entries">0 entrées</div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls-panel">
            <h3>🎛️ Contrôles de la Mémoire Thermique</h3>
            <div class="control-buttons">
                <button class="control-btn btn-primary" onclick="syncWithSystemTemp()">
                    🌡️ Synchroniser avec Système
                </button>
                <button class="control-btn btn-success" onclick="optimizeZones()">
                    ⚡ Optimiser Zones
                </button>
                <button class="control-btn btn-warning" onclick="resetZones()">
                    🔄 Réinitialiser
                </button>
                <button class="control-btn btn-primary" onclick="saveConfiguration()">
                    💾 Sauvegarder Config
                </button>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="stats-panel">
            <h3>📊 Statistiques de la Mémoire</h3>
            <div class="stat-item">
                <span>Total Entrées</span>
                <span class="stat-value" id="total-entries">0</span>
            </div>
            <div class="stat-item">
                <span>Cycles Effectués</span>
                <span class="stat-value" id="cycles-performed">0</span>
            </div>
            <div class="stat-item">
                <span>Accélérateurs KYBER</span>
                <span class="stat-value" id="kyber-accelerators">0</span>
            </div>
            <div class="stat-item">
                <span>Opérations Fluides</span>
                <span class="stat-value" id="fluid-operations">0</span>
            </div>
            <div class="stat-item">
                <span>Dernière Sauvegarde</span>
                <span class="stat-value" id="last-save">--</span>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let systemTemperature = 0;
        let memoryStats = {};
        let zoneTemperatures = {
            instant: 100,
            shortTerm: 80,
            working: 60,
            mediumTerm: 40,
            longTerm: 20,
            creative: 10
        };

        // Charger la température système
        async function loadSystemTemperature() {
            try {
                const response = await fetch('/api/system-temperature');
                const data = await response.json();
                
                if (data.success) {
                    systemTemperature = data.temperature.main;
                    document.getElementById('system-temp').textContent = Math.round(systemTemperature) + '°C';
                    document.getElementById('system-cores').textContent = data.temperature.cores.length + ' cœurs';
                    
                    // Mettre à jour la barre de progression (0-100°C)
                    const percentage = Math.min(systemTemperature, 100);
                    document.getElementById('system-temp-bar').style.width = percentage + '%';
                    
                    if (data.simulated) {
                        console.log('Température système simulée');
                    }
                }
            } catch (error) {
                console.error('Erreur chargement température:', error);
            }
        }

        // Charger les statistiques de mémoire
        async function loadMemoryStats() {
            try {
                const response = await fetch('/api/memory-stats');
                const data = await response.json();
                
                if (data.success) {
                    memoryStats = data.stats;
                    
                    // Mettre à jour l'affichage
                    document.getElementById('memory-temp').textContent = memoryStats.averageTemperature + '°C';
                    document.getElementById('memory-entries').textContent = memoryStats.totalEntries + ' entrées';
                    document.getElementById('memory-temp-bar').style.width = memoryStats.averageTemperature + '%';
                    
                    // Statistiques
                    document.getElementById('total-entries').textContent = memoryStats.totalEntries;
                    document.getElementById('cycles-performed').textContent = memoryStats.cyclesPerformed;
                    document.getElementById('kyber-accelerators').textContent = 
                        memoryStats.kyberAccelerators ? memoryStats.kyberAccelerators.active : 0;
                    document.getElementById('fluid-operations').textContent = 
                        memoryStats.fluidBuffer ? memoryStats.fluidBuffer.operations : 0;
                    document.getElementById('last-save').textContent = 
                        memoryStats.lastSave ? new Date(memoryStats.lastSave).toLocaleTimeString() : '--';
                    
                    // Zones
                    if (memoryStats.zones) {
                        memoryStats.zones.forEach(zone => {
                            const tempElement = document.getElementById(`zone-${zone.name}-temp`);
                            const entriesElement = document.getElementById(`zone-${zone.name}-entries`);
                            
                            if (tempElement) {
                                tempElement.textContent = zone.temperature + '°C';
                                zoneTemperatures[zone.name] = zone.temperature;
                            }
                            if (entriesElement) {
                                entriesElement.textContent = zone.entries + ' entrées';
                            }
                        });
                    }
                    
                    updateAverageTemperature();
                }
            } catch (error) {
                console.error('Erreur chargement mémoire:', error);
            }
        }

        // Mettre à jour la température d'une zone
        function updateZoneTemperature(zoneName, temperature) {
            zoneTemperatures[zoneName] = parseInt(temperature);
            document.getElementById(`zone-${zoneName}-temp`).textContent = temperature + '°C';
            updateAverageTemperature();
            
            // Simuler la mise à jour dans le système
            console.log(`Zone ${zoneName} mise à jour: ${temperature}°C`);
        }

        // Calculer et afficher la température moyenne
        function updateAverageTemperature() {
            const temps = Object.values(zoneTemperatures);
            const average = temps.reduce((a, b) => a + b, 0) / temps.length;
            
            document.getElementById('average-temp').textContent = Math.round(average) + '°C';
            document.getElementById('average-temp-bar').style.width = average + '%';
        }

        // Synchroniser avec la température système
        function syncWithSystemTemp() {
            if (systemTemperature > 0) {
                // Adapter les zones selon la température système
                const factor = systemTemperature / 50; // Facteur basé sur 50°C de référence
                
                Object.keys(zoneTemperatures).forEach(zone => {
                    const baseTemp = zoneTemperatures[zone];
                    const newTemp = Math.min(100, Math.max(0, baseTemp * factor));
                    
                    zoneTemperatures[zone] = Math.round(newTemp);
                    document.getElementById(`zone-${zone}-temp`).textContent = Math.round(newTemp) + '°C';
                    document.getElementById(`zone-${zone}-slider`).value = Math.round(newTemp);
                });
                
                updateAverageTemperature();
                alert('Zones synchronisées avec la température système !');
            } else {
                alert('Température système non disponible');
            }
        }

        // Optimiser les zones
        function optimizeZones() {
            // Optimisation automatique basée sur l'utilisation
            const optimizedTemps = {
                instant: 95,
                shortTerm: 75,
                working: 55,
                mediumTerm: 35,
                longTerm: 25,
                creative: 15
            };
            
            Object.keys(optimizedTemps).forEach(zone => {
                zoneTemperatures[zone] = optimizedTemps[zone];
                document.getElementById(`zone-${zone}-temp`).textContent = optimizedTemps[zone] + '°C';
                document.getElementById(`zone-${zone}-slider`).value = optimizedTemps[zone];
            });
            
            updateAverageTemperature();
            alert('Zones optimisées pour les performances !');
        }

        // Réinitialiser les zones
        function resetZones() {
            const defaultTemps = {
                instant: 100,
                shortTerm: 80,
                working: 60,
                mediumTerm: 40,
                longTerm: 20,
                creative: 10
            };
            
            Object.keys(defaultTemps).forEach(zone => {
                zoneTemperatures[zone] = defaultTemps[zone];
                document.getElementById(`zone-${zone}-temp`).textContent = defaultTemps[zone] + '°C';
                document.getElementById(`zone-${zone}-slider`).value = defaultTemps[zone];
            });
            
            updateAverageTemperature();
            alert('Zones réinitialisées aux valeurs par défaut !');
        }

        // Sauvegarder la configuration
        function saveConfiguration() {
            const config = {
                zoneTemperatures,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('thermalMemoryConfig', JSON.stringify(config));
            alert('Configuration sauvegardée !');
        }

        // Charger la configuration sauvegardée
        function loadConfiguration() {
            const saved = localStorage.getItem('thermalMemoryConfig');
            if (saved) {
                const config = JSON.parse(saved);
                zoneTemperatures = config.zoneTemperatures;
                
                Object.keys(zoneTemperatures).forEach(zone => {
                    document.getElementById(`zone-${zone}-temp`).textContent = zoneTemperatures[zone] + '°C';
                    document.getElementById(`zone-${zone}-slider`).value = zoneTemperatures[zone];
                });
                
                updateAverageTemperature();
                console.log('Configuration chargée');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadConfiguration();
            loadSystemTemperature();
            loadMemoryStats();
            
            // Actualisation périodique
            setInterval(() => {
                loadSystemTemperature();
                loadMemoryStats();
            }, 5000);
        });
    </script>
</body>
</html>
