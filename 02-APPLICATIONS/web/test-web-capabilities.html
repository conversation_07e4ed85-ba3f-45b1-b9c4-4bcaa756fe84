<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 Test Capacités Web - Louna AI v3.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #ff69b4;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .test-form {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 15px 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .test-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
            justify-content: center;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-section {
            margin-top: 30px;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #4caf50;
        }

        .result-item.error {
            border-left-color: #f44336;
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .result-url {
            color: #4caf50;
            font-size: 14px;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .result-snippet {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .status-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .status-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        .status-testing {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #ff69b4;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quick-test-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 105, 180, 0.3);
            color: white;
            padding: 15px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
        }

        .quick-test-btn:hover {
            border-color: #ff69b4;
            background: rgba(255, 105, 180, 0.2);
            transform: translateY(-2px);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">
        <i class="fas fa-home"></i>
        <span>Accueil</span>
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-globe"></i> Test Capacités Web</h1>
            <p>Testez les capacités de recherche Internet de l'agent Louna AI</p>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-search"></i> Test de Recherche Web</h2>
            
            <div class="quick-tests">
                <button class="quick-test-btn" onclick="testQuery('actualités France')">
                    <i class="fas fa-newspaper"></i><br>Actualités France
                </button>
                <button class="quick-test-btn" onclick="testQuery('intelligence artificielle 2024')">
                    <i class="fas fa-robot"></i><br>IA 2024
                </button>
                <button class="quick-test-btn" onclick="testQuery('programmation JavaScript')">
                    <i class="fas fa-code"></i><br>JavaScript
                </button>
                <button class="quick-test-btn" onclick="testQuery('météo Guadeloupe')">
                    <i class="fas fa-cloud-sun"></i><br>Météo Guadeloupe
                </button>
            </div>

            <div class="test-form">
                <input type="text" class="search-input" id="searchQuery" placeholder="Entrez votre requête de recherche..." value="">
                <button class="test-btn" id="testBtn" onclick="performWebTest()">
                    <i class="fas fa-search"></i>
                    <span>Tester</span>
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Test des capacités web en cours...</p>
            </div>

            <div class="results-section" id="results"></div>
        </div>
    </div>

    <script>
        let isTestingWeb = false;

        function testQuery(query) {
            document.getElementById('searchQuery').value = query;
            performWebTest();
        }

        async function performWebTest() {
            if (isTestingWeb) return;

            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                alert('Veuillez entrer une requête de recherche');
                return;
            }

            isTestingWeb = true;
            updateUI(true);

            try {
                console.log(`🌐 Test des capacités web pour: "${query}"`);

                const response = await fetch('/api/test-web-capabilities', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query })
                });

                const data = await response.json();
                displayResults(data, query);

            } catch (error) {
                console.error('Erreur test web:', error);
                displayError(error.message, query);
            } finally {
                isTestingWeb = false;
                updateUI(false);
            }
        }

        function updateUI(testing) {
            const testBtn = document.getElementById('testBtn');
            const loading = document.getElementById('loading');

            if (testing) {
                testBtn.disabled = true;
                testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Test en cours...</span>';
                loading.classList.add('active');
            } else {
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-search"></i> <span>Tester</span>';
                loading.classList.remove('active');
            }
        }

        function displayResults(data, query) {
            const resultsDiv = document.getElementById('results');
            
            let html = '';

            if (data.success) {
                html += `
                    <div class="status-indicator status-success">
                        <i class="fas fa-check-circle"></i>
                        Capacités web fonctionnelles
                    </div>
                `;

                if (data.results && data.results.length > 0) {
                    html += `<h3><i class="fas fa-list"></i> Résultats pour "${query}" (${data.results.length})</h3>`;
                    
                    data.results.forEach((result, index) => {
                        html += `
                            <div class="result-item">
                                <div class="result-title">${result.title || 'Sans titre'}</div>
                                <div class="result-url">${result.url || 'URL non disponible'}</div>
                                <div class="result-snippet">${result.snippet || 'Aucun extrait disponible'}</div>
                            </div>
                        `;
                    });
                } else {
                    html += `
                        <div class="result-item error">
                            <div class="result-title">Aucun résultat trouvé</div>
                            <div class="result-snippet">La recherche n'a retourné aucun résultat pour "${query}"</div>
                        </div>
                    `;
                }
            } else {
                html += `
                    <div class="status-indicator status-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        Erreur: ${data.error || 'Erreur inconnue'}
                    </div>
                    <div class="result-item error">
                        <div class="result-title">Test échoué</div>
                        <div class="result-snippet">
                            Requête: "${query}"<br>
                            Erreur: ${data.error || 'Erreur inconnue'}<br>
                            Accès web: ${data.webAccess ? 'Disponible' : 'Non disponible'}
                        </div>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        function displayError(error, query) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="status-indicator status-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    Erreur de connexion
                </div>
                <div class="result-item error">
                    <div class="result-title">Erreur de test</div>
                    <div class="result-snippet">
                        Impossible de tester les capacités web.<br>
                        Requête: "${query}"<br>
                        Erreur: ${error}
                    </div>
                </div>
            `;
        }

        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Interface de test des capacités web chargée');
        });

        // Permettre de tester avec Entrée
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performWebTest();
            }
        });
    </script>
</body>
</html>
