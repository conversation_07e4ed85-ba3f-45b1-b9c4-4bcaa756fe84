<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche Web - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
        }

        .search-title {
            font-size: 28px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .search-subtitle {
            color: #ccc;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            max-width: 800px;
            margin: 0 auto;
            align-items: flex-end;
        }

        .search-input-group {
            flex: 1;
        }

        .search-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 25px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .search-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .search-options {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .search-filter {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-filter:hover, .search-filter.active {
            background: rgba(255, 105, 180, 0.3);
            border-color: #ff69b4;
        }

        .results-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            min-height: 400px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .results-count {
            color: #ccc;
            font-size: 14px;
        }

        .results-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid #ff69b4;
        }

        .result-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 8px;
            cursor: pointer;
        }

        .result-title:hover {
            text-decoration: underline;
        }

        .result-url {
            font-size: 12px;
            color: #4caf50;
            margin-bottom: 10px;
        }

        .result-snippet {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .result-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff69b4;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 50px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-results {
            text-align: center;
            padding: 50px;
            color: #888;
        }

        .no-results i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.info {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-options {
                flex-direction: column;
                align-items: center;
            }
            
            .result-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-search"></i>
            Recherche Web Intelligente
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/image-generator-simple.html" class="nav-btn">
                <i class="fas fa-image"></i>
                Images
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Section de recherche -->
        <div class="search-section">
            <div class="search-title">
                <i class="fas fa-globe"></i>
                Recherche Web avec IA
            </div>
            <div class="search-subtitle">
                Recherchez des informations sur internet et enrichissez votre mémoire thermique
            </div>

            <form class="search-form" id="searchForm">
                <div class="search-input-group">
                    <input 
                        type="text" 
                        class="search-input" 
                        id="searchInput"
                        placeholder="Que voulez-vous rechercher ? (ex: dernières nouvelles en IA, tutoriels JavaScript...)"
                        required
                    >
                </div>
                <button type="submit" class="search-btn" id="searchBtn">
                    <i class="fas fa-search"></i>
                    Rechercher
                </button>
            </form>

            <div class="search-options">
                <div class="search-filter active" data-filter="all">
                    <i class="fas fa-globe"></i> Tout
                </div>
                <div class="search-filter" data-filter="news">
                    <i class="fas fa-newspaper"></i> Actualités
                </div>
                <div class="search-filter" data-filter="tech">
                    <i class="fas fa-microchip"></i> Technologie
                </div>
                <div class="search-filter" data-filter="science">
                    <i class="fas fa-flask"></i> Science
                </div>
                <div class="search-filter" data-filter="education">
                    <i class="fas fa-graduation-cap"></i> Éducation
                </div>
            </div>
        </div>

        <!-- Section des résultats -->
        <div class="results-section">
            <div class="results-header">
                <div class="results-title">
                    <i class="fas fa-list"></i>
                    Résultats de recherche
                </div>
                <div class="results-count" id="resultsCount">
                    Aucune recherche effectuée
                </div>
            </div>

            <div class="results-list" id="resultsList">
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>Effectuez une recherche pour voir les résultats ici.</p>
                    <p>Les résultats seront automatiquement ajoutés à votre mémoire thermique !</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isSearching = false;
        let currentFilter = 'all';
        let searchHistory = [];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Recherche web intelligente initialisée');
            setupEventListeners();
            loadSearchHistory();
        });

        function setupEventListeners() {
            const form = document.getElementById('searchForm');
            form.addEventListener('submit', handleSearch);

            // Filtres de recherche
            const filters = document.querySelectorAll('.search-filter');
            filters.forEach(filter => {
                filter.addEventListener('click', function() {
                    filters.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                });
            });
        }

        async function handleSearch(event) {
            event.preventDefault();
            
            if (isSearching) return;
            
            const query = document.getElementById('searchInput').value.trim();
            
            if (!query) {
                showNotification('Veuillez entrer une requête de recherche', 'error');
                return;
            }

            isSearching = true;
            updateSearchButton(true);
            showLoading();

            try {
                // Simuler une recherche web
                await performWebSearch(query, currentFilter);
                
            } catch (error) {
                console.error('Erreur recherche web:', error);
                showNotification('Erreur lors de la recherche', 'error');
                showNoResults();
            } finally {
                isSearching = false;
                updateSearchButton(false);
            }
        }

        async function performWebSearch(query, filter) {
            console.log(`🔍 Recherche: "${query}" (filtre: ${filter})`);
            
            // Simuler un délai de recherche
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Générer des résultats simulés intelligents
            const results = generateSimulatedResults(query, filter);
            
            // Afficher les résultats
            displayResults(results, query);
            
            // Ajouter à l'historique
            addToSearchHistory(query, filter, results.length);
            
            // Ajouter à la mémoire thermique
            addToThermalMemory(query, results);
            
            showNotification(`${results.length} résultats trouvés pour "${query}"`, 'success');
        }

        function generateSimulatedResults(query, filter) {
            const baseResults = [
                {
                    title: `Guide complet sur ${query}`,
                    url: `https://example.com/guide-${query.replace(/\s+/g, '-')}`,
                    snippet: `Un guide détaillé et complet sur ${query}. Découvrez tout ce que vous devez savoir avec des exemples pratiques et des conseils d'experts.`,
                    source: 'Guide Expert',
                    date: new Date().toLocaleDateString()
                },
                {
                    title: `${query} - Dernières actualités et tendances`,
                    url: `https://news.example.com/${query.replace(/\s+/g, '-')}-news`,
                    snippet: `Les dernières actualités et tendances concernant ${query}. Restez informé des développements récents et des innovations dans ce domaine.`,
                    source: 'Actualités Tech',
                    date: new Date().toLocaleDateString()
                },
                {
                    title: `Tutoriel pratique : ${query} pour débutants`,
                    url: `https://tutorial.example.com/${query.replace(/\s+/g, '-')}-tutorial`,
                    snippet: `Apprenez ${query} étape par étape avec ce tutoriel pratique. Parfait pour les débutants qui veulent maîtriser rapidement les concepts de base.`,
                    source: 'Tutoriels Pro',
                    date: new Date().toLocaleDateString()
                },
                {
                    title: `${query} - Forum de discussion et communauté`,
                    url: `https://forum.example.com/discussion-${query.replace(/\s+/g, '-')}`,
                    snippet: `Rejoignez la discussion sur ${query}. Partagez vos expériences, posez vos questions et apprenez de la communauté d'experts.`,
                    source: 'Forum Communauté',
                    date: new Date().toLocaleDateString()
                },
                {
                    title: `Outils et ressources pour ${query}`,
                    url: `https://tools.example.com/${query.replace(/\s+/g, '-')}-tools`,
                    snippet: `Découvrez les meilleurs outils et ressources pour ${query}. Une sélection curatée d'outils professionnels et gratuits.`,
                    source: 'Ressources Tools',
                    date: new Date().toLocaleDateString()
                }
            ];

            // Adapter selon le filtre
            return baseResults.slice(0, Math.floor(Math.random() * 3) + 3);
        }

        function displayResults(results, query) {
            const resultsList = document.getElementById('resultsList');
            const resultsCount = document.getElementById('resultsCount');
            
            resultsCount.textContent = `${results.length} résultats pour "${query}"`;
            
            resultsList.innerHTML = '';
            
            results.forEach((result, index) => {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = `
                    <div class="result-title" onclick="openResult('${result.url}')">
                        ${result.title}
                    </div>
                    <div class="result-url">${result.url}</div>
                    <div class="result-snippet">${result.snippet}</div>
                    <div class="result-meta">
                        <span><i class="fas fa-globe"></i> ${result.source}</span>
                        <span><i class="fas fa-calendar"></i> ${result.date}</span>
                        <span><i class="fas fa-eye"></i> Résultat ${index + 1}</span>
                    </div>
                `;
                resultsList.appendChild(resultItem);
            });
        }

        function showLoading() {
            const resultsList = document.getElementById('resultsList');
            resultsList.innerHTML = '<div class="loading-spinner"></div>';
            document.getElementById('resultsCount').textContent = 'Recherche en cours...';
        }

        function showNoResults() {
            const resultsList = document.getElementById('resultsList');
            resultsList.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Aucun résultat trouvé.</p>
                    <p>Essayez avec des mots-clés différents.</p>
                </div>
            `;
            document.getElementById('resultsCount').textContent = 'Aucun résultat';
        }

        function updateSearchButton(searching) {
            const btn = document.getElementById('searchBtn');
            if (searching) {
                btn.innerHTML = '<div class="loading-spinner" style="width: 20px; height: 20px; border-width: 2px;"></div> Recherche...';
                btn.disabled = true;
            } else {
                btn.innerHTML = '<i class="fas fa-search"></i> Rechercher';
                btn.disabled = false;
            }
        }

        function openResult(url) {
            console.log('Ouverture du résultat:', url);
            showNotification('Résultat simulé - URL copiée dans les logs', 'info');
        }

        function addToSearchHistory(query, filter, resultCount) {
            const searchEntry = {
                query: query,
                filter: filter,
                resultCount: resultCount,
                timestamp: new Date().toISOString()
            };
            
            searchHistory.unshift(searchEntry);
            
            // Limiter l'historique à 50 entrées
            if (searchHistory.length > 50) {
                searchHistory = searchHistory.slice(0, 50);
            }
            
            localStorage.setItem('lounaSearchHistory', JSON.stringify(searchHistory));
        }

        function loadSearchHistory() {
            const saved = localStorage.getItem('lounaSearchHistory');
            if (saved) {
                searchHistory = JSON.parse(saved);
            }
        }

        function addToThermalMemory(query, results) {
            // Simuler l'ajout à la mémoire thermique
            console.log('💾 Ajout à la mémoire thermique:', {
                query: query,
                resultCount: results.length,
                timestamp: new Date().toISOString()
            });
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
