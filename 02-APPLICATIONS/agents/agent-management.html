<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Gestion des Agents - Louna AI v3.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #ff69b4;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 105, 180, 0.3);
        }

        .agent-card.active {
            border-color: #00ff00;
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1));
            animation: pulseGreen 2s infinite;
        }

        .agent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .agent-icon {
            font-size: 3rem;
            color: #ff69b4;
        }

        .agent-icon.active {
            color: #00ff00;
            animation: bounce 2s infinite;
        }

        .agent-status {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
            border: 1px solid #00ff00;
        }

        .status-inactive {
            background: rgba(255, 68, 68, 0.2);
            color: #ff4444;
            border: 1px solid #ff4444;
        }

        .status-training {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .agent-info {
            margin-bottom: 25px;
        }

        .agent-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .agent-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .agent-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ff00;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .agent-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 105, 180, 0.4);
        }

        .control-btn.start {
            background: linear-gradient(135deg, #00ff00, #00cc00);
        }

        .control-btn.stop {
            background: linear-gradient(135deg, #ff4444, #cc0000);
        }

        .control-btn.train {
            background: linear-gradient(135deg, #ffc107, #ff9800);
        }

        .system-overview {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .overview-item {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }

        .overview-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .overview-label {
            color: rgba(255, 255, 255, 0.8);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        @keyframes pulseGreen {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 0, 0.3); }
            50% { box-shadow: 0 0 40px rgba(0, 255, 0, 0.6); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .logs-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            max-height: 300px;
            overflow-y: auto;
        }

        .log-item {
            padding: 10px 15px;
            margin: 5px 0;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            border-left: 4px solid #ff69b4;
        }

        .log-item.success {
            border-left-color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }

        .log-item.error {
            border-left-color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
        }

        .log-item.warning {
            border-left-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
    </style>
</head>
<body>
    <a href="/chat" class="back-btn">
        <i class="fas fa-comments"></i>
        <span>Chat</span>
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-robot"></i> Gestion des Agents IA</h1>
            <p>Centre de contrôle des agents intelligents Louna AI</p>
        </div>

        <div class="system-overview">
            <h2 style="text-align: center; color: #ff69b4; margin-bottom: 25px;">
                <i class="fas fa-chart-line"></i> Vue d'ensemble du système
            </h2>
            <div class="overview-grid">
                <div class="overview-item">
                    <div class="overview-value" id="totalAgents">3</div>
                    <div class="overview-label">Agents Totaux</div>
                </div>
                <div class="overview-item">
                    <div class="overview-value" id="activeAgents">2</div>
                    <div class="overview-label">Agents Actifs</div>
                </div>
                <div class="overview-item">
                    <div class="overview-value" id="systemQI">267</div>
                    <div class="overview-label">QI Système</div>
                </div>
                <div class="overview-item">
                    <div class="overview-value" id="uptime">99.8%</div>
                    <div class="overview-label">Disponibilité</div>
                </div>
            </div>
        </div>

        <div class="agents-grid">
            <!-- Agent Principal Louna -->
            <div class="agent-card active" id="agent-louna">
                <div class="agent-header">
                    <div class="agent-icon active">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="agent-status status-active">ACTIF</div>
                </div>
                <div class="agent-info">
                    <div class="agent-name">Louna Principal</div>
                    <div class="agent-description">
                        Agent principal avec mémoire thermique évolutive et QI adaptatif. 
                        Spécialisé dans les conversations intelligentes et l'apprentissage continu.
                    </div>
                    <div class="agent-stats">
                        <div class="stat-item">
                            <div class="stat-value">235</div>
                            <div class="stat-label">QI Actuel</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">89%</div>
                            <div class="stat-label">Efficacité</div>
                        </div>
                    </div>
                </div>
                <div class="agent-controls">
                    <button class="control-btn stop" onclick="controlAgent('louna', 'stop')">
                        <i class="fas fa-stop"></i> Arrêter
                    </button>
                    <button class="control-btn train" onclick="controlAgent('louna', 'train')">
                        <i class="fas fa-graduation-cap"></i> Former
                    </button>
                    <button class="control-btn" onclick="controlAgent('louna', 'config')">
                        <i class="fas fa-cog"></i> Config
                    </button>
                </div>
            </div>

            <!-- Agent DeepSeek Formation -->
            <div class="agent-card active" id="agent-deepseek">
                <div class="agent-header">
                    <div class="agent-icon active">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="agent-status status-training">FORMATION</div>
                </div>
                <div class="agent-info">
                    <div class="agent-name">DeepSeek Formateur</div>
                    <div class="agent-description">
                        Agent spécialisé dans la formation et l'amélioration continue. 
                        Pose des questions automatiques et enrichit les connaissances.
                    </div>
                    <div class="agent-stats">
                        <div class="stat-item">
                            <div class="stat-value">156</div>
                            <div class="stat-label">Sessions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">94%</div>
                            <div class="stat-label">Succès</div>
                        </div>
                    </div>
                </div>
                <div class="agent-controls">
                    <button class="control-btn stop" onclick="controlAgent('deepseek', 'stop')">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="control-btn start" onclick="controlAgent('deepseek', 'start')">
                        <i class="fas fa-play"></i> Reprendre
                    </button>
                    <button class="control-btn" onclick="controlAgent('deepseek', 'config')">
                        <i class="fas fa-cog"></i> Config
                    </button>
                </div>
            </div>

            <!-- Agent Claude Connecteur -->
            <div class="agent-card" id="agent-claude">
                <div class="agent-header">
                    <div class="agent-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="agent-status status-inactive">INACTIF</div>
                </div>
                <div class="agent-info">
                    <div class="agent-name">Claude Connecteur</div>
                    <div class="agent-description">
                        Connecteur vers l'API Claude d'Anthropic pour les tâches complexes 
                        nécessitant une intelligence externe avancée.
                    </div>
                    <div class="agent-stats">
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Requêtes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">--</div>
                            <div class="stat-label">Latence</div>
                        </div>
                    </div>
                </div>
                <div class="agent-controls">
                    <button class="control-btn start" onclick="controlAgent('claude', 'start')">
                        <i class="fas fa-power-off"></i> Activer
                    </button>
                    <button class="control-btn" onclick="controlAgent('claude', 'test')">
                        <i class="fas fa-vial"></i> Tester
                    </button>
                    <button class="control-btn" onclick="controlAgent('claude', 'config')">
                        <i class="fas fa-cog"></i> Config
                    </button>
                </div>
            </div>
        </div>

        <div class="logs-section">
            <h3 style="color: #ff69b4; margin-bottom: 20px;">
                <i class="fas fa-list"></i> Logs du Système
            </h3>
            <div id="logsContainer">
                <div class="log-item success">
                    [12:34:56] Agent Louna Principal démarré avec succès
                </div>
                <div class="log-item success">
                    [12:35:02] Agent DeepSeek Formateur en mode formation
                </div>
                <div class="log-item warning">
                    [12:35:15] Agent Claude Connecteur hors ligne
                </div>
                <div class="log-item">
                    [12:35:30] Système de monitoring actif
                </div>
            </div>
        </div>
    </div>

    <script>
        const agents = {
            louna: { name: 'Louna Principal', status: 'active', qi: 235 },
            deepseek: { name: 'DeepSeek Formateur', status: 'training', sessions: 156 },
            claude: { name: 'Claude Connecteur', status: 'inactive', requests: 0 }
        };

        function controlAgent(agentId, action) {
            const agent = agents[agentId];
            const timestamp = new Date().toLocaleTimeString();
            
            switch(action) {
                case 'start':
                    agent.status = 'active';
                    addLog(`Agent ${agent.name} démarré`, 'success');
                    break;
                case 'stop':
                    agent.status = 'inactive';
                    addLog(`Agent ${agent.name} arrêté`, 'warning');
                    break;
                case 'train':
                    addLog(`Formation de ${agent.name} initiée`, 'success');
                    break;
                case 'config':
                    addLog(`Configuration de ${agent.name} ouverte`, 'info');
                    openAgentConfig(agentId);
                    break;
                case 'test':
                    addLog(`Test de ${agent.name} en cours...`, 'info');
                    setTimeout(() => {
                        addLog(`Test de ${agent.name} réussi`, 'success');
                    }, 2000);
                    break;
            }
            
            updateAgentDisplay(agentId);
            updateOverview();
        }

        function updateAgentDisplay(agentId) {
            const card = document.getElementById(`agent-${agentId}`);
            const agent = agents[agentId];
            
            // Mettre à jour le statut visuel
            const statusElement = card.querySelector('.agent-status');
            const iconElement = card.querySelector('.agent-icon');
            
            card.classList.remove('active');
            iconElement.classList.remove('active');
            statusElement.className = 'agent-status';
            
            if (agent.status === 'active') {
                card.classList.add('active');
                iconElement.classList.add('active');
                statusElement.classList.add('status-active');
                statusElement.textContent = 'ACTIF';
            } else if (agent.status === 'training') {
                statusElement.classList.add('status-training');
                statusElement.textContent = 'FORMATION';
            } else {
                statusElement.classList.add('status-inactive');
                statusElement.textContent = 'INACTIF';
            }
        }

        function updateOverview() {
            const activeCount = Object.values(agents).filter(a => a.status === 'active' || a.status === 'training').length;
            document.getElementById('activeAgents').textContent = activeCount;
        }

        function openAgentConfig(agentId) {
            const agent = agents[agentId];
            alert(`🔧 Configuration ${agent.name}\n\n• Modèle: deepseek-r1:7b\n• Température: 0.7\n• Tokens max: 2000\n• Mémoire: Activée\n• Apprentissage: Continu`);
        }

        function addLog(message, type = 'info') {
            const container = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logItem = document.createElement('div');
            logItem.className = `log-item ${type}`;
            logItem.textContent = `[${timestamp}] ${message}`;
            
            container.insertBefore(logItem, container.firstChild);
            
            // Limiter à 20 logs
            const logs = container.querySelectorAll('.log-item');
            if (logs.length > 20) {
                logs[logs.length - 1].remove();
            }
        }

        // Simulation des mises à jour en temps réel
        function simulateActivity() {
            const activities = [
                'Traitement de requête utilisateur',
                'Sauvegarde mémoire thermique',
                'Optimisation des connexions neuronales',
                'Analyse des patterns de conversation',
                'Mise à jour des accélérateurs KYBER'
            ];
            
            const randomActivity = activities[Math.floor(Math.random() * activities.length)];
            addLog(randomActivity, 'info');
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 Gestionnaire d\'agents initialisé');
            
            // Simulation d'activité toutes les 10 secondes
            setInterval(simulateActivity, 10000);
            
            // Mise à jour des stats toutes les 5 secondes
            setInterval(() => {
                if (agents.louna.status === 'active') {
                    agents.louna.qi = Math.min(agents.louna.qi + 0.1, 250);
                    document.querySelector('#agent-louna .stat-value').textContent = Math.round(agents.louna.qi);
                }
            }, 5000);
        });
    </script>
</body>
</html>
