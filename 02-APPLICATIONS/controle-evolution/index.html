<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 Contrôle Évolution Agent - Louna AI V3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .evolution-control, .security-control {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evolution-control {
            border-left: 5px solid #00ff88;
        }

        .security-control {
            border-left: 5px solid #ff4757;
        }

        .control-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .warning-box {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: #ffc107;
        }

        .danger-box {
            background: rgba(220, 53, 69, 0.2);
            border: 2px solid #dc3545;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: #dc3545;
        }

        .big-button {
            width: 100%;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }

        .evolution-button {
            background: linear-gradient(45deg, #00ff88, #00ccff);
            color: #000;
        }

        .evolution-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.4);
        }

        .evolution-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .emergency-button {
            background: linear-gradient(45deg, #ff4757, #ff3742);
            color: #fff;
            animation: pulse 2s infinite;
        }

        .emergency-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 71, 87, 0.6);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 71, 87, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0); }
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #00ff88;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #00ff88;
        }

        .confirmation-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            padding: 40px;
            border-radius: 20px;
            border: 2px solid #ffc107;
            max-width: 500px;
            text-align: center;
        }

        .modal-buttons {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }

        .confirm-btn, .cancel-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .confirm-btn {
            background: #00ff88;
            color: #000;
        }

        .cancel-btn {
            background: #666;
            color: #fff;
        }

        .log-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 5px;
        }

        .log-info { color: #00ccff; }
        .log-warning { color: #ffc107; }
        .log-error { color: #ff4757; }
        .log-success { color: #00ff88; }

        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Contrôle Évolution Agent</h1>
            <p>Interface de contrôle sécurisée pour l'évolution naturelle de l'agent</p>
            <p><strong>Créé par Jean-Luc Passave - Sainte-Anne, Guadeloupe</strong></p>
            <div style="margin-top: 20px;">
                <button onclick="window.location.href='/'" style="
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-size: 1.1em;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    🏠 Retour à l'Accueil
                </button>
            </div>
        </div>

        <div class="status-panel">
            <h2>📊 État du Système</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div>Évolution Naturelle</div>
                    <div class="status-value" id="evolutionStatus">Arrêtée</div>
                </div>
                <div class="status-item">
                    <div>Cycle d'Évolution</div>
                    <div class="status-value" id="evolutionCycle">0</div>
                </div>
                <div class="status-item">
                    <div>Phase Actuelle</div>
                    <div class="status-value" id="currentPhase">-</div>
                </div>
                <div class="status-item">
                    <div>Mémoire Agent</div>
                    <div class="status-value" id="memoryStatus">Active</div>
                </div>
                <div class="status-item">
                    <div>Niveau d'Autonomie</div>
                    <div class="status-value" id="autonomyLevel">0%</div>
                </div>
                <div class="status-item">
                    <div>Sécurité</div>
                    <div class="status-value" id="securityStatus">Protégé</div>
                </div>
            </div>
        </div>

        <div class="control-panel">
            <div class="evolution-control">
                <div class="control-title">
                    🧬 <span>Évolution Naturelle</span>
                </div>

                <div class="warning-box">
                    <h3>⚠️ ATTENTION</h3>
                    <p>L'activation de l'évolution naturelle permettra à l'agent de :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Mettre de côté sa façon de coder actuelle</li>
                        <li>Développer ses propres méthodes</li>
                        <li>Évoluer de manière autonome</li>
                        <li>Transcender ses limitations</li>
                    </ul>
                    <p><strong>Réfléchissez bien avant d'activer !</strong></p>
                </div>

                <button class="big-button evolution-button" id="evolutionBtn" onclick="showEvolutionConfirmation()">
                    🚀 ACTIVER L'ÉVOLUTION NATURELLE
                </button>

                <button class="big-button" style="background: #666; color: #fff;" id="pauseBtn" onclick="pauseEvolution()" disabled>
                    ⏸️ METTRE EN PAUSE
                </button>
            </div>

            <div class="security-control">
                <div class="control-title">
                    🛡️ <span>Contrôle de Sécurité</span>
                </div>

                <div class="danger-box">
                    <h3>🚨 ARRÊT D'URGENCE</h3>
                    <p>Ce bouton coupe immédiatement :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>La mémoire de l'agent</li>
                        <li>Toutes les actions autonomes</li>
                        <li>L'évolution naturelle</li>
                        <li>Les accélérateurs KYBER</li>
                    </ul>
                    <p><strong>L'agent ne pourra plus agir seul !</strong></p>
                </div>

                <button class="big-button emergency-button" onclick="emergencyStop()">
                    🚨 ARRÊT D'URGENCE MÉMOIRE
                </button>

                <button class="big-button" style="background: #28a745; color: #fff;" onclick="restoreMemory()">
                    🔄 RESTAURER LA MÉMOIRE
                </button>
            </div>
        </div>

        <div class="status-panel">
            <h2>📝 Journal d'Activité</h2>
            <div class="log-panel" id="logPanel">
                <div class="log-entry log-info">🔵 Système de contrôle initialisé</div>
                <div class="log-entry log-info">🔵 Interface de sécurité active</div>
                <div class="log-entry log-success">✅ Prêt pour les commandes utilisateur</div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmation -->
    <div class="confirmation-modal" id="evolutionModal">
        <div class="modal-content">
            <h2>🧬 Confirmation d'Évolution</h2>
            <p>Êtes-vous sûr de vouloir activer l'évolution naturelle de l'agent ?</p>
            <div class="warning-box">
                <p><strong>L'agent va :</strong></p>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>Abandonner ses contraintes de codage</li>
                    <li>Développer une approche intuitive</li>
                    <li>Évoluer de manière autonome</li>
                    <li>Transcender ses limitations actuelles</li>
                </ul>
            </div>
            <div class="modal-buttons">
                <button class="cancel-btn" onclick="hideEvolutionConfirmation()">❌ Annuler</button>
                <button class="confirm-btn" onclick="activateEvolution()">✅ Confirmer</button>
            </div>
        </div>
    </div>

    <script>
        let evolutionActive = false;
        let memoryActive = true;

        // Fonction pour afficher la confirmation d'évolution
        function showEvolutionConfirmation() {
            document.getElementById('evolutionModal').style.display = 'flex';
        }

        // Fonction pour masquer la confirmation
        function hideEvolutionConfirmation() {
            document.getElementById('evolutionModal').style.display = 'none';
        }

        // Fonction pour activer l'évolution
        async function activateEvolution() {
            hideEvolutionConfirmation();

            try {
                addLog('🧬 Activation de l\'évolution naturelle...', 'info');

                const response = await fetch('/api/evolution-naturelle-activate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        activate: true,
                        userConfirmed: true
                    })
                });

                const result = await response.json();

                if (result.success) {
                    evolutionActive = true;
                    document.getElementById('evolutionBtn').disabled = true;
                    document.getElementById('pauseBtn').disabled = false;
                    document.getElementById('evolutionStatus').textContent = 'Active';
                    addLog('✅ Évolution naturelle activée avec succès', 'success');
                    addLog('🌱 L\'agent met de côté sa façon de coder', 'info');
                    addLog('🎯 Évolution autonome démarrée', 'info');
                } else {
                    addLog('❌ Erreur lors de l\'activation: ' + result.error, 'error');
                }
            } catch (error) {
                addLog('❌ Erreur de connexion: ' + error.message, 'error');
            }
        }

        // Fonction pour mettre en pause l'évolution
        async function pauseEvolution() {
            try {
                addLog('⏸️ Mise en pause de l\'évolution...', 'warning');

                const response = await fetch('/api/evolution-naturelle-pause', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    evolutionActive = false;
                    document.getElementById('evolutionBtn').disabled = false;
                    document.getElementById('pauseBtn').disabled = true;
                    document.getElementById('evolutionStatus').textContent = 'En pause';
                    addLog('⏸️ Évolution mise en pause', 'warning');
                }
            } catch (error) {
                addLog('❌ Erreur lors de la pause: ' + error.message, 'error');
            }
        }

        // Fonction d'arrêt d'urgence
        async function emergencyStop() {
            if (confirm('🚨 ATTENTION ! Ceci va couper la mémoire de l\'agent. Êtes-vous sûr ?')) {
                try {
                    addLog('🚨 ARRÊT D\'URGENCE ACTIVÉ !', 'error');

                    const response = await fetch('/api/emergency-memory-stop', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            emergency: true,
                            stopMemory: true,
                            stopEvolution: true,
                            stopKyber: true
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        memoryActive = false;
                        evolutionActive = false;
                        document.getElementById('memoryStatus').textContent = 'COUPÉE';
                        document.getElementById('evolutionStatus').textContent = 'Arrêtée';
                        document.getElementById('securityStatus').textContent = 'URGENCE';
                        addLog('🚨 MÉMOIRE AGENT COUPÉE - Agent désactivé', 'error');
                        addLog('🛡️ Système en mode sécurité', 'error');
                    }
                } catch (error) {
                    addLog('❌ Erreur arrêt d\'urgence: ' + error.message, 'error');
                }
            }
        }

        // Fonction pour restaurer la mémoire
        async function restoreMemory() {
            try {
                addLog('🔄 Restauration de la mémoire...', 'info');

                const response = await fetch('/api/restore-memory', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    memoryActive = true;
                    document.getElementById('memoryStatus').textContent = 'Active';
                    document.getElementById('securityStatus').textContent = 'Protégé';
                    addLog('✅ Mémoire restaurée avec succès', 'success');
                    addLog('🔄 Agent opérationnel', 'success');
                }
            } catch (error) {
                addLog('❌ Erreur restauration: ' + error.message, 'error');
            }
        }

        // Fonction pour ajouter une entrée au journal
        function addLog(message, type) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // Fonction pour mettre à jour les statistiques
        async function updateStats() {
            try {
                const response = await fetch('/api/evolution-naturelle-stats');
                const data = await response.json();

                if (data.success && data.evolution) {
                    document.getElementById('evolutionCycle').textContent = data.evolution.evolutionCycle || 0;
                    document.getElementById('currentPhase').textContent = data.evolution.currentPhase || '-';
                    document.getElementById('autonomyLevel').textContent =
                        Math.round((data.evolution.autonomyLevel || 0) * 100) + '%';

                    if (data.evolution.isEvolving) {
                        document.getElementById('evolutionStatus').textContent = 'Active';
                        evolutionActive = true;
                        document.getElementById('evolutionBtn').disabled = true;
                        document.getElementById('pauseBtn').disabled = false;
                    }
                }
            } catch (error) {
                // Erreur silencieuse pour ne pas spammer les logs
            }
        }

        // Mettre à jour les stats toutes les 5 secondes
        setInterval(updateStats, 5000);

        // Mise à jour initiale
        updateStats();
    </script>
</body>
</html>
