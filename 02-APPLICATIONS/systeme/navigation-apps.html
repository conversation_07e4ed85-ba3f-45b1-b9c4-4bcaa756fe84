<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>🧠 Louna AI - Navigation Applications</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: rgba(255,255,255,0.05); border-radius: 20px; padding: 30px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 2.5em; color: #00ff88; }
        .apps-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .app-category { background: rgba(255,255,255,0.03); border-radius: 15px; padding: 25px; border-left: 4px solid #00ff88; }
        .category-title { font-size: 1.4em; color: #00ff88; margin-bottom: 20px; }
        .app-link { display: block; background: rgba(0,255,136,0.1); color: #fff; text-decoration: none; padding: 12px 15px; border-radius: 8px; margin: 8px 0; transition: all 0.3s; }
        .app-link:hover { background: rgba(0,255,136,0.2); transform: translateX(5px); }
        .back-btn { background: linear-gradient(45deg, #00ff88, #00ccff); color: #000; border: none; padding: 12px 25px; border-radius: 25px; font-weight: bold; text-decoration: none; display: inline-block; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Louna AI - Navigation Applications</h1>
            <p>Accès direct à toutes les applications fonctionnelles</p>
        </div>

        <a href="/" class="back-btn">🏠 Retour Interface Principale</a>

        <div class="apps-grid">
            <div class="app-category">
                <div class="category-title">📱 Communication</div>
                <a href="/phone" class="app-link">📱 Système Caméra/Téléphone</a>
                <a href="/chat" class="app-link">💬 Chat Agents IA</a>
                <a href="/voice" class="app-link">🎤 Système Vocal Avancé</a>
            </div>

            <div class="app-category">
                <div class="category-title">🎨 Génération IA</div>
                <a href="/video" class="app-link">🎥 Générateur Vidéo LTX</a>
                <a href="/youtube" class="app-link">🎬 Laboratoire YouTube</a>
                <a href="/generation" class="app-link">🎨 Centre de Génération</a>
                <a href="/music" class="app-link">🎵 Générateur Musical</a>
                <a href="/3d" class="app-link">🧊 Générateur 3D</a>
                <a href="/image" class="app-link">🖼️ Générateur d Images</a>
            </div>

            <div class="app-category">
                <div class="category-title">🧠 Intelligence</div>
                <a href="/brain" class="app-link">🧠 Tableau de Bord Cérébral</a>
                <a href="/qi" class="app-link">🎯 Test QI Avancé</a>
                <a href="/claude" class="app-link">🤖 Configuration Claude</a>
            </div>

            <div class="app-category">
                <div class="category-title">📊 Monitoring</div>
                <a href="/monitoring" class="app-link">📊 Monitoring Cérébral</a>
                <a href="/thermal" class="app-link">🔥 Mémoire Thermique</a>
                <a href="/thermal-dashboard" class="app-link">🌡️ Dashboard Thermique</a>
                <a href="/brain-3d" class="app-link">🧠 Cerveau 3D Live</a>
                <a href="/brain-viz" class="app-link">🧠 Visualisation 3D</a>
            </div>

            <div class="app-category">
                <div class="category-title">⚙️ Système</div>
                <a href="/kyber" class="app-link">⚡ Dashboard KYBER</a>
                <a href="/editor" class="app-link">💻 Éditeur Code Avancé</a>
                <a href="/accelerators" class="app-link">🚀 Accélérateurs</a>
            </div>

            <div class="app-category">
                <div class="category-title">🌐 Web & Sécurité</div>
                <a href="/search" class="app-link">🔍 Recherche Web IA</a>
                <a href="/face" class="app-link">👁️ Reconnaissance Faciale</a>
                <a href="/security" class="app-link">🔐 Centre de Sécurité</a>
                <a href="/emergency" class="app-link">🚨 Contrôle d Urgence</a>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <h3>🎯 Toutes les applications sont connectées et fonctionnelles</h3>
            <p><strong>Serveur actif :</strong> http://localhost:3001</p>
        </div>
    </div>
</body>
</html>
