<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Monitoring Système - Louna AI v3.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 2px solid rgba(33, 150, 243, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #2196f3;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(33, 150, 243, 0.5);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .metric-card.critical {
            border-color: #ff4444;
            background: linear-gradient(135deg, rgba(255, 68, 68, 0.2), rgba(200, 0, 0, 0.1));
            animation: pulseRed 2s infinite;
        }

        .metric-card.warning {
            border-color: #ffc107;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.1));
        }

        .metric-card.good {
            border-color: #00ff00;
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1));
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .metric-icon {
            font-size: 2.5rem;
            color: #2196f3;
        }

        .metric-icon.critical {
            color: #ff4444;
            animation: shake 0.5s infinite;
        }

        .metric-icon.warning {
            color: #ffc107;
        }

        .metric-icon.good {
            color: #00ff00;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2196f3;
        }

        .metric-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .metric-details {
            display: grid;
            gap: 8px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.8);
        }

        .detail-value {
            font-weight: bold;
            color: #2196f3;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2196f3, #21cbf3);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.critical {
            background: linear-gradient(90deg, #ff4444, #ff6b6b);
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #ffc107, #ffeb3b);
        }

        .progress-fill.good {
            background: linear-gradient(90deg, #00ff00, #4caf50);
        }

        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-placeholder {
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
        }

        .alerts-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #2196f3;
        }

        .alert-item.critical {
            border-left-color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
        }

        .alert-item.warning {
            border-left-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }

        .alert-item.info {
            border-left-color: #2196f3;
            background: rgba(33, 150, 243, 0.1);
        }

        .alert-icon {
            font-size: 1.5rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .alert-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .alert-time {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        @keyframes pulseRed {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.3); }
            50% { box-shadow: 0 0 40px rgba(255, 68, 68, 0.6); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }

        .status-warning {
            background: #ffc107;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }

        .status-offline {
            background: #ff4444;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
        }
    </style>
</head>
<body>
    <a href="/chat" class="back-btn">
        <i class="fas fa-comments"></i>
        <span>Chat</span>
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> Monitoring Système</h1>
            <p>Surveillance en temps réel de Louna AI v3.0</p>
        </div>

        <div class="metrics-grid">
            <!-- CPU -->
            <div class="metric-card good" id="cpuCard">
                <div class="metric-header">
                    <div class="metric-icon good">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="metric-value" id="cpuValue">23%</div>
                </div>
                <div class="metric-title">Processeur</div>
                <div class="progress-bar">
                    <div class="progress-fill good" id="cpuProgress" style="width: 23%"></div>
                </div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Cœurs actifs:</span>
                        <span class="detail-value" id="cpuCores">6/8</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Température:</span>
                        <span class="detail-value" id="cpuTemp">45°C</span>
                    </div>
                </div>
            </div>

            <!-- Mémoire -->
            <div class="metric-card warning" id="memoryCard">
                <div class="metric-header">
                    <div class="metric-icon warning">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="metric-value" id="memoryValue">67%</div>
                </div>
                <div class="metric-title">Mémoire RAM</div>
                <div class="progress-bar">
                    <div class="progress-fill warning" id="memoryProgress" style="width: 67%"></div>
                </div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Utilisée:</span>
                        <span class="detail-value" id="memoryUsed">10.7 GB</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Disponible:</span>
                        <span class="detail-value" id="memoryAvailable">5.3 GB</span>
                    </div>
                </div>
            </div>

            <!-- Stockage -->
            <div class="metric-card good" id="storageCard">
                <div class="metric-header">
                    <div class="metric-icon good">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="metric-value" id="storageValue">34%</div>
                </div>
                <div class="metric-title">Stockage</div>
                <div class="progress-bar">
                    <div class="progress-fill good" id="storageProgress" style="width: 34%"></div>
                </div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Utilisé:</span>
                        <span class="detail-value" id="storageUsed">340 GB</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Libre:</span>
                        <span class="detail-value" id="storageFree">660 GB</span>
                    </div>
                </div>
            </div>

            <!-- Réseau -->
            <div class="metric-card good" id="networkCard">
                <div class="metric-header">
                    <div class="metric-icon good">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="metric-value" id="networkValue">89 Mbps</div>
                </div>
                <div class="metric-title">Réseau</div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Download:</span>
                        <span class="detail-value" id="networkDown">89.3 Mbps</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Upload:</span>
                        <span class="detail-value" id="networkUp">12.7 Mbps</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Latence:</span>
                        <span class="detail-value" id="networkLatency">12 ms</span>
                    </div>
                </div>
            </div>

            <!-- Agents IA -->
            <div class="metric-card good" id="agentsCard">
                <div class="metric-header">
                    <div class="metric-icon good">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="metric-value" id="agentsValue">2/3</div>
                </div>
                <div class="metric-title">Agents IA</div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Louna Principal:</span>
                        <span class="detail-value"><span class="status-indicator status-online"></span>Actif</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">DeepSeek:</span>
                        <span class="detail-value"><span class="status-indicator status-online"></span>Formation</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Claude:</span>
                        <span class="detail-value"><span class="status-indicator status-offline"></span>Hors ligne</span>
                    </div>
                </div>
            </div>

            <!-- Sécurité -->
            <div class="metric-card good" id="securityCard">
                <div class="metric-header">
                    <div class="metric-icon good">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="metric-value" id="securityValue">98%</div>
                </div>
                <div class="metric-title">Sécurité</div>
                <div class="metric-details">
                    <div class="detail-item">
                        <span class="detail-label">Firewall:</span>
                        <span class="detail-value"><span class="status-indicator status-online"></span>Actif</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">VPN:</span>
                        <span class="detail-value"><span class="status-indicator status-online"></span>Connecté</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Chiffrement:</span>
                        <span class="detail-value"><span class="status-indicator status-online"></span>AES-256</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="alerts-section">
            <h3 style="color: #2196f3; margin-bottom: 20px;">
                <i class="fas fa-exclamation-triangle"></i> Alertes Système
            </h3>
            <div id="alertsContainer">
                <div class="alert-item warning">
                    <div class="alert-icon" style="color: #ffc107;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Utilisation mémoire élevée</div>
                        <div class="alert-description">La mémoire RAM atteint 67% d'utilisation</div>
                        <div class="alert-time">Il y a 5 minutes</div>
                    </div>
                </div>
                
                <div class="alert-item info">
                    <div class="alert-icon" style="color: #2196f3;">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Agent DeepSeek en formation</div>
                        <div class="alert-description">Session d'apprentissage automatique en cours</div>
                        <div class="alert-time">Il y a 12 minutes</div>
                    </div>
                </div>
                
                <div class="alert-item info">
                    <div class="alert-icon" style="color: #00ff00;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Sauvegarde automatique réussie</div>
                        <div class="alert-description">Mémoire thermique sauvegardée avec succès</div>
                        <div class="alert-time">Il y a 18 minutes</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulation des données en temps réel
        function updateMetrics() {
            // CPU
            const cpuUsage = Math.random() * 40 + 10; // 10-50%
            document.getElementById('cpuValue').textContent = Math.round(cpuUsage) + '%';
            document.getElementById('cpuProgress').style.width = cpuUsage + '%';
            
            // Mémoire
            const memoryUsage = Math.random() * 30 + 50; // 50-80%
            document.getElementById('memoryValue').textContent = Math.round(memoryUsage) + '%';
            document.getElementById('memoryProgress').style.width = memoryUsage + '%';
            
            // Mise à jour des couleurs selon les seuils
            updateCardStatus('cpuCard', cpuUsage, [70, 90]);
            updateCardStatus('memoryCard', memoryUsage, [70, 85]);
            
            // Réseau
            const networkSpeed = Math.random() * 20 + 80; // 80-100 Mbps
            document.getElementById('networkValue').textContent = Math.round(networkSpeed) + ' Mbps';
            document.getElementById('networkDown').textContent = Math.round(networkSpeed) + ' Mbps';
            document.getElementById('networkUp').textContent = Math.round(networkSpeed * 0.15) + ' Mbps';
            
            // Latence
            const latency = Math.random() * 10 + 8; // 8-18 ms
            document.getElementById('networkLatency').textContent = Math.round(latency) + ' ms';
        }

        function updateCardStatus(cardId, value, thresholds) {
            const card = document.getElementById(cardId);
            const icon = card.querySelector('.metric-icon');
            const progress = card.querySelector('.progress-fill');
            
            card.classList.remove('good', 'warning', 'critical');
            icon.classList.remove('good', 'warning', 'critical');
            progress.classList.remove('good', 'warning', 'critical');
            
            if (value >= thresholds[1]) {
                card.classList.add('critical');
                icon.classList.add('critical');
                progress.classList.add('critical');
            } else if (value >= thresholds[0]) {
                card.classList.add('warning');
                icon.classList.add('warning');
                progress.classList.add('warning');
            } else {
                card.classList.add('good');
                icon.classList.add('good');
                progress.classList.add('good');
            }
        }

        function addAlert(type, title, description) {
            const container = document.getElementById('alertsContainer');
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item ${type}`;
            
            const iconClass = type === 'critical' ? 'fas fa-times-circle' : 
                             type === 'warning' ? 'fas fa-exclamation-triangle' : 
                             'fas fa-info-circle';
            
            const iconColor = type === 'critical' ? '#ff4444' : 
                             type === 'warning' ? '#ffc107' : 
                             '#2196f3';
            
            alertItem.innerHTML = `
                <div class="alert-icon" style="color: ${iconColor};">
                    <i class="${iconClass}"></i>
                </div>
                <div class="alert-content">
                    <div class="alert-title">${title}</div>
                    <div class="alert-description">${description}</div>
                    <div class="alert-time">À l'instant</div>
                </div>
            `;
            
            container.insertBefore(alertItem, container.firstChild);
            
            // Limiter à 10 alertes
            const alerts = container.querySelectorAll('.alert-item');
            if (alerts.length > 10) {
                alerts[alerts.length - 1].remove();
            }
        }

        // Simulation d'alertes aléatoires
        function simulateAlerts() {
            const alerts = [
                { type: 'info', title: 'Optimisation automatique', description: 'Accélérateurs KYBER optimisés' },
                { type: 'info', title: 'Nouvelle connexion', description: 'Utilisateur connecté au chat' },
                { type: 'warning', title: 'Température élevée', description: 'CPU à 52°C' },
                { type: 'info', title: 'Sauvegarde programmée', description: 'Sauvegarde mémoire thermique' }
            ];
            
            if (Math.random() < 0.3) { // 30% de chance
                const randomAlert = alerts[Math.floor(Math.random() * alerts.length)];
                addAlert(randomAlert.type, randomAlert.title, randomAlert.description);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 Monitoring système initialisé');
            
            // Mise à jour toutes les 3 secondes
            setInterval(updateMetrics, 3000);
            
            // Alertes aléatoires toutes les 10 secondes
            setInterval(simulateAlerts, 10000);
            
            // Première mise à jour
            updateMetrics();
        });
    </script>
</body>
</html>
