<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accélérateurs Kyber - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #ff6b35;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-accueil {
            background: #e91e63;
            color: white;
        }

        .btn-chat {
            background: #9c27b0;
            color: white;
        }

        .btn-memoire {
            background: #3f51b5;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #ff6b35;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }

        .accelerators-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .accelerator-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .accelerator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .accelerator-name {
            font-size: 16px;
            font-weight: 600;
        }

        .accelerator-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #4caf50;
            color: white;
        }

        .status-veille {
            background: #ff9800;
            color: white;
        }

        .accelerator-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .metric-label {
            font-size: 12px;
            opacity: 0.7;
        }

        .performance-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b35, #f7931e);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .performance-text {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .controls-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .controls-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .control-btn {
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-boost {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }

        .btn-optimiser {
            background: linear-gradient(135deg, #00bcd4, #00acc1);
            color: white;
        }

        .btn-reinitialiser {
            background: linear-gradient(135deg, #f44336, #e53935);
            color: white;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bolt"></i> Accélérateurs Kyber</h1>
            <div class="header-buttons">
                <button class="header-btn btn-accueil" onclick="window.location.href='/'">
                    <i class="fas fa-home"></i> Accueil
                </button>
                <button class="header-btn btn-chat" onclick="window.location.href='/chat'">
                    <i class="fas fa-comments"></i> Chat
                </button>
                <button class="header-btn btn-memoire" onclick="window.location.href='/thermal-memory-dashboard.html'">
                    <i class="fas fa-brain"></i> Mémoire
                </button>
            </div>
        </div>

        <!-- Statistiques globales -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-bolt"></i></div>
                <div class="stat-number" id="total-accelerators">0</div>
                <div class="stat-label">Accélérateurs Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                <div class="stat-number" id="boost-total">245%</div>
                <div class="stat-label">Boost Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-fire"></i></div>
                <div class="stat-number" id="consumption">1.8kW</div>
                <div class="stat-label">Consommation</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="stat-number" id="efficiency">96%</div>
                <div class="stat-label">Efficacité</div>
            </div>
        </div>

        <!-- Grille des accélérateurs -->
        <div class="accelerators-grid" id="accelerators-grid">
            <!-- Les accélérateurs seront générés ici -->
        </div>

        <!-- Contrôles système -->
        <div class="controls-section">
            <div class="controls-title">Contrôles Système</div>
            <div class="controls-buttons">
                <button class="control-btn btn-boost" onclick="boostMaximum()">
                    <i class="fas fa-rocket"></i> Boost Maximum
                </button>
                <button class="control-btn btn-optimiser" onclick="optimiser()">
                    <i class="fas fa-cogs"></i> Optimiser
                </button>
                <button class="control-btn btn-reinitialiser" onclick="reinitialiser()">
                    <i class="fas fa-redo"></i> Réinitialiser
                </button>
            </div>
        </div>
    </div>

    <script>
        // Données des accélérateurs Kyber
        const acceleratorsData = [
            {
                id: 'quantum_booster',
                name: 'Quantum Booster',
                status: 'ACTIF',
                boost: 45,
                efficiency: 98.65,
                performance: 98.46
            },
            {
                id: 'neural_enhancer',
                name: 'Neural Enhancer',
                status: 'ACTIF',
                boost: 38,
                efficiency: 95.56,
                performance: 96.56
            },
            {
                id: 'memory_accelerator',
                name: 'Memory Accelerator',
                status: 'ACTIF',
                boost: 42,
                efficiency: 94.81,
                performance: 94.81
            },
            {
                id: 'processing_amplifier',
                name: 'Processing Amplifier',
                status: 'ACTIF',
                boost: 35,
                efficiency: 93.16,
                performance: 93.16
            },
            {
                id: 'thermal_optimizer',
                name: 'Thermal Optimizer',
                status: 'VEILLE',
                boost: 28,
                efficiency: 91,
                performance: 91
            },
            {
                id: 'qi_enhancer',
                name: 'QI Enhancer',
                status: 'ACTIF',
                boost: 52,
                efficiency: 100,
                performance: 100
            },
            {
                id: 'response_accelerator',
                name: 'Response Accelerator',
                status: 'ACTIF',
                boost: 33,
                efficiency: 95.51,
                performance: 95.51
            },
            {
                id: 'learning_booster',
                name: 'Learning Booster',
                status: 'VEILLE',
                boost: 29,
                efficiency: 92,
                performance: 92
            }
        ];

        function formatNumber(num) {
            return Number(num).toFixed(2);
        }

        function renderAccelerators() {
            const grid = document.getElementById('accelerators-grid');
            
            grid.innerHTML = acceleratorsData.map(acc => `
                <div class="accelerator-card">
                    <div class="accelerator-header">
                        <div class="accelerator-name">${acc.name}</div>
                        <div class="accelerator-status ${acc.status === 'ACTIF' ? 'status-active' : 'status-veille'}">
                            ${acc.status}
                        </div>
                    </div>
                    <div class="accelerator-metrics">
                        <div class="metric">
                            <div class="metric-value">${acc.boost}%</div>
                            <div class="metric-label">Boost</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${formatNumber(acc.efficiency)}%</div>
                            <div class="metric-label">Efficacité</div>
                        </div>
                    </div>
                    <div class="performance-bar">
                        <div class="performance-fill" style="width: ${acc.performance}%"></div>
                    </div>
                    <div class="performance-text">Performance ${formatNumber(acc.performance)}%</div>
                </div>
            `).join('');
        }

        function updateStats() {
            const activeCount = acceleratorsData.filter(acc => acc.status === 'ACTIF').length;
            const totalBoost = acceleratorsData
                .filter(acc => acc.status === 'ACTIF')
                .reduce((sum, acc) => sum + acc.boost, 0);
            
            document.getElementById('total-accelerators').textContent = activeCount; console.log("Accélérateurs actifs:", activeCount);
            document.getElementById('boost-total').textContent = totalBoost + '%';
            
            // Simulation des autres métriques
            const consumption = (activeCount * 0.3).toFixed(1);
            const efficiency = Math.round(95 + Math.random() * 5);
            
            document.getElementById('consumption').textContent = consumption + 'kW';
            document.getElementById('efficiency').textContent = efficiency + '%';
        }

        function boostMaximum() {
            acceleratorsData.forEach(acc => {
                acc.status = 'ACTIF';
                acc.boost = Math.min(acc.boost + 10, 100);
                acc.efficiency = Math.min(acc.efficiency + 2, 100);
                acc.performance = Math.min(acc.performance + 2, 100);
            });
            renderAccelerators();
            updateStats();
        }

        function optimiser() {
            acceleratorsData.forEach(acc => {
                if (acc.status === 'ACTIF') {
                    acc.efficiency = Math.min(acc.efficiency + 1, 100);
                    acc.performance = Math.min(acc.performance + 1, 100);
                }
            });
            renderAccelerators();
            updateStats();
        }

        function reinitialiser() {
            location.reload();
        }

        // Mise à jour automatique
        function autoUpdate() {
            acceleratorsData.forEach(acc => {
                if (acc.status === 'ACTIF') {
                    // Petites variations aléatoires
                    acc.efficiency += (Math.random() - 0.5) * 0.1;
                    acc.performance += (Math.random() - 0.5) * 0.1;
                    
                    // Garder dans les limites
                    acc.efficiency = Math.max(90, Math.min(100, acc.efficiency));
                    acc.performance = Math.max(90, Math.min(100, acc.performance));
                }
            });
            renderAccelerators();
            updateStats();
        }

        // Initialisation
        renderAccelerators();
        updateStats();

        // Mise à jour toutes les 3 secondes
        setInterval(autoUpdate, 3000);
    </script>
    <!-- Script de mise à jour automatique du QI -->
    <script src="/js/auto-update-qi.js"></script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
