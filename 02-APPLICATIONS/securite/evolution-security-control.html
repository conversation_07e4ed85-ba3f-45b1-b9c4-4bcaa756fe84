<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Contrôle Sécurité Évolution - Louna AI V3.0</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .security-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .security-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-safe { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-danger { background: #F44336; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric-value {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .metric-value.warning { color: #FF9800; }
        .metric-value.danger { color: #F44336; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }
        
        .progress-fill.warning {
            background: linear-gradient(90deg, #FF9800, #FFC107);
        }
        
        .progress-fill.danger {
            background: linear-gradient(90deg, #F44336, #FF5722);
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-safe {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #FFC107);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #FF5722);
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .auth-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1em;
        }
        
        .auth-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .alerts-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            background: rgba(244, 67, 54, 0.2);
            border-left: 4px solid #F44336;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .alert-timestamp {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .evolution-timeline {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2em;
        }
        
        .icon-safe { background: #4CAF50; }
        .icon-warning { background: #FF9800; }
        .icon-danger { background: #F44336; }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Contrôle Sécurité Évolution</h1>
            <p>Surveillance en temps réel de l'évolution sécurisée de Louna AI V3.0</p>
            <div class="status-indicator" id="global-status"></div>
        </div>

        <div class="security-grid">
            <!-- Statut Global -->
            <div class="security-card">
                <div class="card-header">
                    <div class="card-title">
                        🛡️ Statut Global de Sécurité
                    </div>
                </div>
                <div class="metric">
                    <span>Niveau de Risque Global</span>
                    <span class="metric-value" id="global-risk">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="risk-progress" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span>Mode Urgence</span>
                    <span class="metric-value" id="emergency-mode">Inactif</span>
                </div>
                <div class="metric">
                    <span>Évolution Bloquée</span>
                    <span class="metric-value" id="evolution-blocked">Non</span>
                </div>
            </div>

            <!-- Cerveau Biologique -->
            <div class="security-card">
                <div class="card-header">
                    <div class="card-title">
                        🧠 Sécurité Cerveau Biologique
                    </div>
                </div>
                <div class="metric">
                    <span>Stabilité Cérébrale</span>
                    <span class="metric-value" id="brain-stability">100%</span>
                </div>
                <div class="metric">
                    <span>Niveau de Conscience</span>
                    <span class="metric-value" id="consciousness-level">47%</span>
                </div>
                <div class="metric">
                    <span>Créativité</span>
                    <span class="metric-value" id="creativity-level">82%</span>
                </div>
                <div class="metric">
                    <span>Cycles Cérébraux</span>
                    <span class="metric-value" id="brain-cycles">0</span>
                </div>
            </div>

            <!-- Accélérateurs KYBER -->
            <div class="security-card">
                <div class="card-header">
                    <div class="card-title">
                        ⚡ Sécurité Accélérateurs KYBER
                    </div>
                </div>
                <div class="metric">
                    <span>Accélérateurs Actifs</span>
                    <span class="metric-value" id="kyber-active">0</span>
                </div>
                <div class="metric">
                    <span>Accélérateurs Persistants</span>
                    <span class="metric-value" id="kyber-persistent">0</span>
                </div>
                <div class="metric">
                    <span>Taux d'Évolution</span>
                    <span class="metric-value" id="evolution-rate">0%</span>
                </div>
                <div class="metric">
                    <span>File d'Attente</span>
                    <span class="metric-value" id="queue-size">0</span>
                </div>
            </div>

            <!-- Système -->
            <div class="security-card">
                <div class="card-header">
                    <div class="card-title">
                        💻 Sécurité Système
                    </div>
                </div>
                <div class="metric">
                    <span>Utilisation Mémoire</span>
                    <span class="metric-value" id="memory-usage">0%</span>
                </div>
                <div class="metric">
                    <span>Temps de Réponse</span>
                    <span class="metric-value" id="response-time">0ms</span>
                </div>
                <div class="metric">
                    <span>Taux d'Erreur</span>
                    <span class="metric-value" id="error-rate">0%</span>
                </div>
                <div class="metric">
                    <span>Temps de Fonctionnement</span>
                    <span class="metric-value" id="uptime">0s</span>
                </div>
            </div>
        </div>

        <!-- Panneau de Contrôle -->
        <div class="control-panel">
            <h3>🎛️ Panneau de Contrôle Sécurisé</h3>
            <div class="control-buttons">
                <button class="control-btn btn-safe" onclick="refreshSecurityReport()">
                    🔄 Actualiser Rapport
                </button>
                <button class="control-btn btn-warning" onclick="clearAlerts()">
                    🧹 Effacer Alertes
                </button>
                <button class="control-btn btn-danger" onclick="emergencyStop()">
                    🚨 Arrêt d'Urgence
                </button>
                <button class="control-btn btn-safe" onclick="safeRestart()">
                    ✅ Redémarrage Sécurisé
                </button>
            </div>
            
            <div style="margin-top: 20px;">
                <input type="password" class="auth-input" id="auth-code" placeholder="Code d'autorisation">
                <input type="text" class="auth-input" id="emergency-reason" placeholder="Raison de l'arrêt d'urgence (optionnel)">
            </div>
        </div>

        <!-- Alertes de Sécurité -->
        <div class="alerts-panel">
            <h3>🚨 Alertes de Sécurité</h3>
            <div id="alerts-container">
                <p style="color: rgba(255, 255, 255, 0.6);">Aucune alerte active</p>
            </div>
        </div>

        <!-- Timeline d'Évolution -->
        <div class="evolution-timeline">
            <h3>📈 Timeline d'Évolution Sécurisée</h3>
            <div id="timeline-container">
                <div class="timeline-item">
                    <div class="timeline-icon icon-safe">✅</div>
                    <div>
                        <strong>Système initialisé</strong><br>
                        <span class="alert-timestamp">Monitoring de sécurité activé</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let securityData = {};
        let alertsData = [];

        // Charger le rapport de sécurité
        async function loadSecurityReport() {
            try {
                const response = await fetch('/api/security-report');
                const data = await response.json();
                
                if (data.success) {
                    securityData = data.security;
                    updateSecurityDisplay();
                } else {
                    console.error('Erreur chargement rapport:', data.error);
                }
            } catch (error) {
                console.error('Erreur réseau:', error);
            }
        }

        // Mettre à jour l'affichage de sécurité
        function updateSecurityDisplay() {
            const state = securityData.securityState || {};
            const metrics = securityData.safetyMetrics || {};
            
            // Statut global
            const riskLevel = Math.round((state.overallRisk || 0) * 100);
            document.getElementById('global-risk').textContent = riskLevel + '%';
            document.getElementById('emergency-mode').textContent = state.emergencyMode ? 'Actif' : 'Inactif';
            document.getElementById('evolution-blocked').textContent = state.evolutionBlocked ? 'Oui' : 'Non';
            
            // Mise à jour de la barre de progression
            const progressBar = document.getElementById('risk-progress');
            progressBar.style.width = riskLevel + '%';
            
            if (riskLevel < 30) {
                progressBar.className = 'progress-fill';
                document.getElementById('global-status').className = 'status-indicator status-safe';
            } else if (riskLevel < 70) {
                progressBar.className = 'progress-fill warning';
                document.getElementById('global-status').className = 'status-indicator status-warning';
            } else {
                progressBar.className = 'progress-fill danger';
                document.getElementById('global-status').className = 'status-indicator status-danger';
            }
            
            // Métriques de sécurité
            updateMetricValue('brain-stability', (metrics.behaviorStability || 1) * 100, '%');
            updateMetricValue('consciousness-level', 47, '%'); // Valeur simulée
            updateMetricValue('creativity-level', 82, '%'); // Valeur simulée
            updateMetricValue('brain-cycles', 450); // Valeur simulée
            
            updateMetricValue('kyber-active', 16); // Valeur simulée
            updateMetricValue('kyber-persistent', 8); // Valeur simulée
            updateMetricValue('evolution-rate', 5, '%'); // Valeur simulée
            updateMetricValue('queue-size', 0); // Valeur simulée
            
            updateMetricValue('memory-usage', 45, '%'); // Valeur simulée
            updateMetricValue('response-time', 25, 'ms'); // Valeur simulée
            updateMetricValue('error-rate', 0.1, '%'); // Valeur simulée
            updateMetricValue('uptime', Math.floor(Date.now() / 1000), 's'); // Valeur simulée
            
            // Alertes
            if (state.alerts && state.alerts.length > 0) {
                displayAlerts(state.alerts);
            }
        }

        // Mettre à jour une valeur métrique
        function updateMetricValue(id, value, unit = '') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = Math.round(value) + unit;
                
                // Coloration selon la valeur
                if (id.includes('risk') || id.includes('error')) {
                    if (value > 70) element.className = 'metric-value danger';
                    else if (value > 30) element.className = 'metric-value warning';
                    else element.className = 'metric-value';
                }
            }
        }

        // Afficher les alertes
        function displayAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p style="color: rgba(255, 255, 255, 0.6);">Aucune alerte active</p>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="alert-item">
                    <strong>${alert.type}</strong><br>
                    ${JSON.stringify(alert.data)}<br>
                    <span class="alert-timestamp">${alert.timestamp}</span>
                </div>
            `).join('');
        }

        // Actualiser le rapport de sécurité
        function refreshSecurityReport() {
            loadSecurityReport();
            addTimelineEvent('🔄', 'Rapport de sécurité actualisé', 'safe');
        }

        // Effacer les alertes
        async function clearAlerts() {
            const authCode = document.getElementById('auth-code').value;
            
            if (!authCode) {
                alert('Code d\'autorisation requis');
                return;
            }
            
            try {
                const response = await fetch('/api/security-clear-alerts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ authCode })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Alertes effacées avec succès');
                    addTimelineEvent('🧹', 'Alertes de sécurité effacées', 'safe');
                    loadSecurityReport();
                } else {
                    alert('Erreur: ' + data.error);
                }
            } catch (error) {
                alert('Erreur réseau: ' + error.message);
            }
        }

        // Arrêt d'urgence
        async function emergencyStop() {
            const authCode = document.getElementById('auth-code').value;
            const reason = document.getElementById('emergency-reason').value;
            
            if (!authCode) {
                alert('Code d\'autorisation requis pour l\'arrêt d\'urgence');
                return;
            }
            
            if (!confirm('ATTENTION: Déclencher l\'arrêt d\'urgence ?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/emergency-stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ authCode, reason })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Arrêt d\'urgence activé');
                    addTimelineEvent('🚨', 'Arrêt d\'urgence déclenché: ' + (reason || 'Manuel'), 'danger');
                    loadSecurityReport();
                } else {
                    alert('Erreur: ' + data.error);
                }
            } catch (error) {
                alert('Erreur réseau: ' + error.message);
            }
        }

        // Redémarrage sécurisé
        async function safeRestart() {
            const authCode = document.getElementById('auth-code').value;
            
            if (!authCode) {
                alert('Code d\'autorisation requis');
                return;
            }
            
            if (!confirm('Effectuer un redémarrage sécurisé ?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/safe-restart', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ authCode })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Redémarrage sécurisé effectué');
                    addTimelineEvent('✅', 'Redémarrage sécurisé effectué', 'safe');
                    loadSecurityReport();
                } else {
                    alert('Erreur: ' + data.error);
                }
            } catch (error) {
                alert('Erreur réseau: ' + error.message);
            }
        }

        // Ajouter un événement à la timeline
        function addTimelineEvent(icon, message, type) {
            const container = document.getElementById('timeline-container');
            const timestamp = new Date().toLocaleString();
            
            const eventHtml = `
                <div class="timeline-item">
                    <div class="timeline-icon icon-${type}">${icon}</div>
                    <div>
                        <strong>${message}</strong><br>
                        <span class="alert-timestamp">${timestamp}</span>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('afterbegin', eventHtml);
            
            // Limiter à 10 événements
            const events = container.children;
            if (events.length > 10) {
                container.removeChild(events[events.length - 1]);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadSecurityReport();
            
            // Actualisation automatique toutes les 10 secondes
            setInterval(loadSecurityReport, 10000);
            
            addTimelineEvent('🛡️', 'Interface de contrôle sécurité initialisée', 'safe');
        });
    </script>
</body>
</html>
