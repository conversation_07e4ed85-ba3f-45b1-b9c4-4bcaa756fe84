<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centre de Sécurité Avancée - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header-icon {
            font-size: 32px;
            color: #ffffff;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .security-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .security-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .security-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #ff69b4;
        }

        .card-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .status-inactive {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        .status-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .security-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: #4ecdc4;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            border-radius: 4px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .threat-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .threat-item {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            color: #ffc107;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .threat-icon {
            font-size: 16px;
        }

        .logs-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #4ecdc4;
            padding-left: 10px;
        }

        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }

        .log-level-info { border-left-color: #4ecdc4; }
        .log-level-warning { border-left-color: #ffc107; }
        .log-level-error { border-left-color: #f44336; }

        @media (max-width: 768px) {
            .security-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-shield-alt header-icon"></i>
            Centre de Sécurité Avancée
        </h1>
        <p>Protection complète et monitoring en temps réel</p>

        <div class="nav-buttons">
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
            <a href="/chat" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/brain-monitoring-complete.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Monitoring
            </a>
            <a href="/generation-center.html" class="nav-btn">
                <i class="fas fa-magic"></i>
                Génération
            </a>
        </div>
    </div>

    <div class="container">
        <div class="security-grid" id="security-grid">
            <!-- Les cartes de sécurité seront générées ici -->
        </div>
    </div>

    <script>
        // Données de sécurité simulées
        let securityData = {
            firewall: {
                status: 'active',
                blocked: 1247,
                allowed: 8934,
                efficiency: 98.7
            },
            antivirus: {
                status: 'active',
                scanned: 15678,
                threats: 23,
                quarantine: 5,
                efficiency: 99.2
            },
            encryption: {
                status: 'active',
                level: 'AES-256',
                files: 2847,
                strength: 100
            },
            vpn: {
                status: 'active',
                location: 'Guadeloupe, FR',
                speed: 89.3,
                latency: 12
            },
            biometric: {
                status: 'inactive',
                enrolled: 0,
                attempts: 0,
                success: 0
            },
            monitoring: {
                status: 'active',
                events: 456,
                alerts: 3,
                uptime: 99.8
            }
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            renderSecurityInterface();
            startRealTimeUpdates();
            console.log('🔐 Centre de sécurité initialisé');
        });

        // Rendu de l'interface de sécurité
        function renderSecurityInterface() {
            const grid = document.getElementById('security-grid');

            grid.innerHTML = `
                ${createFirewallCard()}
                ${createAntivirusCard()}
                ${createEncryptionCard()}
                ${createVPNCard()}
                ${createBiometricCard()}
                ${createMonitoringCard()}
            `;
        }

        // Carte Firewall
        function createFirewallCard() {
            const fw = securityData.firewall;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-fire card-icon"></i>
                            Firewall Intelligent
                        </div>
                        <div class="status-badge ${fw.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${fw.status === 'active' ? 'Actif' : 'Inactif'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Connexions bloquées</span>
                        <span class="metric-value">${fw.blocked.toLocaleString()}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Connexions autorisées</span>
                        <span class="metric-value">${fw.allowed.toLocaleString()}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${fw.efficiency}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #4ecdc4;">
                        Efficacité: ${fw.efficiency}%
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="configureFirewall()">
                            <i class="fas fa-cog"></i>
                            Configurer
                        </button>
                        <button class="action-btn btn-secondary" onclick="viewFirewallLogs()">
                            <i class="fas fa-list"></i>
                            Logs
                        </button>
                    </div>
                </div>
            `;
        }

        // Carte Antivirus
        function createAntivirusCard() {
            const av = securityData.antivirus;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-virus-slash card-icon"></i>
                            Protection Antivirus
                        </div>
                        <div class="status-badge ${av.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${av.status === 'active' ? 'Actif' : 'Inactif'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Fichiers analysés</span>
                        <span class="metric-value">${av.scanned.toLocaleString()}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Menaces détectées</span>
                        <span class="metric-value">${av.threats}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">En quarantaine</span>
                        <span class="metric-value">${av.quarantine}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${av.efficiency}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #4ecdc4;">
                        Protection: ${av.efficiency}%
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="scanSystem()">
                            <i class="fas fa-search"></i>
                            Scanner
                        </button>
                        <button class="action-btn btn-secondary" onclick="updateDefinitions()">
                            <i class="fas fa-download"></i>
                            Mettre à jour
                        </button>
                    </div>
                </div>
            `;
        }

        // Carte Chiffrement
        function createEncryptionCard() {
            const enc = securityData.encryption;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-lock card-icon"></i>
                            Chiffrement End-to-End
                        </div>
                        <div class="status-badge ${enc.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${enc.status === 'active' ? 'Actif' : 'Inactif'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Niveau de chiffrement</span>
                        <span class="metric-value">${enc.level}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Fichiers protégés</span>
                        <span class="metric-value">${enc.files.toLocaleString()}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${enc.strength}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #4ecdc4;">
                        Force: ${enc.strength}%
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="manageKeys()">
                            <i class="fas fa-key"></i>
                            Clés
                        </button>
                        <button class="action-btn btn-secondary" onclick="encryptFiles()">
                            <i class="fas fa-shield-alt"></i>
                            Chiffrer
                        </button>
                    </div>
                </div>
            `;
        }

        // Carte VPN
        function createVPNCard() {
            const vpn = securityData.vpn;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-globe card-icon"></i>
                            Réseau Privé Virtuel
                        </div>
                        <div class="status-badge ${vpn.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${vpn.status === 'active' ? 'Connecté' : 'Déconnecté'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Localisation</span>
                        <span class="metric-value">${vpn.location}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Vitesse</span>
                        <span class="metric-value">${vpn.speed} Mbps</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Latence</span>
                        <span class="metric-value">${vpn.latency} ms</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${vpn.speed}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #4ecdc4;">
                        Performance: ${vpn.speed}%
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="changeLocation()">
                            <i class="fas fa-map-marker-alt"></i>
                            Localisation
                        </button>
                        <button class="action-btn btn-secondary" onclick="testSpeed()">
                            <i class="fas fa-tachometer-alt"></i>
                            Test vitesse
                        </button>
                    </div>
                </div>
            `;
        }

        // Carte Biométrie
        function createBiometricCard() {
            const bio = securityData.biometric;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-fingerprint card-icon"></i>
                            Authentification Biométrique
                        </div>
                        <div class="status-badge ${bio.status === 'active' ? 'status-active' : 'status-warning'}">
                            ${bio.status === 'active' ? 'Configuré' : 'À configurer'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Utilisateurs enregistrés</span>
                        <span class="metric-value">${bio.enrolled}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Tentatives d'accès</span>
                        <span class="metric-value">${bio.attempts}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Taux de succès</span>
                        <span class="metric-value">${bio.success}%</span>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="setupBiometric()">
                            <i class="fas fa-plus"></i>
                            Configurer
                        </button>
                        <button class="action-btn btn-secondary" onclick="testBiometric()">
                            <i class="fas fa-hand-paper"></i>
                            Tester
                        </button>
                    </div>
                </div>
            `;
        }

        // Carte Monitoring
        function createMonitoringCard() {
            const mon = securityData.monitoring;
            return `
                <div class="security-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-eye card-icon"></i>
                            Monitoring Sécurité
                        </div>
                        <div class="status-badge ${mon.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${mon.status === 'active' ? 'Surveillé' : 'Arrêté'}
                        </div>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Événements détectés</span>
                        <span class="metric-value">${mon.events}</span>
                    </div>

                    <div class="security-metric">
                        <span class="metric-label">Alertes actives</span>
                        <span class="metric-value">${mon.alerts}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${mon.uptime}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #4ecdc4;">
                        Disponibilité: ${mon.uptime}%
                    </div>

                    <div class="threat-list">
                        <div class="threat-item">
                            <i class="fas fa-exclamation-triangle threat-icon"></i>
                            Tentative d'accès non autorisé détectée
                        </div>
                        <div class="threat-item">
                            <i class="fas fa-bug threat-icon"></i>
                            Activité suspecte sur le port 443
                        </div>
                        <div class="threat-item">
                            <i class="fas fa-shield-alt threat-icon"></i>
                            Mise à jour de sécurité disponible
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-primary" onclick="viewAlerts()">
                            <i class="fas fa-bell"></i>
                            Alertes
                        </button>
                        <button class="action-btn btn-secondary" onclick="generateReport()">
                            <i class="fas fa-file-alt"></i>
                            Rapport
                        </button>
                    </div>
                </div>
            `;
        }

        // Fonctions d'interaction
        function startRealTimeUpdates() {
            setInterval(() => {
                updateSecurityData();
                renderSecurityInterface();
            }, 5000);
        }

        function updateSecurityData() {
            // Simulation de mises à jour en temps réel
            securityData.firewall.blocked += Math.floor(Math.random() * 10);
            securityData.firewall.allowed += Math.floor(Math.random() * 50);
            securityData.antivirus.scanned += Math.floor(Math.random() * 100);
            securityData.monitoring.events += Math.floor(Math.random() * 5);

            // Variations légères des métriques
            securityData.firewall.efficiency = Math.min(100, 95 + Math.random() * 5);
            securityData.antivirus.efficiency = Math.min(100, 97 + Math.random() * 3);
            securityData.vpn.speed = Math.min(100, 85 + Math.random() * 15);
            securityData.vpn.latency = Math.max(5, 10 + Math.random() * 10);
        }

        // Actions Firewall
        function configureFirewall() {
            alert('🔥 Configuration du Firewall\n\nOuverture du panneau de configuration...');
            console.log('🔥 Configuration Firewall demandée');
        }

        function viewFirewallLogs() {
            alert('📋 Logs du Firewall\n\nAffichage des derniers événements...');
            console.log('📋 Consultation logs Firewall');
        }

        // Actions Antivirus
        function scanSystem() {
            alert('🔍 Analyse du Système\n\nDémarrage de l\'analyse complète...');
            console.log('🔍 Analyse système démarrée');
        }

        function updateDefinitions() {
            alert('📥 Mise à jour des Définitions\n\nTéléchargement des dernières signatures...');
            console.log('📥 Mise à jour définitions antivirus');
        }

        // Actions Chiffrement
        function manageKeys() {
            alert('🔑 Gestion des Clés\n\nOuverture du gestionnaire de clés...');
            console.log('🔑 Gestion des clés de chiffrement');
        }

        function encryptFiles() {
            alert('🛡️ Chiffrement de Fichiers\n\nSélection des fichiers à chiffrer...');
            console.log('🛡️ Chiffrement de fichiers');
        }

        // Actions VPN
        function changeLocation() {
            const locations = ['Paris, FR', 'Londres, UK', 'New York, US', 'Tokyo, JP', 'Sydney, AU'];
            const newLocation = locations[Math.floor(Math.random() * locations.length)];
            securityData.vpn.location = newLocation;
            alert(`🌍 Changement de Localisation\n\nNouvelle localisation: ${newLocation}`);
            renderSecurityInterface();
        }

        function testSpeed() {
            alert('⚡ Test de Vitesse\n\nMesure de la vitesse de connexion...');
            console.log('⚡ Test de vitesse VPN');
        }

        // Actions Biométrie
        function setupBiometric() {
            alert('👆 Configuration Biométrique\n\nDémarrage de l\'enregistrement...');
            console.log('👆 Configuration authentification biométrique');
        }

        function testBiometric() {
            alert('✋ Test Biométrique\n\nPlacez votre doigt sur le capteur...');
            console.log('✋ Test authentification biométrique');
        }

        // Actions Monitoring
        function viewAlerts() {
            alert('🚨 Alertes de Sécurité\n\n• Tentative d\'accès non autorisé\n• Activité suspecte détectée\n• Mise à jour disponible');
            console.log('🚨 Consultation des alertes');
        }

        function generateReport() {
            alert('📊 Génération de Rapport\n\nCréation du rapport de sécurité...');
            console.log('📊 Génération rapport de sécurité');
        }

        // Mise à jour de la configuration globale
        if (window.LOUNA_CONFIG) {
            window.LOUNA_CONFIG.security = {
                firewall: true,
                antivirus: true,
                encryption: true,
                vpn: true,
                biometric: false,
                monitoring: true,
                level: 95.8
            };
        }

        console.log('🔐 Centre de sécurité avancée chargé');
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
