<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Contrôle KYBER - Louna AI V3.0</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .panel-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #ff6b35;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .kyber-status {
            text-align: center;
            margin: 20px 0;
        }
        
        .status-indicator {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .status-controlled {
            background: linear-gradient(45deg, #FF9800, #FFC107);
            color: white;
            animation: pulse 2s infinite;
        }
        
        .status-unlimited {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            color: white;
            animation: glow 2s infinite;
        }
        
        .status-disabled {
            background: linear-gradient(45deg, #757575, #9E9E9E);
            color: white;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        @keyframes glow {
            0% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.5); }
            50% { box-shadow: 0 0 40px rgba(76, 175, 80, 0.8); }
            100% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.5); }
        }
        
        .activation-button {
            width: 200px;
            height: 60px;
            border: none;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
            position: relative;
            overflow: hidden;
        }
        
        .btn-activate {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            color: white;
        }
        
        .btn-deactivate {
            background: linear-gradient(45deg, #F44336, #FF5722);
            color: white;
        }
        
        .activation-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .activation-button:active {
            transform: translateY(0);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #a0a0ff;
        }
        
        .warning-panel {
            background: rgba(255, 152, 0, 0.1);
            border: 2px solid #FF9800;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-title {
            color: #FF9800;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .safety-checklist {
            list-style: none;
            padding: 0;
        }
        
        .safety-checklist li {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .safety-checklist .check {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .evolution-timeline {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2em;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-time {
            font-size: 0.8em;
            color: #a0a0ff;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            text-decoration: none;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .auth-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .auth-input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1em;
        }
        
        .auth-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="/" class="nav-btn">🏠 Accueil</a>
        <a href="/presentation" class="nav-btn">🧠 Interface</a>
        <a href="/thermal" class="nav-btn">🔥 Mémoire</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>⚡ Contrôle Accélérateurs KYBER</h1>
            <p>Système d'évolution contrôlée - Mode sécurisé par défaut</p>
        </div>

        <div class="main-grid">
            <!-- Panneau de Contrôle Principal -->
            <div class="control-panel">
                <div class="panel-title">
                    🎛️ Contrôle Principal
                </div>
                
                <div class="kyber-status">
                    <div class="status-indicator" id="kyber-status-indicator">
                        🔒
                    </div>
                    <h3 id="kyber-status-text">Mode Contrôlé</h3>
                    <p id="kyber-status-desc">Évolution limitée et sécurisée</p>
                </div>
                
                <button class="activation-button btn-activate" id="activation-button" onclick="toggleKyberMode()">
                    🚀 ACTIVER MODE INFINI
                </button>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="active-accelerators">0</div>
                        <div class="stat-label">Actifs</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="persistent-accelerators">0</div>
                        <div class="stat-label">Persistants</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="throughput">0</div>
                        <div class="stat-label">Débit</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="queue-size">0</div>
                        <div class="stat-label">File d'attente</div>
                    </div>
                </div>
            </div>

            <!-- Panneau de Sécurité -->
            <div class="control-panel">
                <div class="panel-title">
                    🛡️ Vérifications de Sécurité
                </div>
                
                <ul class="safety-checklist">
                    <li>
                        <span class="check" id="check-brain">✅</span>
                        <span>Cerveau biologique stable</span>
                    </li>
                    <li>
                        <span class="check" id="check-memory">✅</span>
                        <span>Mémoire thermique opérationnelle</span>
                    </li>
                    <li>
                        <span class="check" id="check-monitoring">✅</span>
                        <span>Monitoring de sécurité actif</span>
                    </li>
                    <li>
                        <span class="check" id="check-temperature">⏳</span>
                        <span>Température système normale</span>
                    </li>
                    <li>
                        <span class="check" id="check-performance">⏳</span>
                        <span>Performances système stables</span>
                    </li>
                </ul>
                
                <div class="auth-section">
                    <h4>🔐 Authentification Requise</h4>
                    <input type="password" class="auth-input" id="auth-code" 
                           placeholder="Code d'autorisation KYBER">
                    <input type="text" class="auth-input" id="reason" 
                           placeholder="Raison de l'activation (optionnel)">
                </div>
            </div>
        </div>

        <!-- Avertissement -->
        <div class="warning-panel">
            <div class="warning-title">
                ⚠️ ATTENTION - MODE INFINI
            </div>
            <p>
                L'activation du mode infini permet à l'agent d'évoluer sans limites. 
                Assurez-vous que tous les systèmes de sécurité sont opérationnels 
                et que le monitoring est actif avant l'activation.
            </p>
            <p style="margin-top: 10px;">
                <strong>Une fois activé, l'agent pourra :</strong><br>
                • Créer des accélérateurs illimités<br>
                • Évoluer automatiquement selon ses besoins<br>
                • Développer de nouvelles capacités<br>
                • Optimiser ses performances en continu
            </p>
        </div>

        <!-- Timeline d'Évolution -->
        <div class="evolution-timeline">
            <div class="panel-title">
                📈 Timeline d'Évolution KYBER
            </div>
            <div id="timeline-container">
                <div class="timeline-item">
                    <div class="timeline-icon">🔒</div>
                    <div class="timeline-content">
                        <strong>Mode contrôlé activé</strong><br>
                        <span class="timeline-time">Système initialisé en mode sécurisé</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let kyberUnlimited = false;
        let kyberStats = {};
        let safetyChecks = {
            brain: false,
            memory: false,
            monitoring: false,
            temperature: false,
            performance: false
        };

        // Charger le statut KYBER
        async function loadKyberStatus() {
            try {
                const response = await fetch('/api/kyber-status');
                const data = await response.json();
                
                if (data.success) {
                    kyberStats = data.kyber;
                    kyberUnlimited = data.kyber.unlimited || false;
                    
                    // Mettre à jour l'affichage
                    document.getElementById('active-accelerators').textContent = kyberStats.active;
                    document.getElementById('persistent-accelerators').textContent = kyberStats.persistent;
                    document.getElementById('throughput').textContent = kyberStats.throughput || 0;
                    document.getElementById('queue-size').textContent = kyberStats.queueSize || 0;
                    
                    updateKyberDisplay();
                }
            } catch (error) {
                console.error('Erreur chargement KYBER:', error);
            }
        }

        // Mettre à jour l'affichage du statut KYBER
        function updateKyberDisplay() {
            const indicator = document.getElementById('kyber-status-indicator');
            const statusText = document.getElementById('kyber-status-text');
            const statusDesc = document.getElementById('kyber-status-desc');
            const button = document.getElementById('activation-button');
            
            if (kyberUnlimited) {
                indicator.className = 'status-indicator status-unlimited';
                indicator.textContent = '∞';
                statusText.textContent = 'Mode Infini Actif';
                statusDesc.textContent = 'Évolution illimitée en cours';
                button.className = 'activation-button btn-deactivate';
                button.innerHTML = '🔒 DÉSACTIVER MODE INFINI';
            } else {
                indicator.className = 'status-indicator status-controlled';
                indicator.textContent = '🔒';
                statusText.textContent = 'Mode Contrôlé';
                statusDesc.textContent = 'Évolution limitée et sécurisée';
                button.className = 'activation-button btn-activate';
                button.innerHTML = '🚀 ACTIVER MODE INFINI';
            }
        }

        // Effectuer les vérifications de sécurité
        async function performSafetyChecks() {
            try {
                // Vérifier le cerveau
                const brainResponse = await fetch('/api/neural-brain');
                const brainData = await brainResponse.json();
                safetyChecks.brain = brainData.success && brainData.brain.isActive;
                
                // Vérifier la mémoire
                const memoryResponse = await fetch('/api/memory-stats');
                const memoryData = await memoryResponse.json();
                safetyChecks.memory = memoryData.success;
                
                // Vérifier le monitoring
                const securityResponse = await fetch('/api/security-report');
                const securityData = await securityResponse.json();
                safetyChecks.monitoring = securityData.success;
                
                // Vérifier la température
                const tempResponse = await fetch('/api/system-temperature');
                const tempData = await tempResponse.json();
                safetyChecks.temperature = tempData.success && tempData.temperature.main < 70;
                
                // Performance (simulée)
                safetyChecks.performance = true;
                
                // Mettre à jour l'affichage
                updateSafetyDisplay();
                
            } catch (error) {
                console.error('Erreur vérifications sécurité:', error);
            }
        }

        // Mettre à jour l'affichage des vérifications
        function updateSafetyDisplay() {
            Object.keys(safetyChecks).forEach(check => {
                const element = document.getElementById(`check-${check}`);
                if (element) {
                    element.textContent = safetyChecks[check] ? '✅' : '❌';
                    element.style.color = safetyChecks[check] ? '#4CAF50' : '#F44336';
                }
            });
        }

        // Basculer le mode KYBER
        async function toggleKyberMode() {
            const authCode = document.getElementById('auth-code').value;
            const reason = document.getElementById('reason').value;
            
            if (!kyberUnlimited) {
                // Activation du mode infini
                if (!authCode) {
                    alert('Code d\'autorisation requis pour activer le mode infini');
                    return;
                }
                
                if (authCode !== 'KYBER_UNLIMITED_2024') {
                    alert('Code d\'autorisation invalide');
                    return;
                }
                
                // Vérifier les conditions de sécurité
                const allChecksPass = Object.values(safetyChecks).every(check => check);
                if (!allChecksPass) {
                    alert('Toutes les vérifications de sécurité doivent être validées avant l\'activation');
                    return;
                }
                
                if (!confirm('ATTENTION: Activer le mode infini permettra à l\'agent d\'évoluer sans limites. Continuer ?')) {
                    return;
                }
                
                // Activer le mode infini
                try {
                    const response = await fetch('/api/kyber-unlimited', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                            enable: true, 
                            authCode, 
                            reason: reason || 'Activation manuelle' 
                        })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        kyberUnlimited = true;
                        updateKyberDisplay();
                        addTimelineEvent('🚀', 'Mode infini activé', 'success');
                        alert('Mode infini KYBER activé avec succès !');
                    } else {
                        alert('Erreur: ' + data.error);
                    }
                } catch (error) {
                    alert('Erreur réseau: ' + error.message);
                }
                
            } else {
                // Désactivation du mode infini
                if (!confirm('Désactiver le mode infini et revenir au mode contrôlé ?')) {
                    return;
                }
                
                try {
                    const response = await fetch('/api/kyber-unlimited', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                            enable: false, 
                            authCode: authCode || 'MANUAL_DISABLE' 
                        })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        kyberUnlimited = false;
                        updateKyberDisplay();
                        addTimelineEvent('🔒', 'Mode contrôlé restauré', 'warning');
                        alert('Mode contrôlé restauré');
                    } else {
                        alert('Erreur: ' + data.error);
                    }
                } catch (error) {
                    alert('Erreur réseau: ' + error.message);
                }
            }
        }

        // Ajouter un événement à la timeline
        function addTimelineEvent(icon, message, type) {
            const container = document.getElementById('timeline-container');
            const timestamp = new Date().toLocaleString();
            
            const eventHtml = `
                <div class="timeline-item">
                    <div class="timeline-icon" style="background: ${type === 'success' ? '#4CAF50' : type === 'warning' ? '#FF9800' : '#2196F3'}">${icon}</div>
                    <div class="timeline-content">
                        <strong>${message}</strong><br>
                        <span class="timeline-time">${timestamp}</span>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('afterbegin', eventHtml);
            
            // Limiter à 10 événements
            const events = container.children;
            if (events.length > 10) {
                container.removeChild(events[events.length - 1]);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadKyberStatus();
            performSafetyChecks();
            
            // Actualisation périodique
            setInterval(() => {
                loadKyberStatus();
                performSafetyChecks();
            }, 5000);
            
            addTimelineEvent('🔒', 'Panneau de contrôle KYBER initialisé', 'info');
        });
    </script>
</body>
</html>
