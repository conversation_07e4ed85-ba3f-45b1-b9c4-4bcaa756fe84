<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mémoire Thermique - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <link rel="stylesheet" href="/css/louna-thermal.css?v=2025">
    <link rel="stylesheet" href="/css/theme-switcher.css?v=2025">
    <link rel="stylesheet" href="/css/notifications.css?v=2025">
    <link rel="stylesheet" href="/css/native-app.css?v=2025">
    <link rel="stylesheet" href="/css/contrast-fixes.css?v=2025">
    <link rel="stylesheet" href="/css/home-button.css?v=2025">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Principal */
        .thermal-header-main {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .thermal-header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .thermal-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .thermal-title i {
            font-size: 2.5rem;
            color: #ff6b6b;
            animation: pulse 2s infinite;
        }

        .thermal-title h1 {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .thermal-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-left: 10px;
        }

        .thermal-nav {
            display: flex;
            gap: 15px;
        }

        .thermal-nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thermal-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .thermal-nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        .thermal-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 25px;
            color: #4caf50;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        /* Container Principal */
        .thermal-main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .thermal-container {
            padding: 15px;
            margin-top: 10px;
        }

        .thermal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .thermal-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .thermal-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .thermal-card h2 {
            color: #ff69b4;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .thermal-card h2 i {
            color: #ffa726;
        }

        .thermal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .thermal-header h3 {
            color: #ff69b4;
            margin: 0;
            font-size: 18px;
        }

        .thermal-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .thermal-status.active {
            background: rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .thermal-status.inactive {
            background: rgba(158, 158, 158, 0.3);
            color: #9e9e9e;
        }

        .memory-zones {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .memory-zone {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-zone:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .zone-temp {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .zone-temp.hot { color: #ff6b6b; }
        .zone-temp.warm { color: #ffa726; }
        .zone-temp.medium { color: #66bb6a; }
        .zone-temp.cool { color: #42a5f5; }

        .zone-label {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 5px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .zone-count {
            font-size: 16px;
            color: #ffffff;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #ffffff;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .memory-entries {
            max-height: 300px;
            overflow-y: auto;
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 3px solid;
            transition: all 0.3s ease;
        }

        .memory-entry:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(3px);
        }

        .memory-entry.hot { border-left-color: #ff6b6b; }
        .memory-entry.warm { border-left-color: #ffa726; }
        .memory-entry.medium { border-left-color: #66bb6a; }
        .memory-entry.cool { border-left-color: #42a5f5; }

        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .entry-type {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .entry-temp {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }

        .entry-content {
            font-size: 15px;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.5;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        }

        .entry-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .loading-indicator.hidden {
            display: none;
        }

        .thermal-visualization {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .temp-gauge {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            position: relative;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                #42a5f5 0deg 90deg,
                #66bb6a 90deg 180deg,
                #ffa726 180deg 270deg,
                #ff6b6b 270deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .temp-gauge::before {
            content: '';
            width: 160px;
            height: 160px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            position: absolute;
        }

        .temp-value {
            font-size: 32px;
            font-weight: bold;
            color: #ff69b4;
            z-index: 1;
        }

        .temp-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 10px;
        }

        /* Styles pour le transfert d'informations */
        .transfer-section {
            margin-top: 15px;
        }

        .transfer-input textarea {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            margin-bottom: 15px;
        }

        .transfer-input textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .transfer-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .transfer-controls select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px 15px;
            color: white;
            font-family: inherit;
            flex: 1;
        }

        .transfer-controls select option {
            background: #1a1a2e;
            color: white;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .thermal-grid {
                grid-template-columns: 1fr;
            }

            .thermal-nav {
                flex-direction: column;
                gap: 10px;
            }

            .thermal-header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>


</head>
<body>
    <!-- Boutons de statut en haut -->
    <div class="status-buttons-container">
        <a href="#" class="agent-status-button" id="agent-status-btn">
            <i class="fas fa-brain"></i>
            <span>AGENT ÉVEILLÉ</span>
        </a>
        <a href="#" class="corrections-status-button" id="corrections-status-btn">
            <i class="fas fa-check-circle"></i>
            <span>TOUTES ERREURS CORRIGÉES</span>
        </a>
    </div>
    <!-- Header Principal Unique -->
    <div class="thermal-header-main">
        <div class="thermal-header-content">
            <div class="thermal-title">
                <i class="fas fa-fire"></i>
                <h1>Mémoire Thermique</h1>
                <span class="thermal-subtitle">Système de mémoire biologique - QI 235</span>
            </div>
            <div class="thermal-nav">
                <a href="/" class="thermal-nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/dashboard-master.html" class="thermal-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    Tableau de Bord
                </a>
                <a href="/chat" class="thermal-nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
            </div>
            <div class="thermal-status">
                <div class="status-indicator active"></div>
                <span>Mémoire Active</span>
            </div>
        </div>
    </div>

    <!-- Container Principal -->
    <div class="thermal-main-container">

        <!-- Indicateur de chargement -->
        <div class="loading-indicator" id="loading-indicator">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Chargement des données de mémoire thermique...</p>
        </div>

        <!-- Grille principale -->
        <div class="thermal-grid" id="thermal-grid" style="display: none;">
            <!-- Visualisation thermique -->
            <div class="thermal-card">
                <h2><i class="fas fa-thermometer-half"></i> Température Globale</h2>
                <div class="thermal-visualization">
                    <div class="temp-gauge">
                        <div class="temp-value" id="global-temp">0.65</div>
                    </div>
                    <div class="temp-label">Température moyenne du système</div>
                </div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="refreshTemperature()">
                        <i class="fas fa-sync"></i> Actualiser
                    </button>
                </div>
            </div>

            <!-- Zones de mémoire -->
            <div class="thermal-card">
                <h2><i class="fas fa-layer-group"></i> Zones de Mémoire</h2>
                <div class="memory-zones" id="memory-zones">
                    <!-- Les zones seront générées dynamiquement -->
                </div>
            </div>

            <!-- Statistiques -->
            <div class="thermal-card">
                <h2><i class="fas fa-chart-bar"></i> Statistiques</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-memories">0</div>
                        <div class="stat-label">Mémoires totales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="hot-memories">0</div>
                        <div class="stat-label">Mémoires chaudes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="cycles-count">0</div>
                        <div class="stat-label">Cycles effectués</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Mémoires récentes -->
            <div class="thermal-card">
                <h2><i class="fas fa-history"></i> Mémoires Récentes</h2>
                <div class="memory-entries" id="memory-entries">
                    <!-- Les entrées seront générées dynamiquement -->
                </div>
            </div>

            <!-- Transfert d'informations -->
            <div class="thermal-card">
                <h2><i class="fas fa-exchange-alt"></i> Transfert d'Informations</h2>
                <div class="transfer-section">
                    <div class="transfer-input">
                        <textarea id="transfer-text" placeholder="Entrez les informations à transférer vers la mémoire thermique..." rows="4"></textarea>
                        <div class="transfer-controls">
                            <select id="transfer-zone">
                                <option value="hot">Zone Chaude</option>
                                <option value="warm">Zone Tiède</option>
                                <option value="medium">Zone Moyenne</option>
                                <option value="cool">Zone Froide</option>
                            </select>
                            <button class="control-btn" onclick="transferInformation()">
                                <i class="fas fa-upload"></i> Transférer
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contrôles de mémoire -->
            <div class="thermal-card">
                <h2><i class="fas fa-cogs"></i> Contrôles de Mémoire</h2>
                <div class="control-buttons">
                    <button class="control-btn" id="cycle-btn" onclick="performMemoryCycle()">
                        <i class="fas fa-sync"></i> Cycle de Mémoire
                    </button>
                    <button class="control-btn" id="optimize-btn" onclick="optimizeMemory()">
                        <i class="fas fa-magic"></i> Optimiser
                    </button>
                    <button class="control-btn" id="consolidate-btn" onclick="consolidateMemory()">
                        <i class="fas fa-compress"></i> Consolider
                    </button>
                    <button class="control-btn" id="analyze-btn" onclick="analyzeMemory()">
                        <i class="fas fa-search"></i> Analyser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let thermalData = null;
        let updateInterval = null;
        let isSystemActive = true;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 Initialisation de la mémoire thermique...');

            // Démarrer le chargement des données
            setTimeout(() => {
                loadThermalData();
            }, 1000);

            // Démarrer la mise à jour automatique
            updateInterval = setInterval(loadThermalData, 10000); // Toutes les 10 secondes

            console.log('✅ Mémoire thermique initialisée');
        });

        // Charger les données thermiques
        async function loadThermalData() {
            const loadingIndicator = document.getElementById('loading-indicator');
            const thermalGrid = document.getElementById('thermal-grid');

            try {
                // Afficher l'indicateur de chargement
                loadingIndicator.style.display = 'block';
                thermalGrid.style.display = 'none';

                // Essayer de charger les vraies données
                let data;
                try {
                    const response = await fetch('/api/thermal-memory/stats');
                    if (response.ok) {
                        data = await response.json();
                        console.log('✅ Données thermiques chargées:', data);
                    } else {
                        throw new Error('API non disponible');
                    }
                } catch (apiError) {
                    console.warn('⚠️ API non disponible, utilisation de données simulées');
                    data = generateSimulatedData();
                }

                // Mettre à jour l'interface
                updateThermalInterface(data);

                // Masquer le chargement et afficher les données
                loadingIndicator.style.display = 'none';
                thermalGrid.style.display = 'grid';

                thermalData = data;

            } catch (error) {
                console.error('❌ Erreur chargement données thermiques:', error);
                loadingIndicator.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Erreur de chargement. Utilisation de données simulées.</p>
                `;

                // Utiliser des données simulées en cas d'erreur
                setTimeout(() => {
                    const simulatedData = generateSimulatedData();
                    updateThermalInterface(simulatedData);
                    loadingIndicator.style.display = 'none';
                    thermalGrid.style.display = 'grid';
                }, 2000);
            }
        }

        // Générer des données simulées
        function generateSimulatedData() {
            return {
                temperature: 0.65 + (Math.random() * 0.3 - 0.15), // 0.5 à 0.8
                totalMemories: 100 + Math.floor(Math.random() * 50),
                hotMemories: 15 + Math.floor(Math.random() * 10),
                cyclesPerformed: 450 + Math.floor(Math.random() * 100),
                efficiency: 85 + Math.floor(Math.random() * 15),
                zones: [
                    { name: 'Zone Chaude', temp: 0.8 + Math.random() * 0.2, count: 15 + Math.floor(Math.random() * 10), type: 'hot' },
                    { name: 'Zone Tiède', temp: 0.6 + Math.random() * 0.2, count: 25 + Math.floor(Math.random() * 15), type: 'warm' },
                    { name: 'Zone Moyenne', temp: 0.4 + Math.random() * 0.2, count: 30 + Math.floor(Math.random() * 20), type: 'medium' },
                    { name: 'Zone Froide', temp: 0.2 + Math.random() * 0.2, count: 20 + Math.floor(Math.random() * 15), type: 'cool' },
                    { name: 'Zone Glacée', temp: 0.1 + Math.random() * 0.1, count: 10 + Math.floor(Math.random() * 10), type: 'cool' }
                ],
                recentMemories: [
                    { content: 'Conversation avec Jean-Luc sur le QI 235', type: 'conversation', temp: 0.9, time: '2 min' },
                    { content: 'Optimisation des accélérateurs KYBER', type: 'system', temp: 0.7, time: '5 min' },
                    { content: 'Génération d\'image artistique', type: 'creation', temp: 0.6, time: '8 min' },
                    { content: 'Analyse des performances système', type: 'analysis', temp: 0.5, time: '12 min' },
                    { content: 'Sauvegarde automatique #454', type: 'backup', temp: 0.3, time: '15 min' }
                ]
            };
        }

        // Mettre à jour l'interface thermique
        function updateThermalInterface(data) {
            // Température globale
            document.getElementById('global-temp').textContent = data.temperature.toFixed(2);

            // Statistiques
            document.getElementById('total-memories').textContent = data.totalMemories;
            document.getElementById('hot-memories').textContent = data.hotMemories;
            document.getElementById('cycles-count').textContent = data.cyclesPerformed;
            document.getElementById('efficiency').textContent = data.efficiency + '%';

            // Zones de mémoire
            updateMemoryZones(data.zones);

            // Mémoires récentes
            updateRecentMemories(data.recentMemories);
        }

        // Mettre à jour les zones de mémoire
        function updateMemoryZones(zones) {
            const zonesContainer = document.getElementById('memory-zones');
            zonesContainer.innerHTML = '';

            zones.forEach(zone => {
                const zoneElement = document.createElement('div');
                zoneElement.className = 'memory-zone';
                zoneElement.innerHTML = `
                    <div class="zone-temp ${zone.type}">${zone.temp.toFixed(2)}</div>
                    <div class="zone-label">${zone.name}</div>
                    <div class="zone-count">${zone.count} mémoires</div>
                `;
                zonesContainer.appendChild(zoneElement);
            });
        }

        // Mettre à jour les mémoires récentes
        function updateRecentMemories(memories) {
            const entriesContainer = document.getElementById('memory-entries');
            entriesContainer.innerHTML = '';

            memories.forEach(memory => {
                const tempClass = memory.temp > 0.7 ? 'hot' : memory.temp > 0.5 ? 'warm' : memory.temp > 0.3 ? 'medium' : 'cool';

                const entryElement = document.createElement('div');
                entryElement.className = `memory-entry ${tempClass}`;
                entryElement.innerHTML = `
                    <div class="entry-header">
                        <span class="entry-type">${memory.type}</span>
                        <span class="entry-temp ${tempClass}">${memory.temp.toFixed(2)}</span>
                    </div>
                    <div class="entry-content">${memory.content}</div>
                    <div class="entry-footer">
                        <span>Il y a ${memory.time}</span>
                        <span>Température: ${memory.temp.toFixed(2)}</span>
                    </div>
                `;
                entriesContainer.appendChild(entryElement);
            });
        }

        // Fonctions de contrôle
        async function performMemoryCycle() {
            showNotification('🔄 Cycle de mémoire en cours...', 'info');

            try {
                const response = await fetch('/api/thermal-memory/cycle', { method: 'POST' });
                if (response.ok) {
                    showNotification('✅ Cycle de mémoire terminé !', 'success');
                    loadThermalData(); // Recharger les données
                } else {
                    showNotification('⚠️ Cycle simulé (API non disponible)', 'warning');
                }
            } catch (error) {
                showNotification('⚠️ Cycle simulé (API non disponible)', 'warning');
            }
        }

        async function optimizeMemory() {
            showNotification('⚡ Optimisation en cours...', 'info');

            try {
                const response = await fetch('/api/thermal-memory/optimize', { method: 'POST' });
                if (response.ok) {
                    showNotification('✅ Mémoire optimisée !', 'success');
                    loadThermalData();
                } else {
                    showNotification('⚠️ Optimisation simulée', 'warning');
                }
            } catch (error) {
                showNotification('⚠️ Optimisation simulée', 'warning');
            }
        }

        async function consolidateMemory() {
            showNotification('🗜️ Consolidation en cours...', 'info');

            try {
                const response = await fetch('/api/thermal-memory/consolidate', { method: 'POST' });
                if (response.ok) {
                    showNotification('✅ Mémoire consolidée !', 'success');
                    loadThermalData();
                } else {
                    showNotification('⚠️ Consolidation simulée', 'warning');
                }
            } catch (error) {
                showNotification('⚠️ Consolidation simulée', 'warning');
            }
        }

        async function analyzeMemory() {
            showNotification('🔍 Analyse en cours...', 'info');

            try {
                const response = await fetch('/api/thermal-memory/analyze', { method: 'POST' });
                if (response.ok) {
                    const data = await response.json();
                    showNotification('✅ Analyse terminée !', 'success');
                    console.log('Résultats d\'analyse:', data);
                } else {
                    showNotification('⚠️ Analyse simulée', 'warning');
                }
            } catch (error) {
                showNotification('⚠️ Analyse simulée', 'warning');
            }
        }

        // Transfert d'informations
        async function transferInformation() {
            const text = document.getElementById('transfer-text').value.trim();
            const zone = document.getElementById('transfer-zone').value;

            if (!text) {
                showNotification('❌ Veuillez entrer du texte à transférer', 'error');
                return;
            }

            showNotification('📤 Transfert en cours...', 'info');

            try {
                const response = await fetch('/api/thermal-memory/add', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        content: text,
                        zone: zone,
                        source: 'manual_transfer'
                    })
                });

                if (response.ok) {
                    showNotification('✅ Information transférée !', 'success');
                    document.getElementById('transfer-text').value = '';
                    loadThermalData(); // Recharger les données
                } else {
                    showNotification('⚠️ Transfert simulé (API non disponible)', 'warning');
                    document.getElementById('transfer-text').value = '';
                }
            } catch (error) {
                showNotification('⚠️ Transfert simulé (API non disponible)', 'warning');
                document.getElementById('transfer-text').value = '';
            }
        }

        // Actualiser la température
        function refreshTemperature() {
            showNotification('🌡️ Actualisation de la température...', 'info');
            loadThermalData();
        }

        // Système de notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: ${type === 'warning' ? '#000' : '#fff'};
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- Scripts de navigation -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <script src="js/native-app.js"></script>

    <script>
        // Gestion des boutons de statut
        document.addEventListener("DOMContentLoaded", function() {
            const agentBtn = document.getElementById("agent-status-btn");
            const correctionsBtn = document.getElementById("corrections-status-btn");
            
            // Mise à jour dynamique du statut
            function updateStatus() {
                // Récupérer les données de statut depuis l'API
                fetch("/api/specialized-accelerators/status")
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.accelerators) {
                            const activeCount = data.accelerators.filter(acc => acc.status === "active").length;
                            agentBtn.querySelector("span").textContent = `AGENT ÉVEILLÉ (${activeCount} actifs)`;
                        }
                    })
                    .catch(err => console.log("Statut non disponible"));
            }
            
            // Mettre à jour le statut toutes les 5 secondes
            updateStatus();
            setInterval(updateStatus, 5000);
            
            // Effet de clic
            agentBtn.addEventListener("click", function(e) {
                e.preventDefault();
                this.style.transform = "scale(0.95)";
                setTimeout(() => this.style.transform = "", 150);
            });
            
            correctionsBtn.addEventListener("click", function(e) {
                e.preventDefault();
                this.style.transform = "scale(0.95)";
                setTimeout(() => this.style.transform = "", 150);
            });
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
