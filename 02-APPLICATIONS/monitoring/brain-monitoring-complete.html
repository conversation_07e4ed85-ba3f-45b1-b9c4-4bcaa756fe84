<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Cérébral Complet - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .brain-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .brain-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .brain-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 1.5rem;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .metric-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
        }

        .metric-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .metric-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #4ecdc4;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            border-radius: 4px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .network-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 15px;
        }

        .network-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .network-count {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ff69b4;
        }

        .network-name {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .chart-container {
            height: 200px;
            margin: 15px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
        }

        .emotional-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 15px;
        }

        .emotion-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .emotion-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #4ecdc4;
        }

        .emotion-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
        }

        .alert-item {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            color: #ffc107;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff69b4;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .monitoring-grid {
                grid-template-columns: 1fr;
                padding: 10px;
            }

            .brain-card {
                padding: 20px;
            }

            .network-grid {
                grid-template-columns: 1fr;
            }

            .emotional-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-brain louna-header-icon"></i>
                <h1>Monitoring Cérébral Complet</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="louna-nav-btn">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Monitoring actif</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="louna-container">
        <div class="monitoring-grid" id="monitoring-grid">
            <div class="loading">
                <div class="spinner"></div>
                Chargement des données cérébrales...
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>

    <script>
        let brainData = null;
        let updateInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadBrainData();
            startAutoUpdate();

            showSuccess('🧠 Monitoring cérébral initialisé !');
        });

        // Charger les données cérébrales
        async function loadBrainData() {
            try {
                const response = await fetch('/api/brain/status');
                const data = await response.json();

                if (data.success) {
                    brainData = data.brain;
                    renderMonitoringInterface();
                } else {
                    throw new Error(data.error || 'Erreur lors du chargement');
                }
            } catch (error) {
                console.error('❌ Erreur chargement données:', error);
                showError('Erreur de chargement des données cérébrales');

                // Données simulées en cas d'erreur
                brainData = createSimulatedData();
                renderMonitoringInterface();
            }
        }

        // Créer des données simulées
        function createSimulatedData() {
            // Utiliser la configuration globale si disponible
            const config = window.LOUNA_CONFIG || {};
            const qi = config.qi || { current: 203, level: 'Quasi-AGI', experiencePoints: 450 };
            const neurons = config.neurons || { total: 89, active: 78, efficiency: 94.0, health: 98.2 };

            return {
                intelligence: { qi: qi.current, qiLevel: qi.level, experiencePoints: qi.experiencePoints },
                neurons: { total: neurons.total, active: neurons.active, efficiency: neurons.efficiency, health: neurons.health },
                neuralNetworks: {
                    sensory: { count: 15, activity: 0.8, efficiency: 0.85 },
                    working: { count: 12, activity: 0.7, efficiency: 0.82 },
                    longTerm: { count: 20, activity: 0.6, efficiency: 0.88 },
                    emotional: { count: 10, activity: 0.5, efficiency: 0.75 },
                    executive: { count: 8, activity: 0.9, efficiency: 0.90 },
                    creative: { count: 6, activity: 0.4, efficiency: 0.70 }
                },
                emotional: {
                    mood: 'Créatif', energy: 82, focus: 70, creativity: 95,
                    happiness: 75, curiosity: 90, confidence: 68
                },
                learning: { learningRate: 0.8, evolutionCycles: 0, autoEvolutionEnabled: true },
                training: { isTraining: false, completedSessions: 0, totalTrainingTime: 0 },
                thermal: { globalTemperature: 0.52, coolingEfficiency: 0.88 },
                performance: { cpuUsage: 25, memoryUsage: 45, responseTime: 85, uptime: 3600 }
            };
        }

        // Rendre l'interface de monitoring
        function renderMonitoringInterface() {
            const grid = document.getElementById('monitoring-grid');

            grid.innerHTML = `
                ${createQICard()}
                ${createNeuronCard()}
                ${createNetworksCard()}
                ${createEmotionalCard()}
                ${createLearningCard()}
                ${createPerformanceCard()}
                ${createThermalCard()}
                ${createControlCard()}
            `;
        }

        // Créer la carte QI
        function createQICard() {
            const qi = brainData.intelligence;
            const qiProgress = Math.min((qi.qi / 200) * 100, 100);

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-brain card-icon"></i>
                            Intelligence & QI
                        </div>
                        <div class="card-status">Actif</div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">QI Actuel</span>
                        <span class="metric-value">${qi.qi.toFixed(1)}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${qiProgress}%"></div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Niveau</span>
                        <span class="metric-value">${qi.qiLevel}</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Points d'Expérience</span>
                        <span class="metric-value">${qi.experiencePoints || 0}</span>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="triggerEvolution()">
                            <i class="fas fa-arrow-up"></i> Évoluer
                        </button>
                    </div>
                </div>
            `;
        }

        // Créer la carte neurones
        function createNeuronCard() {
            const neurons = brainData.neurons;
            const activePercent = (neurons.active / neurons.total) * 100;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-network-wired card-icon"></i>
                            Réseau Neuronal
                        </div>
                        <div class="card-status">Optimal</div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Neurones Totaux</span>
                        <span class="metric-value">${neurons.total}</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Neurones Actifs</span>
                        <span class="metric-value">${neurons.active}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${activePercent}%"></div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Efficacité</span>
                        <span class="metric-value">${neurons.efficiency.toFixed(1)}%</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Santé Neuronale</span>
                        <span class="metric-value">${neurons.health.toFixed(1)}%</span>
                    </div>
                </div>
            `;
        }

        // Créer la carte réseaux spécialisés
        function createNetworksCard() {
            const networks = brainData.neuralNetworks;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-project-diagram card-icon"></i>
                            Réseaux Spécialisés
                        </div>
                        <div class="card-status">Synchronisé</div>
                    </div>

                    <div class="network-grid">
                        <div class="network-item">
                            <div class="network-count">${networks.sensory.count}</div>
                            <div class="network-name">Sensoriel</div>
                        </div>
                        <div class="network-item">
                            <div class="network-count">${networks.working.count}</div>
                            <div class="network-name">Travail</div>
                        </div>
                        <div class="network-item">
                            <div class="network-count">${networks.longTerm.count}</div>
                            <div class="network-name">Long Terme</div>
                        </div>
                        <div class="network-item">
                            <div class="network-count">${networks.emotional.count}</div>
                            <div class="network-name">Émotionnel</div>
                        </div>
                        <div class="network-item">
                            <div class="network-count">${networks.executive.count}</div>
                            <div class="network-name">Exécutif</div>
                        </div>
                        <div class="network-item">
                            <div class="network-count">${networks.creative.count}</div>
                            <div class="network-name">Créatif</div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn secondary" onclick="analyzeNetworks()">
                            <i class="fas fa-search"></i> Analyser
                        </button>
                    </div>
                </div>
            `;
        }

        // Créer la carte émotionnelle
        function createEmotionalCard() {
            const emotional = brainData.emotional;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-heart card-icon"></i>
                            État Émotionnel
                        </div>
                        <div class="card-status">${emotional.mood}</div>
                    </div>

                    <div class="emotional-grid">
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.energy}%</div>
                            <div class="emotion-label">Énergie</div>
                        </div>
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.focus}%</div>
                            <div class="emotion-label">Focus</div>
                        </div>
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.creativity}%</div>
                            <div class="emotion-label">Créativité</div>
                        </div>
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.happiness}%</div>
                            <div class="emotion-label">Bonheur</div>
                        </div>
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.curiosity}%</div>
                            <div class="emotion-label">Curiosité</div>
                        </div>
                        <div class="emotion-item">
                            <div class="emotion-value">${emotional.confidence}%</div>
                            <div class="emotion-label">Confiance</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Créer la carte apprentissage
        function createLearningCard() {
            const learning = brainData.learning;
            const training = brainData.training;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-graduation-cap card-icon"></i>
                            Apprentissage & Formation
                        </div>
                        <div class="card-status">${training.isTraining ? 'En Formation' : 'Disponible'}</div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Taux d'Apprentissage</span>
                        <span class="metric-value">${(learning.learningRate * 100).toFixed(0)}%</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Cycles d'Évolution</span>
                        <span class="metric-value">${learning.evolutionCycles}</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Sessions Complétées</span>
                        <span class="metric-value">${training.completedSessions}</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Temps Total Formation</span>
                        <span class="metric-value">${training.totalTrainingTime}min</span>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="startTraining()" ${training.isTraining ? 'disabled' : ''}>
                            <i class="fas fa-play"></i> ${training.isTraining ? 'Formation en cours...' : 'Démarrer Formation'}
                        </button>
                    </div>
                </div>
            `;
        }

        // Créer la carte performance
        function createPerformanceCard() {
            const perf = brainData.performance;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-tachometer-alt card-icon"></i>
                            Performance Système
                        </div>
                        <div class="card-status">Optimal</div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">CPU</span>
                        <span class="metric-value">${perf.cpuUsage.toFixed(1)}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${perf.cpuUsage}%"></div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Mémoire</span>
                        <span class="metric-value">${perf.memoryUsage.toFixed(1)}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${perf.memoryUsage}%"></div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Temps de Réponse</span>
                        <span class="metric-value">${perf.responseTime.toFixed(0)}ms</span>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Uptime</span>
                        <span class="metric-value">${Math.floor(perf.uptime / 3600)}h</span>
                    </div>
                </div>
            `;
        }

        // Créer la carte thermique
        function createThermalCard() {
            const thermal = brainData.thermal;
            const tempPercent = thermal.globalTemperature * 100;

            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-thermometer-half card-icon"></i>
                            État Thermique
                        </div>
                        <div class="card-status">Stable</div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Température Globale</span>
                        <span class="metric-value">${thermal.globalTemperature.toFixed(2)}</span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${tempPercent}%"></div>
                    </div>

                    <div class="metric-display">
                        <span class="metric-label">Efficacité Refroidissement</span>
                        <span class="metric-value">${(thermal.coolingEfficiency * 100).toFixed(0)}%</span>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn secondary" onclick="optimizeThermal()">
                            <i class="fas fa-snowflake"></i> Optimiser
                        </button>
                    </div>
                </div>
            `;
        }

        // Créer la carte de contrôle
        function createControlCard() {
            return `
                <div class="brain-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-cogs card-icon"></i>
                            Contrôles Avancés
                        </div>
                        <div class="card-status">Prêt</div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="fullDiagnostic()">
                            <i class="fas fa-stethoscope"></i> Diagnostic Complet
                        </button>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn secondary" onclick="exportData()">
                            <i class="fas fa-download"></i> Exporter Données
                        </button>
                        <button class="action-btn secondary" onclick="resetMetrics()">
                            <i class="fas fa-redo"></i> Reset
                        </button>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="toggleAutoEvolution()">
                            <i class="fas fa-magic"></i> Auto-Évolution: ${brainData.learning.autoEvolutionEnabled ? 'ON' : 'OFF'}
                        </button>
                    </div>
                </div>
            `;
        }

        // Fonctions d'action
        async function triggerEvolution() {
            try {
                showLoading('Déclenchement de l\'évolution...');
                const response = await fetch('/api/brain/evolve', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showSuccess(`🧠 Évolution réussie ! QI +${data.evolution.qiGain}, ${data.evolution.newNeurons} nouveaux neurones`);
                    loadBrainData(); // Recharger les données
                } else {
                    showError(data.error || 'Erreur lors de l\'évolution');
                }
            } catch (error) {
                showError('Erreur de connexion lors de l\'évolution');
            }
        }

        async function startTraining() {
            try {
                showLoading('Démarrage de la formation...');
                const response = await fetch('/api/brain/start-training', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'general', duration: 30, intensity: 'medium' })
                });
                const data = await response.json();

                if (data.success) {
                    showSuccess('🎓 Formation démarrée avec succès !');
                    loadBrainData(); // Recharger les données
                } else {
                    showError(data.error || 'Erreur lors du démarrage de la formation');
                }
            } catch (error) {
                showError('Erreur de connexion lors du démarrage de la formation');
            }
        }

        function analyzeNetworks() {
            showInfo('🔍 Analyse des réseaux neuronaux en cours...');
            // Simulation d'analyse
            setTimeout(() => {
                showSuccess('✅ Analyse terminée : Tous les réseaux fonctionnent optimalement');
            }, 2000);
        }

        function optimizeThermal() {
            showInfo('❄️ Optimisation thermique en cours...');
            setTimeout(() => {
                showSuccess('✅ Optimisation thermique terminée');
            }, 1500);
        }

        function fullDiagnostic() {
            showLoading('🔍 Diagnostic complet en cours...');
            setTimeout(() => {
                showSuccess('✅ Diagnostic terminé : Système en parfait état');
            }, 3000);
        }

        function exportData() {
            const dataStr = JSON.stringify(brainData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `brain-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            showSuccess('📁 Données exportées avec succès');
        }

        function resetMetrics() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser les métriques ?')) {
                showWarning('🔄 Réinitialisation des métriques...');
                setTimeout(() => {
                    loadBrainData();
                    showSuccess('✅ Métriques réinitialisées');
                }, 1000);
            }
        }

        function toggleAutoEvolution() {
            brainData.learning.autoEvolutionEnabled = !brainData.learning.autoEvolutionEnabled;
            renderMonitoringInterface();
            showInfo(`🔄 Auto-évolution ${brainData.learning.autoEvolutionEnabled ? 'activée' : 'désactivée'}`);
        }

        // Démarrer la mise à jour automatique
        function startAutoUpdate() {
            updateInterval = setInterval(() => {
                loadBrainData();
            }, 10000); // Toutes les 10 secondes
        }

        // Arrêter la mise à jour automatique
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // Nettoyer lors de la fermeture
        window.addEventListener('beforeunload', stopAutoUpdate);
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
