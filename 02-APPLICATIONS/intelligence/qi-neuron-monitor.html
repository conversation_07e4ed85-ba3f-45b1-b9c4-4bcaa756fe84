<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring QI & Neurones v2.0 - Louna</title>

    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>

    <!-- Styles CSS -->
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <link rel="stylesheet" href="/css/native-app.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <!-- Chart.js pour les graphiques temps réel -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* === STYLES PRINCIPAUX === */
        .monitoring-container {
            padding: 20px;
            margin-top: 60px;
            max-width: 100%;
            width: 100%;
            min-height: calc(100vh - 80px);
        }

        /* === HEADER === */
        .monitoring-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .monitoring-title {
            font-size: 32px;
            color: #ff69b4;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .monitoring-subtitle {
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 20px;
            opacity: 0.9;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 20px;
            color: #4caf50;
            font-weight: 600;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* === NAVIGATION PRINCIPALE === */
        .top-navbar {
            height: 60px;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
        }

        .logo-container {
            display: flex;
            align-items: center;
            margin-right: 30px;
            min-width: 120px;
        }

        .logo-container i {
            font-size: 1.5rem;
            color: #e91e63;
            margin-right: 10px;
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e91e63;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 5px;
            flex: 1;
            overflow-x: auto;
            padding: 0 10px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            color: #ffffff;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 0.9rem;
            min-width: fit-content;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-item:hover {
            background: rgba(255, 105, 180, 0.2);
            color: #ff69b4;
            transform: translateY(-1px);
            border-color: rgba(255, 105, 180, 0.4);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 105, 180, 0.4);
            border-color: rgba(255, 105, 180, 0.6);
        }

        .nav-item i {
            font-size: 1rem;
        }

        .nav-controls {
            display: flex;
            gap: 10px;
            margin-left: 20px;
        }

        .nav-btn {
            background: rgba(255, 105, 180, 0.1);
            border: 1px solid #ff69b4;
            color: #ff69b4;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        /* Responsive navigation */
        @media (max-width: 1200px) {
            .nav-item span {
                display: none;
            }
            .nav-item {
                padding: 8px;
            }
        }

        @media (max-width: 768px) {
            .top-navbar {
                padding: 0 10px;
            }
            .logo-text {
                display: none;
            }
            .nav-controls {
                margin-left: 10px;
            }
        }

        /* === GRILLE DE MÉTRIQUES === */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .metric-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #ff69b4;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .metric-status {
            padding: 4px 12px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 12px;
            color: #4caf50;
            font-size: 12px;
            font-weight: 600;
        }

        /* === MÉTRIQUES PRINCIPALES === */
        .primary-metric {
            text-align: center;
            margin-bottom: 25px;
        }

        .metric-value {
            font-size: 48px;
            font-weight: 700;
            color: #ff69b4;
            display: block;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease;
        }

        .metric-value:hover {
            transform: scale(1.05);
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.6);
        }

        .metric-label {
            font-size: 16px;
            color: #ffffff;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .metric-unit {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        /* === MÉTRIQUES SECONDAIRES === */
        .secondary-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .secondary-metric {
            text-align: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .secondary-metric .label {
            font-size: 14px;
            color: #ffffff;
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .secondary-metric .value {
            font-size: 20px;
            font-weight: 600;
            color: #ff69b4;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* === BARRES DE PROGRESSION === */
        .progress-container {
            margin-top: 20px;
        }

        .progress-label {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 8px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
            border-radius: 5px;
            transition: width 0.8s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
            text-align: center;
        }

        /* === CHARGEMENT === */
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 105, 180, 0.3);
            border-top: 4px solid #ff69b4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #ffffff;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* === CONTRÔLES === */
        .controls-section {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: rgba(255, 105, 180, 0.2);
            border-color: rgba(255, 105, 180, 0.4);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.6);
        }

        /* === GRAPHIQUES TEMPS RÉEL === */
        .charts-section {
            margin-bottom: 30px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #ff69b4;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
        }

        .chart-btn:hover {
            background: rgba(255, 105, 180, 0.2);
            border-color: rgba(255, 105, 180, 0.4);
        }

        .chart-btn.active {
            background: rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.6);
        }

        .chart-canvas-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .chart-canvas {
            max-height: 300px;
        }

        /* === SECTION HISTORIQUE === */
        .history-section {
            margin-bottom: 30px;
        }

        .history-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .history-tab {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .history-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .history-tab:hover::before {
            left: 100%;
        }

        .history-tab:hover {
            background: rgba(255, 105, 180, 0.2);
            border-color: rgba(255, 105, 180, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
        }

        .history-tab.active {
            background: rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.6);
            color: #ff69b4;
            font-weight: 600;
        }

        .history-content {
            display: none;
        }

        .history-content.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .history-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .history-chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .history-chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
        }

        .history-chart-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #ff69b4;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .history-chart-canvas {
            height: 200px;
            width: 100%;
        }

        .history-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .history-stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .history-stat-card:hover {
            background: rgba(255, 105, 180, 0.1);
            border-color: rgba(255, 105, 180, 0.3);
            transform: translateY(-2px);
        }

        .history-stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .history-stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
        }

        .history-stat-trend {
            font-size: 12px;
            margin-top: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .trend-up {
            color: #4caf50;
        }

        .trend-down {
            color: #f44336;
        }

        .trend-stable {
            color: #ffc107;
        }

        /* === RESPONSIVE === */
        @media (max-width: 768px) {
            .monitoring-container {
                padding: 15px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .metric-value {
                font-size: 36px;
            }

            .controls-section {
                flex-direction: column;
                align-items: center;
            }

            .chart-canvas-container {
                height: 250px;
            }

            .chart-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .history-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .history-tabs {
                flex-direction: column;
                align-items: center;
            }

            .history-tab {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .history-stats {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
        }

        /* === ÉTATS === */
        .hidden {
            display: none !important;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-chart-line louna-header-icon"></i>
                <h1>Monitoring QI & Neurones</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="louna-nav-btn">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/brain-monitoring-complete.html" class="louna-nav-btn">
                    <i class="fas fa-brain"></i>
                    <span>Monitoring Complet</span>
                </a>
                <a href="/futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>QI: 120 - Optimal</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="monitoring-container">
        <!-- Header -->
        <div class="monitoring-header">
            <h1 class="monitoring-title">
                <i class="fas fa-brain"></i>
                Monitoring QI & Neurones v2.0
            </h1>
            <p class="monitoring-subtitle">
                Surveillance en temps réel du système cognitif de Jean-Luc Passave
            </p>
            <div class="status-indicator" id="system-status">
                <div class="status-dot"></div>
                <span>Système actif</span>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls-section">
            <button class="control-btn active" id="auto-refresh-btn">
                <i class="fas fa-play"></i>
                <span>Auto-refresh</span>
            </button>
            <button class="control-btn" id="manual-refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </button>
            <button class="control-btn" id="export-btn">
                <i class="fas fa-download"></i>
                <span>Exporter</span>
            </button>
        </div>

        <!-- Indicateur de chargement -->
        <div class="loading-container" id="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">Chargement des données du cerveau artificiel...</div>
        </div>

        <!-- Section des graphiques temps réel -->
        <div class="charts-section hidden" id="charts-section">
            <!-- Graphique QI & Neurones -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        Évolution QI & Neurones en Temps Réel
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" data-timeframe="5m">5min</button>
                        <button class="chart-btn" data-timeframe="15m">15min</button>
                        <button class="chart-btn" data-timeframe="1h">1h</button>
                        <button class="chart-btn" data-timeframe="24h">24h</button>
                    </div>
                </div>
                <div class="chart-canvas-container">
                    <canvas id="qi-neuron-chart" class="chart-canvas"></canvas>
                </div>
            </div>

            <!-- Graphique Réseaux Spécialisés -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-network-wired"></i>
                        Activité des Réseaux Spécialisés
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" data-chart="networks" data-type="bar">Barres</button>
                        <button class="chart-btn" data-chart="networks" data-type="radar">Radar</button>
                        <button class="chart-btn" data-chart="networks" data-type="doughnut">Donut</button>
                    </div>
                </div>
                <div class="chart-canvas-container">
                    <canvas id="networks-chart" class="chart-canvas"></canvas>
                </div>
            </div>
        </div>

        <!-- Section Historique Détaillé -->
        <div class="history-section hidden" id="history-section">
            <!-- Onglets de période -->
            <div class="history-tabs">
                <button class="history-tab active" data-period="24h">
                    <i class="fas fa-clock"></i>
                    <span>24 Heures</span>
                </button>
                <button class="history-tab" data-period="7d">
                    <i class="fas fa-calendar-week"></i>
                    <span>7 Jours</span>
                </button>
                <button class="history-tab" data-period="30d">
                    <i class="fas fa-calendar-alt"></i>
                    <span>30 Jours</span>
                </button>
                <button class="history-tab" data-period="all">
                    <i class="fas fa-infinity"></i>
                    <span>Tout l'historique</span>
                </button>
            </div>

            <!-- Contenu 24h -->
            <div class="history-content active" id="history-24h">
                <div class="history-grid">
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-brain"></i>
                            Évolution QI (24h)
                        </h4>
                        <canvas id="history-qi-24h" class="history-chart-canvas"></canvas>
                    </div>
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-network-wired"></i>
                            Activité Neuronale (24h)
                        </h4>
                        <canvas id="history-neurons-24h" class="history-chart-canvas"></canvas>
                    </div>
                </div>
                <div class="history-stats" id="stats-24h">
                    <!-- Statistiques générées dynamiquement -->
                </div>
            </div>

            <!-- Contenu 7j -->
            <div class="history-content" id="history-7d">
                <div class="history-grid">
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-chart-area"></i>
                            Tendances QI (7 jours)
                        </h4>
                        <canvas id="history-qi-7d" class="history-chart-canvas"></canvas>
                    </div>
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-chart-bar"></i>
                            Performance Neuronale (7 jours)
                        </h4>
                        <canvas id="history-neurons-7d" class="history-chart-canvas"></canvas>
                    </div>
                </div>
                <div class="history-stats" id="stats-7d">
                    <!-- Statistiques générées dynamiquement -->
                </div>
            </div>

            <!-- Contenu 30j -->
            <div class="history-content" id="history-30d">
                <div class="history-grid">
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-chart-line"></i>
                            Progression QI (30 jours)
                        </h4>
                        <canvas id="history-qi-30d" class="history-chart-canvas"></canvas>
                    </div>
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-project-diagram"></i>
                            Évolution Neuronale (30 jours)
                        </h4>
                        <canvas id="history-neurons-30d" class="history-chart-canvas"></canvas>
                    </div>
                </div>
                <div class="history-stats" id="stats-30d">
                    <!-- Statistiques générées dynamiquement -->
                </div>
            </div>

            <!-- Contenu tout l'historique -->
            <div class="history-content" id="history-all">
                <div class="history-grid">
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-history"></i>
                            Historique Complet QI
                        </h4>
                        <canvas id="history-qi-all" class="history-chart-canvas"></canvas>
                    </div>
                    <div class="history-chart-container">
                        <h4 class="history-chart-title">
                            <i class="fas fa-database"></i>
                            Historique Complet Neurones
                        </h4>
                        <canvas id="history-neurons-all" class="history-chart-canvas"></canvas>
                    </div>
                </div>
                <div class="history-stats" id="stats-all">
                    <!-- Statistiques générées dynamiquement -->
                </div>
            </div>
        </div>

        <!-- Grille de métriques -->
        <div class="metrics-grid hidden" id="metrics-grid">
            <!-- Les cartes seront générées dynamiquement -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="/js/native-app.js"></script>

    <script>
        // === VARIABLES GLOBALES ===
        let isAutoRefresh = true;
        let refreshInterval = null;
        let lastUpdateTime = null;

        // === VARIABLES GRAPHIQUES ===
        let qiNeuronChart = null;
        let networksChart = null;
        let chartData = {
            qi: [],
            neurons: [],
            networks: [],
            timestamps: []
        };
        let maxDataPoints = 50; // Nombre maximum de points sur le graphique

        // === VARIABLES HISTORIQUE ===
        let historyCharts = {
            qi: {
                '24h': null,
                '7d': null,
                '30d': null,
                'all': null
            },
            neurons: {
                '24h': null,
                '7d': null,
                '30d': null,
                'all': null
            }
        };
        let historyData = {
            '24h': { qi: [], neurons: [], timestamps: [] },
            '7d': { qi: [], neurons: [], timestamps: [] },
            '30d': { qi: [], neurons: [], timestamps: [] },
            'all': { qi: [], neurons: [], timestamps: [] }
        };
        let currentHistoryPeriod = '24h';

        // === INITIALISATION ===
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Initialisation du Monitoring QI & Neurones v2.0...');

            // Configuration des événements
            setupEventListeners();

            // Initialisation des graphiques
            initializeCharts();

            // Démarrage du chargement
            setTimeout(() => {
                loadBrainData();
            }, 1000);

            console.log('✅ Monitoring v2.0 initialisé');
        });

        // === CONFIGURATION DES ÉVÉNEMENTS ===
        function setupEventListeners() {
            // Bouton actualiser
            document.getElementById('refresh-btn')?.addEventListener('click', () => {
                loadBrainData();
            });

            document.getElementById('manual-refresh-btn')?.addEventListener('click', () => {
                loadBrainData();
            });

            // Bouton auto-refresh
            document.getElementById('auto-refresh-btn')?.addEventListener('click', toggleAutoRefresh);

            // Bouton export
            document.getElementById('export-btn')?.addEventListener('click', exportData);

            // Contrôles des graphiques
            setupChartEventListeners();

            // Contrôles de l'historique
            setupHistoryEventListeners();
        }

        // === CONFIGURATION DES ÉVÉNEMENTS GRAPHIQUES ===
        function setupChartEventListeners() {
            // Boutons de timeframe pour le graphique QI/Neurones
            document.querySelectorAll('[data-timeframe]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // Retirer la classe active des autres boutons
                    e.target.parentElement.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    // Ajouter la classe active au bouton cliqué
                    e.target.classList.add('active');

                    const timeframe = e.target.dataset.timeframe;
                    updateChartTimeframe(timeframe);
                });
            });

            // Boutons de type pour le graphique des réseaux
            document.querySelectorAll('[data-chart="networks"]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // Retirer la classe active des autres boutons
                    e.target.parentElement.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    // Ajouter la classe active au bouton cliqué
                    e.target.classList.add('active');

                    const chartType = e.target.dataset.type;
                    updateNetworksChartType(chartType);
                });
            });
        }

        // === CONFIGURATION DES ÉVÉNEMENTS HISTORIQUE ===
        function setupHistoryEventListeners() {
            // Boutons d'onglets d'historique
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const period = e.target.closest('.history-tab').dataset.period;
                    switchHistoryPeriod(period);
                });
            });
        }

        function switchHistoryPeriod(period) {
            console.log('📊 Changement de période d\'historique:', period);

            // Mettre à jour les onglets
            document.querySelectorAll('.history-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-period="${period}"]`).classList.add('active');

            // Mettre à jour le contenu
            document.querySelectorAll('.history-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`history-${period}`).classList.add('active');

            // Mettre à jour la période actuelle
            currentHistoryPeriod = period;

            // Charger les données d'historique pour cette période
            loadHistoryData(period);
        }

        // === INITIALISATION DES GRAPHIQUES ===
        function initializeCharts() {
            console.log('📊 Initialisation des graphiques...');

            // Initialiser le graphique QI & Neurones
            initQiNeuronChart();

            // Initialiser le graphique des réseaux
            initNetworksChart();

            // Initialiser les graphiques d'historique
            initHistoryCharts();

            console.log('✅ Graphiques initialisés');
        }

        function initQiNeuronChart() {
            const ctx = document.getElementById('qi-neuron-chart');
            if (!ctx) return;

            qiNeuronChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'QI',
                            data: [],
                            borderColor: '#ff69b4',
                            backgroundColor: 'rgba(255, 105, 180, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#ff69b4',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 6
                        },
                        {
                            label: 'Neurones Actifs',
                            data: [],
                            borderColor: '#00bcd4',
                            backgroundColor: 'rgba(0, 188, 212, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#00bcd4',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#ff69b4',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff',
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            ticks: {
                                color: '#ff69b4',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 105, 180, 0.1)'
                            },
                            title: {
                                display: true,
                                text: 'QI',
                                color: '#ff69b4',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: {
                                color: '#00bcd4',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                            title: {
                                display: true,
                                text: 'Neurones',
                                color: '#00bcd4',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        function initNetworksChart() {
            const ctx = document.getElementById('networks-chart');
            if (!ctx) return;

            networksChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Sensoriel', 'Mémoire', 'Long Terme', 'Émotionnel', 'Exécutif', 'Créatif'],
                    datasets: [{
                        label: 'Neurones par Réseau',
                        data: [15, 12, 20, 10, 8, 6],
                        backgroundColor: [
                            'rgba(255, 105, 180, 0.8)',
                            'rgba(0, 188, 212, 0.8)',
                            'rgba(76, 175, 80, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(156, 39, 176, 0.8)',
                            'rgba(255, 87, 34, 0.8)'
                        ],
                        borderColor: [
                            '#ff69b4',
                            '#00bcd4',
                            '#4caf50',
                            '#ffc107',
                            '#9c27b0',
                            '#ff5722'
                        ],
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#ff69b4',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#ffffff',
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            title: {
                                display: true,
                                text: 'Nombre de Neurones',
                                color: '#ffffff',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // === FONCTIONS DE MISE À JOUR DES GRAPHIQUES ===
        function updateChartTimeframe(timeframe) {
            console.log('📊 Changement de timeframe:', timeframe);

            // Ajuster le nombre de points selon le timeframe
            switch(timeframe) {
                case '5m':
                    maxDataPoints = 30; // 30 points pour 5 minutes
                    break;
                case '15m':
                    maxDataPoints = 45; // 45 points pour 15 minutes
                    break;
                case '1h':
                    maxDataPoints = 60; // 60 points pour 1 heure
                    break;
                case '24h':
                    maxDataPoints = 144; // 144 points pour 24 heures (toutes les 10 min)
                    break;
            }

            // Redimensionner les données existantes
            if (chartData.qi.length > maxDataPoints) {
                chartData.qi = chartData.qi.slice(-maxDataPoints);
                chartData.neurons = chartData.neurons.slice(-maxDataPoints);
                chartData.timestamps = chartData.timestamps.slice(-maxDataPoints);

                // Mettre à jour le graphique
                if (qiNeuronChart) {
                    qiNeuronChart.data.labels = chartData.timestamps;
                    qiNeuronChart.data.datasets[0].data = chartData.qi;
                    qiNeuronChart.data.datasets[1].data = chartData.neurons;
                    qiNeuronChart.update('none');
                }
            }
        }

        function updateNetworksChartType(chartType) {
            console.log('📊 Changement de type de graphique:', chartType);

            if (!networksChart) return;

            // Détruire l'ancien graphique
            networksChart.destroy();

            // Créer le nouveau graphique avec le type demandé
            const ctx = document.getElementById('networks-chart');
            const currentData = networksChart ? networksChart.data.datasets[0].data : [15, 12, 20, 10, 8, 6];

            let chartConfig = {
                type: chartType,
                data: {
                    labels: ['Sensoriel', 'Mémoire', 'Long Terme', 'Émotionnel', 'Exécutif', 'Créatif'],
                    datasets: [{
                        label: 'Neurones par Réseau',
                        data: currentData,
                        backgroundColor: [
                            'rgba(255, 105, 180, 0.8)',
                            'rgba(0, 188, 212, 0.8)',
                            'rgba(76, 175, 80, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(156, 39, 176, 0.8)',
                            'rgba(255, 87, 34, 0.8)'
                        ],
                        borderColor: [
                            '#ff69b4',
                            '#00bcd4',
                            '#4caf50',
                            '#ffc107',
                            '#9c27b0',
                            '#ff5722'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: chartType === 'doughnut',
                            labels: {
                                color: '#ffffff',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#ff69b4',
                            borderWidth: 1
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            };

            // Configuration spécifique selon le type
            if (chartType === 'bar') {
                chartConfig.data.datasets[0].borderRadius = 8;
                chartConfig.data.datasets[0].borderSkipped = false;
                chartConfig.options.scales = {
                    x: {
                        ticks: { color: '#ffffff', font: { size: 12, weight: 'bold' } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#ffffff', font: { size: 12 } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        title: {
                            display: true,
                            text: 'Nombre de Neurones',
                            color: '#ffffff',
                            font: { size: 14, weight: 'bold' }
                        }
                    }
                };
            } else if (chartType === 'radar') {
                chartConfig.options.scales = {
                    r: {
                        ticks: { color: '#ffffff', font: { size: 10 } },
                        grid: { color: 'rgba(255, 255, 255, 0.2)' },
                        angleLines: { color: 'rgba(255, 255, 255, 0.2)' },
                        pointLabels: { color: '#ffffff', font: { size: 12, weight: 'bold' } }
                    }
                };
            }

            networksChart = new Chart(ctx, chartConfig);
        }

        // === INITIALISATION DES GRAPHIQUES D'HISTORIQUE ===
        function initHistoryCharts() {
            console.log('📈 Initialisation des graphiques d\'historique...');

            // Initialiser tous les graphiques d'historique
            const periods = ['24h', '7d', '30d', 'all'];

            periods.forEach(period => {
                initHistoryChart('qi', period);
                initHistoryChart('neurons', period);
            });

            // Charger les données initiales pour 24h
            loadHistoryData('24h');
        }

        function initHistoryChart(type, period) {
            const canvasId = `history-${type}-${period}`;
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            const isQi = type === 'qi';
            const color = isQi ? '#ff69b4' : '#00bcd4';
            const label = isQi ? 'QI' : 'Neurones Actifs';

            historyCharts[type][period] = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: label,
                        data: [],
                        borderColor: color,
                        backgroundColor: `${color}20`,
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: color,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 1,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: color,
                            borderWidth: 1,
                            callbacks: {
                                title: function(context) {
                                    return formatHistoryDate(context[0].label, period);
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff',
                                font: { size: 10 },
                                maxTicksLimit: 8,
                                callback: function(value, index, values) {
                                    return formatHistoryLabel(this.getLabelForValue(value), period);
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: color,
                                font: { size: 10 }
                            },
                            grid: {
                                color: `${color}20`
                            },
                            title: {
                                display: true,
                                text: label,
                                color: color,
                                font: { size: 12, weight: 'bold' }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // === CHARGEMENT DES DONNÉES D'HISTORIQUE ===
        async function loadHistoryData(period) {
            console.log(`📊 Chargement des données d'historique pour ${period}...`);

            try {
                // Tentative de récupération des vraies données d'historique
                const response = await fetch(`/api/brain/history/${period}`);
                let data;

                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        data = result.data;
                        console.log(`✅ Données d'historique ${period} récupérées:`, data);
                    } else {
                        throw new Error('Données d\'historique non disponibles');
                    }
                } else {
                    throw new Error('API d\'historique non disponible');
                }

                // Mettre à jour les graphiques avec les vraies données
                updateHistoryCharts(period, data);
                updateHistoryStats(period, data);

            } catch (error) {
                console.log(`⚠️ API d'historique non disponible pour ${period}, génération de données simulées`);

                // Générer des données simulées basées sur vos vraies informations
                const simulatedData = generateHistoryData(period);
                updateHistoryCharts(period, simulatedData);
                updateHistoryStats(period, simulatedData);
            }
        }

        function generateHistoryData(period) {
            const now = Date.now();
            let dataPoints, interval, startTime;

            switch(period) {
                case '24h':
                    dataPoints = 24; // Une mesure par heure
                    interval = 60 * 60 * 1000; // 1 heure
                    startTime = now - (24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    dataPoints = 7; // Une mesure par jour
                    interval = 24 * 60 * 60 * 1000; // 1 jour
                    startTime = now - (7 * 24 * 60 * 60 * 1000);
                    break;
                case '30d':
                    dataPoints = 30; // Une mesure par jour
                    interval = 24 * 60 * 60 * 1000; // 1 jour
                    startTime = now - (30 * 24 * 60 * 60 * 1000);
                    break;
                case 'all':
                    dataPoints = 90; // 3 mois de données
                    interval = 24 * 60 * 60 * 1000; // 1 jour
                    startTime = now - (90 * 24 * 60 * 60 * 1000);
                    break;
            }

            const qi = [];
            const neurons = [];
            const timestamps = [];

            for (let i = 0; i < dataPoints; i++) {
                const timestamp = startTime + (i * interval);
                const variation = Math.sin(timestamp / (24 * 60 * 60 * 1000)) * 0.1; // Variation journalière
                const trend = i * 0.02; // Légère tendance à la hausse

                qi.push(Math.round(120 + variation * 10 + trend));
                neurons.push(Math.round(35 + variation * 5 + trend * 0.5));
                timestamps.push(new Date(timestamp).toISOString());
            }

            return { qi, neurons, timestamps };
        }

        function updateHistoryCharts(period, data) {
            // Mettre à jour le graphique QI
            if (historyCharts.qi[period]) {
                historyCharts.qi[period].data.labels = data.timestamps;
                historyCharts.qi[period].data.datasets[0].data = data.qi;
                historyCharts.qi[period].update('active');
            }

            // Mettre à jour le graphique Neurones
            if (historyCharts.neurons[period]) {
                historyCharts.neurons[period].data.labels = data.timestamps;
                historyCharts.neurons[period].data.datasets[0].data = data.neurons;
                historyCharts.neurons[period].update('active');
            }

            // Sauvegarder les données
            historyData[period] = data;
        }

        function updateHistoryStats(period, data) {
            const statsContainer = document.getElementById(`stats-${period}`);
            if (!statsContainer) return;

            // Calculer les statistiques
            const qiStats = calculateStats(data.qi);
            const neuronStats = calculateStats(data.neurons);
            const qiTrend = calculateTrend(data.qi);
            const neuronTrend = calculateTrend(data.neurons);

            statsContainer.innerHTML = `
                <div class="history-stat-card">
                    <div class="history-stat-label">QI Moyen</div>
                    <div class="history-stat-value">${qiStats.avg}</div>
                    <div class="history-stat-trend ${getTrendClass(qiTrend)}">
                        <i class="fas fa-${getTrendIcon(qiTrend)}"></i>
                        ${Math.abs(qiTrend).toFixed(1)}%
                    </div>
                </div>
                <div class="history-stat-card">
                    <div class="history-stat-label">QI Maximum</div>
                    <div class="history-stat-value">${qiStats.max}</div>
                </div>
                <div class="history-stat-card">
                    <div class="history-stat-label">QI Minimum</div>
                    <div class="history-stat-value">${qiStats.min}</div>
                </div>
                <div class="history-stat-card">
                    <div class="history-stat-label">Neurones Moyens</div>
                    <div class="history-stat-value">${neuronStats.avg}</div>
                    <div class="history-stat-trend ${getTrendClass(neuronTrend)}">
                        <i class="fas fa-${getTrendIcon(neuronTrend)}"></i>
                        ${Math.abs(neuronTrend).toFixed(1)}%
                    </div>
                </div>
                <div class="history-stat-card">
                    <div class="history-stat-label">Neurones Max</div>
                    <div class="history-stat-value">${neuronStats.max}</div>
                </div>
                <div class="history-stat-card">
                    <div class="history-stat-label">Neurones Min</div>
                    <div class="history-stat-value">${neuronStats.min}</div>
                </div>
            `;
        }

        // === FONCTIONS UTILITAIRES HISTORIQUE ===
        function calculateStats(data) {
            if (!data || data.length === 0) return { avg: 0, max: 0, min: 0 };

            const sum = data.reduce((a, b) => a + b, 0);
            return {
                avg: Math.round(sum / data.length),
                max: Math.max(...data),
                min: Math.min(...data)
            };
        }

        function calculateTrend(data) {
            if (!data || data.length < 2) return 0;

            const first = data[0];
            const last = data[data.length - 1];
            return ((last - first) / first) * 100;
        }

        function getTrendClass(trend) {
            if (trend > 1) return 'trend-up';
            if (trend < -1) return 'trend-down';
            return 'trend-stable';
        }

        function getTrendIcon(trend) {
            if (trend > 1) return 'arrow-up';
            if (trend < -1) return 'arrow-down';
            return 'minus';
        }

        function formatHistoryDate(dateStr, period) {
            const date = new Date(dateStr);

            switch(period) {
                case '24h':
                    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
                case '7d':
                case '30d':
                    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
                case 'all':
                    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: '2-digit' });
                default:
                    return dateStr;
            }
        }

        function formatHistoryLabel(dateStr, period) {
            const date = new Date(dateStr);

            switch(period) {
                case '24h':
                    return date.getHours() + 'h';
                case '7d':
                    return date.toLocaleDateString('fr-FR', { weekday: 'short' });
                case '30d':
                    return date.getDate() + '/' + (date.getMonth() + 1);
                case 'all':
                    return (date.getMonth() + 1) + '/' + date.getFullYear().toString().slice(-2);
                default:
                    return dateStr;
            }
        }

        // === CHARGEMENT DES DONNÉES RÉELLES ===
        async function loadBrainData() {
            console.log('🧠 Chargement des données réelles du cerveau...');

            try {
                // Utiliser l'API globale pour obtenir les vraies données
                const response = await fetch('/api/global/qi-detailed');
                const data = await response.json();

                if (data.success) {
                    console.log('✅ Données réelles reçues:', data);

                    // Convertir les données au format attendu
                    const formattedData = {
                        qi: data.qi_data.qi || 203,
                        qiLevel: getQI 225),
                        totalNeurons: 100, // Total possible
                        activeNeurons: data.qi_data.neurones || 71,
                        efficiency: data.qi_data.efficiency || 85,
                        temperature: data.qi_data.temperature || 42.3,
                        evolution_level: data.qi_data.evolution_level || 85,
                        accelerateurs: data.qi_data.accelerateurs || 8,
                        networks: {
                            sensory: Math.floor((data.qi_data.neurones || 71) * 0.2),
                            memory: Math.floor((data.qi_data.neurones || 71) * 0.15),
                            longTerm: Math.floor((data.qi_data.neurones || 71) * 0.3),
                            emotional: Math.floor((data.qi_data.neurones || 71) * 0.15),
                            executive: Math.floor((data.qi_data.neurones || 71) * 0.1),
                            creative: Math.floor((data.qi_data.neurones || 71) * 0.1)
                        },
                        timestamp: data.timestamp
                    };

                    displayData(formattedData);
                    updateCharts(formattedData);
                    updateSystemStatus(`Système actif - QI: ${formattedData.qi} - Neurones: ${formattedData.activeNeurons}`);
                } else {
                    throw new Error(data.error || 'Erreur lors du chargement des données');
                }
            } catch (error) {
                console.error('❌ Erreur lors du chargement:', error);
                updateSystemStatus('Erreur de connexion - Utilisation des données de sauvegarde...');

                // Essayer de récupérer les données de l'état global
                try {
                    const fallbackResponse = await fetch('/api/global/state');
                    const fallbackData = await fallbackResponse.json();

                    if (fallbackData.success && fallbackData.state.agent) {
                        const agent = fallbackData.state.agent;
                        const backupData = {
                            qi: agent.qi || 148,
                            qiLevel: getQILevel(agent.qi || 148),
                            totalNeurons: 100,
                            activeNeurons: agent.neurones || 71,
                            efficiency: Math.round((agent.neurones || 71) / 100 * 100),
                            temperature: agent.temperature || 42.3,
                            evolution_level: agent.evolution_level || 85,
                            accelerateurs: agent.accelerateurs || 8,
                            networks: {
                                sensory: Math.floor((agent.neurones || 71) * 0.2),
                                memory: Math.floor((agent.neurones || 71) * 0.15),
                                longTerm: Math.floor((agent.neurones || 71) * 0.3),
                                emotional: Math.floor((agent.neurones || 71) * 0.15),
                                executive: Math.floor((agent.neurones || 71) * 0.1),
                                creative: Math.floor((agent.neurones || 71) * 0.1)
                            },
                            timestamp: fallbackData.timestamp
                        };

                        displayData(backupData);
                        updateCharts(backupData);
                        updateSystemStatus(`Données de sauvegarde - QI: ${backupData.qi} - Neurones: ${backupData.activeNeurons}`);
                        return;
                    }
                } catch (fallbackError) {
                    console.error('❌ Erreur données de sauvegarde:', fallbackError);
                }

                // En dernier recours, utiliser les données par défaut
                const config = window.LOUNA_CONFIG || {};
                const defaultData = {
                    qi: config.qi?.current || 203,
                    qiLevel: config.qi?.level || 'Quasi-AGI',
                    totalNeurons: config.neurons?.total || 89,
                    activeNeurons: config.neurons?.active || 78,
                    efficiency: config.neurons?.efficiency || 94.0,
                    temperature: 37.0,
                    evolution_level: 100,
                    accelerateurs: 13,
                    networks: {
                        sensory: 18,
                        memory: 13,
                        longTerm: 27,
                        emotional: 13,
                        executive: 9,
                        creative: 9
                    },
                    timestamp: new Date().toISOString()
                };

                displayData(defaultData);
                updateCharts(defaultData);
                updateSystemStatus(`Mode par défaut - QI: ${defaultData.qi} - Neurones: ${defaultData.activeNeurons}`);
            }
        }

        // Fonction pour déterminer le niveau de QI
        function getQILevel(qi) {
            // Utiliser la configuration globale si disponible
            if (window.LounaUtils) {
                return window.LounaUtils.getQILevel(qi);
            }

            // Fallback si la configuration globale n'est pas chargée
            if (qi >= 200) return "Quasi-AGI";
            if (qi >= 180) return "Génie Exceptionnel";
            if (qi >= 160) return "Très Supérieur";
            if (qi >= 140) return "Supérieur";
            if (qi >= 120) return "Intelligent";
            if (qi >= 100) return "Moyen";
            if (qi >= 80) return "Faible";
            return "Très Faible";
        }

        // === AFFICHAGE DES DONNÉES ===
        function displayData(data) {
            const loadingContainer = document.getElementById('loading-container');
            const chartsSection = document.getElementById('charts-section');
            const historySection = document.getElementById('history-section');
            const metricsGrid = document.getElementById('metrics-grid');

            // Masquer le chargement
            loadingContainer.classList.add('hidden');

            // Mettre à jour les graphiques
            updateCharts(data);

            // Créer les cartes de métriques
            createMetricCards(data);

            // Afficher les sections avec animation
            chartsSection.classList.remove('hidden');
            chartsSection.classList.add('fade-in');

            historySection.classList.remove('hidden');
            historySection.classList.add('fade-in');

            metricsGrid.classList.remove('hidden');
            metricsGrid.classList.add('fade-in');

            // Mettre à jour le timestamp
            lastUpdateTime = new Date();
            updateSystemStatus('Système actif - Dernière mise à jour: ' + lastUpdateTime.toLocaleTimeString());

            // Démarrer l'auto-refresh si activé
            if (isAutoRefresh && !refreshInterval) {
                startAutoRefresh();
            }
        }

        // === MISE À JOUR DES GRAPHIQUES ===
        function updateCharts(data) {
            const now = new Date();
            const timeLabel = now.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });

            // Ajouter les nouvelles données
            chartData.qi.push(data.qi);
            chartData.neurons.push(data.activeNeurons);
            chartData.timestamps.push(timeLabel);

            // Limiter le nombre de points
            if (chartData.qi.length > maxDataPoints) {
                chartData.qi.shift();
                chartData.neurons.shift();
                chartData.timestamps.shift();
            }

            // Mettre à jour le graphique QI & Neurones
            if (qiNeuronChart) {
                qiNeuronChart.data.labels = chartData.timestamps;
                qiNeuronChart.data.datasets[0].data = chartData.qi;
                qiNeuronChart.data.datasets[1].data = chartData.neurons;
                qiNeuronChart.update('active');
            }

            // Mettre à jour le graphique des réseaux
            if (networksChart && data.networks) {
                const networkData = [
                    data.networks.sensory || 15,
                    data.networks.memory || 12,
                    data.networks.longTerm || 20,
                    data.networks.emotional || 10,
                    data.networks.executive || 8,
                    data.networks.creative || 6
                ];
                networksChart.data.datasets[0].data = networkData;
                networksChart.update('active');
            }
        }

        // === CRÉATION DES CARTES DE MÉTRIQUES ===
        function createMetricCards(data) {
            const metricsGrid = document.getElementById('metrics-grid');
            if (!metricsGrid) return;

            metricsGrid.innerHTML = `
                <!-- Carte QI -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3 class="metric-title">
                            <i class="fas fa-brain"></i>
                            Quotient Intellectuel
                        </h3>
                        <span class="metric-status">Actif</span>
                    </div>
                    <div class="primary-metric">
                        <span class="metric-value">${data.qi}</span>
                        <div class="metric-label">
                            ${data.qiLevel} <span class="metric-unit">points</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-label">Niveau cognitif</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${Math.min((data.qi / 200) * 100, 100)}%"></div>
                        </div>
                        <div class="progress-text">${data.qi}/200 points</div>
                    </div>
                </div>

                <!-- Carte Neurones -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3 class="metric-title">
                            <i class="fas fa-network-wired"></i>
                            Réseau Neuronal
                        </h3>
                        <span class="metric-status">Optimal</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Total</span>
                            <span class="value">${data.totalNeurons}</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Actifs</span>
                            <span class="value">${data.activeNeurons}</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-label">Efficacité neuronale</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${data.efficiency}%"></div>
                        </div>
                        <div class="progress-text">${data.efficiency}% d'efficacité</div>
                    </div>
                </div>

                <!-- Carte Réseaux Spécialisés -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3 class="metric-title">
                            <i class="fas fa-project-diagram"></i>
                            Réseaux Spécialisés
                        </h3>
                        <span class="metric-status">Synchronisé</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Sensoriel</span>
                            <span class="value">${data.networks?.sensory || 15}</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Mémoire</span>
                            <span class="value">${data.networks?.memory || 12}</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Long Terme</span>
                            <span class="value">${data.networks?.longTerm || 20}</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Créatif</span>
                            <span class="value">${data.networks?.creative || 6}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // === FONCTIONS UTILITAIRES ===
        function toggleAutoRefresh() {
            isAutoRefresh = !isAutoRefresh;
            const btn = document.getElementById('auto-refresh-btn');

            if (isAutoRefresh) {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-pause"></i><span>Auto-refresh</span>';
                startAutoRefresh();
            } else {
                btn.classList.remove('active');
                btn.innerHTML = '<i class="fas fa-play"></i><span>Auto-refresh</span>';
                stopAutoRefresh();
            }
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(() => {
                loadBrainData();
            }, 5000); // Actualisation toutes les 5 secondes
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        function updateSystemStatus(message) {
            const statusElement = document.getElementById('system-status');
            if (statusElement) {
                statusElement.querySelector('span').textContent = message;
            }
        }

        function exportData() {
            const exportData = {
                timestamp: new Date().toISOString(),
                chartData: chartData,
                historyData: historyData,
                lastUpdate: lastUpdateTime
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `monitoring-qi-neurones-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);
            console.log('📊 Données exportées');
        }

        // === CONTRÔLES DE NAVIGATION ===
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log(`Erreur lors du passage en plein écran: ${err.message}`);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // === INITIALISATION DES ÉVÉNEMENTS DE NAVIGATION ===
        document.addEventListener('DOMContentLoaded', function() {
            // Bouton d'actualisation
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    loadBrainData();
                    console.log('🔄 Actualisation manuelle');
                });
            }

            // Bouton plein écran
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', toggleFullscreen);
            }

            // Gestion du changement d'état plein écran
            document.addEventListener('fullscreenchange', () => {
                const fullscreenBtn = document.getElementById('fullscreen-btn');
                if (fullscreenBtn) {
                    const icon = fullscreenBtn.querySelector('i');
                    if (document.fullscreenElement) {
                        icon.className = 'fas fa-compress';
                        fullscreenBtn.title = 'Quitter le plein écran';
                    } else {
                        icon.className = 'fas fa-expand';
                        fullscreenBtn.title = 'Plein écran';
                    }
                }
            });

            // Animation du bouton d'accueil
            const homeButton = document.getElementById('homeButton');
            if (homeButton) {
                homeButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 6px 20px rgba(233, 30, 99, 0.4)';
                });

                homeButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 4px 15px rgba(233, 30, 99, 0.3)';
                });
            }

            console.log('🎯 Navigation initialisée');
        });
    </script>
<script src="/js/auto-init-fixes.js"></script>
<script src="/js/qi-manager.js"></script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
