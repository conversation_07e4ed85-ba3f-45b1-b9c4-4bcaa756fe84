<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Code Avancé - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/javascript-hint.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 10px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .main-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            height: calc(100vh - 60px);
            gap: 0;
        }

        .sidebar {
            background: rgba(0, 0, 0, 0.4);
            border-right: 2px solid rgba(255, 105, 180, 0.3);
            padding: 15px;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #ff69b4;
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-tree {
            margin-bottom: 20px;
        }

        .file-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .file-item:hover {
            background: rgba(255, 105, 180, 0.2);
        }

        .file-item.active {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            background: rgba(0, 0, 0, 0.2);
        }

        .editor-toolbar {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-bottom: 2px solid rgba(255, 105, 180, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .toolbar-btn {
            background: linear-gradient(135deg, #9c27b0, #673ab7);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .toolbar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);
        }

        .language-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
        }

        .editor-wrapper {
            flex: 1;
            position: relative;
        }

        .CodeMirror {
            height: 100% !important;
            font-size: 14px;
            font-family: 'Fira Code', 'Consolas', monospace;
        }

        .properties-panel {
            background: rgba(0, 0, 0, 0.4);
            border-left: 2px solid rgba(255, 105, 180, 0.3);
            padding: 15px;
            overflow-y: auto;
        }

        .panel-section {
            margin-bottom: 20px;
        }

        .panel-section h4 {
            color: #ff69b4;
            margin-bottom: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .property-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 12px;
        }

        .multimedia-extensions {
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid rgba(255, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .extension-btn {
            width: 100%;
            background: linear-gradient(135deg, #ff4444, #cc0000);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px 0;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .extension-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.4);
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.6);
            padding: 5px 15px;
            border-top: 1px solid rgba(255, 105, 180, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            height: 30px;
        }

        .brain-connection {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid rgba(0, 255, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .brain-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .brain-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff00;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .ai-assist-btn {
            width: 100%;
            background: linear-gradient(135deg, #00ff00, #00cc00);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .ai-assist-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 255, 0, 0.4);
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 200px 1fr 250px;
            }
        }

        @media (max-width: 900px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .sidebar, .properties-panel {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-code"></i>
            Éditeur de Code Avancé - Louna AI (QI: 235)
        </h1>
        <div class="nav-buttons">
            <a href="/voice-system-enhanced.html" class="nav-btn">
                <i class="fas fa-microphone"></i>
                Vocal
            </a>
            <a href="/qi-manager.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                QI
            </a>
            <a href="/thermal-memory-dashboard.html" class="nav-btn">
                <i class="fas fa-fire"></i>
                Mémoire
            </a>
            <a href="/chat.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="main-container">
        <!-- Sidebar - Explorateur de fichiers -->
        <div class="sidebar">
            <h3>
                <i class="fas fa-folder-open"></i>
                Explorateur
            </h3>
            <div class="file-tree" id="fileTree">
                <div class="file-item active" onclick="openFile('index.html', 'html')">
                    <i class="fab fa-html5"></i>
                    index.html
                </div>
                <div class="file-item" onclick="openFile('server.js', 'javascript')">
                    <i class="fab fa-js-square"></i>
                    server.js
                </div>
                <div class="file-item" onclick="openFile('style.css', 'css')">
                    <i class="fab fa-css3-alt"></i>
                    style.css
                </div>
                <div class="file-item" onclick="openFile('voice-system.js', 'javascript')">
                    <i class="fas fa-microphone"></i>
                    voice-system.js
                </div>
                <div class="file-item" onclick="openFile('thermal-memory.js', 'javascript')">
                    <i class="fas fa-brain"></i>
                    thermal-memory.js
                </div>
                <div class="file-item" onclick="openFile('package.json', 'javascript')">
                    <i class="fas fa-box"></i>
                    package.json
                </div>
            </div>

            <h3>
                <i class="fas fa-magic"></i>
                Extensions Multimédia
            </h3>
            <div class="multimedia-extensions">
                <button class="extension-btn" onclick="generateImageCode()">
                    <i class="fas fa-image"></i>
                    Générateur d'Images
                </button>
                <button class="extension-btn" onclick="generateMusicCode()">
                    <i class="fas fa-music"></i>
                    Générateur de Musique
                </button>
                <button class="extension-btn" onclick="generateVideoCode()">
                    <i class="fas fa-video"></i>
                    Générateur de Vidéos
                </button>
                <button class="extension-btn" onclick="generateVoiceCode()">
                    <i class="fas fa-microphone-alt"></i>
                    Synthèse Vocale
                </button>
                <button class="extension-btn" onclick="generate3DCode()">
                    <i class="fas fa-cube"></i>
                    Modélisation 3D
                </button>
            </div>
        </div>

        <!-- Zone d'édition principale -->
        <div class="editor-container">
            <div class="editor-toolbar">
                <div class="toolbar-left">
                    <button class="toolbar-btn" onclick="saveFile()">
                        <i class="fas fa-save"></i>
                        Sauvegarder
                    </button>
                    <button class="toolbar-btn" onclick="runCode()">
                        <i class="fas fa-play"></i>
                        Exécuter
                    </button>
                    <button class="toolbar-btn" onclick="formatCode()">
                        <i class="fas fa-magic"></i>
                        Formater
                    </button>
                    <select class="language-select" id="languageSelect" onchange="changeLanguage()">
                        <option value="htmlmixed">HTML</option>
                        <option value="javascript">JavaScript</option>
                        <option value="css">CSS</option>
                        <option value="python">Python</option>
                        <option value="text/plain">Texte</option>
                    </select>
                </div>
                <div class="toolbar-right">
                    <span id="currentFile">index.html</span>
                </div>
            </div>
            
            <div class="editor-wrapper">
                <textarea id="codeEditor"></textarea>
            </div>
            
            <div class="status-bar">
                <div class="status-left">
                    <span id="cursorPosition">Ligne 1, Col 1</span>
                    <span style="margin-left: 20px;" id="fileSize">0 caractères</span>
                </div>
                <div class="status-right">
                    <span id="editorMode">Mode: Édition</span>
                </div>
            </div>
        </div>

        <!-- Panel de propriétés -->
        <div class="properties-panel">
            <!-- Connexion au cerveau de l'agent -->
            <div class="brain-connection">
                <div class="brain-status">
                    <div class="brain-indicator"></div>
                    Cerveau Louna Connecté
                </div>
                <div style="font-size: 12px; margin-bottom: 10px;">
                    QI: 235 • Neurones: 145 • Connexions: 717
                </div>
                <button class="ai-assist-btn" onclick="askAIAssistance()">
                    <i class="fas fa-robot"></i>
                    Assistance IA
                </button>
                <button class="ai-assist-btn" onclick="generateCodeWithAI()">
                    <i class="fas fa-magic"></i>
                    Générer Code IA
                </button>
                <button class="ai-assist-btn" onclick="optimizeCodeWithAI()">
                    <i class="fas fa-rocket"></i>
                    Optimiser Code
                </button>
            </div>
        </div>
    </div>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
