<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interface Vocale - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title .icon {
            font-size: 2.5rem;
            color: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        /* Container principal */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* Interface vocale principale */
        .voice-interface {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            text-align: center;
        }

        .voice-avatar {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .voice-avatar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle, transparent 40%, rgba(255, 255, 255, 0.1) 70%);
            animation: ripple 2s infinite;
        }

        @keyframes ripple {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(1.2); opacity: 0; }
        }

        .voice-avatar.listening::before {
            animation: ripple 1s infinite;
        }

        .voice-avatar.speaking::before {
            animation: ripple 0.5s infinite;
            background: radial-gradient(circle, transparent 40%, rgba(255, 107, 107, 0.3) 70%);
        }

        .avatar-icon {
            font-size: 4rem;
            color: white;
            z-index: 1;
        }

        .voice-status {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #4ecdc4;
            font-weight: 500;
        }

        .voice-controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .voice-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .voice-btn.record {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .voice-btn.stop {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .voice-btn.play {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .voice-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .voice-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Conversation */
        .conversation {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
        }

        .conversation-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #4ecdc4;
            text-align: center;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            position: relative;
        }

        .message.user {
            background: rgba(78, 205, 196, 0.2);
            border-left: 4px solid #4ecdc4;
            margin-left: 20px;
        }

        .message.assistant {
            background: rgba(255, 107, 107, 0.2);
            border-left: 4px solid #ff6b6b;
            margin-right: 20px;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .message-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .message.user .message-icon {
            background: #4ecdc4;
        }

        .message.assistant .message-icon {
            background: #ff6b6b;
        }

        .message-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .message-content {
            color: white;
            line-height: 1.5;
        }

        /* Paramètres vocaux */
        .voice-settings {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .settings-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #4ecdc4;
            text-align: center;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .setting-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .setting-control {
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 14px;
        }

        .setting-control:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .voice-avatar {
                width: 150px;
                height: 150px;
            }

            .avatar-icon {
                font-size: 3rem;
            }

            .voice-controls {
                gap: 15px;
            }

            .voice-btn {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-microphone icon"></i>
                <h1>Interface Vocale</h1>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/kyber-dashboard.html" class="nav-btn">
                    <i class="fas fa-bolt"></i>
                    Kyber
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Interface vocale principale -->
        <div class="voice-interface">
            <div class="voice-avatar" id="voice-avatar">
                <i class="fas fa-microphone avatar-icon"></i>
            </div>

            <div class="voice-status" id="voice-status">Prêt à vous écouter</div>

            <div class="voice-controls">
                <button class="voice-btn record" id="record-btn" onclick="startRecording()">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="voice-btn stop" id="stop-btn" onclick="stopRecording()" disabled>
                    <i class="fas fa-stop"></i>
                </button>
                <button class="voice-btn play" id="play-btn" onclick="playResponse()" disabled>
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>

        <!-- Conversation -->
        <div class="conversation">
            <h3 class="conversation-title">Conversation Vocale</h3>
            <div id="conversation-content">
                <div class="message assistant">
                    <div class="message-header">
                        <div class="message-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <span class="message-time">Maintenant</span>
                    </div>
                    <div class="message-content">
                        Bonjour ! Je suis Louna, votre assistant vocal. Appuyez sur le bouton microphone pour commencer à parler.
                    </div>
                </div>
            </div>
        </div>

        <!-- Paramètres vocaux -->
        <div class="voice-settings">
            <h3 class="settings-title">Paramètres Vocaux</h3>
            <div class="settings-grid">
                <div class="setting-group">
                    <label class="setting-label">Langue de reconnaissance</label>
                    <select class="setting-control" id="language-select">
                        <option value="fr-FR">Français (France)</option>
                        <option value="en-US">English (US)</option>
                        <option value="es-ES">Español</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label class="setting-label">Voix de synthèse</label>
                    <select class="setting-control" id="voice-select">
                        <option value="female">Voix féminine</option>
                        <option value="male">Voix masculine</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label class="setting-label">Vitesse de parole</label>
                    <input type="range" class="setting-control" id="speed-range" min="0.5" max="2" step="0.1" value="1">
                </div>
                <div class="setting-group">
                    <label class="setting-label">Volume</label>
                    <input type="range" class="setting-control" id="volume-range" min="0" max="1" step="0.1" value="0.8">
                </div>
            </div>
        </div>
    </div>

    <script>
        let recognition;
        let synthesis = window.speechSynthesis;
        let isRecording = false;
        let currentUtterance = null;

        // Initialisation de la reconnaissance vocale
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = document.getElementById('language-select').value;

                recognition.onstart = function() {
                    console.log('🎤 Reconnaissance vocale démarrée');
                    updateStatus('Écoute en cours...', 'listening');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    console.log('🗣️ Texte reconnu:', transcript);
                    addMessage(transcript, 'user');
                    processVoiceInput(transcript);
                };

                recognition.onerror = function(event) {
                    console.error('❌ Erreur reconnaissance vocale:', event.error);
                    updateStatus('Erreur de reconnaissance', 'error');
                    resetControls();
                };

                recognition.onend = function() {
                    console.log('🔇 Reconnaissance vocale terminée');
                    resetControls();
                };
            } else {
                console.error('❌ Reconnaissance vocale non supportée');
                updateStatus('Reconnaissance vocale non supportée', 'error');
            }
        }

        // Démarrer l'enregistrement
        function startRecording() {
            if (recognition && !isRecording) {
                isRecording = true;
                recognition.start();

                document.getElementById('record-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;

                updateStatus('Parlez maintenant...', 'listening');
            }
        }

        // Arrêter l'enregistrement
        function stopRecording() {
            if (recognition && isRecording) {
                recognition.stop();
                resetControls();
            }
        }

        // Réinitialiser les contrôles
        function resetControls() {
            isRecording = false;
            document.getElementById('record-btn').disabled = false;
            document.getElementById('stop-btn').disabled = true;
            updateStatus('Prêt à vous écouter', 'ready');
        }

        // Mettre à jour le statut
        function updateStatus(message, state) {
            const statusElement = document.getElementById('voice-status');
            const avatarElement = document.getElementById('voice-avatar');

            statusElement.textContent = message;

            // Retirer toutes les classes d'état
            avatarElement.classList.remove('listening', 'speaking');

            // Ajouter la nouvelle classe d'état
            if (state === 'listening') {
                avatarElement.classList.add('listening');
            } else if (state === 'speaking') {
                avatarElement.classList.add('speaking');
            }
        }

        // Ajouter un message à la conversation
        function addMessage(content, type) {
            const conversationContent = document.getElementById('conversation-content');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const now = new Date();
            const timeString = now.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-icon">
                        <i class="fas fa-${type === 'user' ? 'user' : 'robot'}"></i>
                    </div>
                    <span class="message-time">${timeString}</span>
                </div>
                <div class="message-content">${content}</div>
            `;

            conversationContent.appendChild(messageDiv);
            conversationContent.scrollTop = conversationContent.scrollHeight;
        }

        // Traiter l'entrée vocale
        async function processVoiceInput(text) {
            try {
                updateStatus('Traitement en cours...', 'processing');

                // Envoyer à l'agent Claude
                const response = await fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: text,
                        conversationId: 'voice-chat',
                        isVoice: true
                    })
                });

                const data = await response.json();

                if (data.success && data.response) {
                    addMessage(data.response, 'assistant');
                    speakResponse(data.response);
                } else {
                    const fallbackResponse = "Je n'ai pas pu traiter votre demande. Pouvez-vous répéter ?";
                    addMessage(fallbackResponse, 'assistant');
                    speakResponse(fallbackResponse);
                }
            } catch (error) {
                console.error('❌ Erreur traitement vocal:', error);
                const errorResponse = "Désolé, j'ai rencontré une erreur technique.";
                addMessage(errorResponse, 'assistant');
                speakResponse(errorResponse);
            }
        }

        // Synthèse vocale
        function speakResponse(text) {
            if (synthesis) {
                // Arrêter toute synthèse en cours
                synthesis.cancel();

                currentUtterance = new SpeechSynthesisUtterance(text);
                currentUtterance.lang = 'fr-FR';
                currentUtterance.rate = parseFloat(document.getElementById('speed-range').value);
                currentUtterance.volume = parseFloat(document.getElementById('volume-range').value);

                // Sélectionner la voix
                const voices = synthesis.getVoices();
                const voiceType = document.getElementById('voice-select').value;
                const selectedVoice = voices.find(voice =>
                    voice.lang.includes('fr') &&
                    (voiceType === 'female' ? voice.name.includes('female') || voice.name.includes('Female') :
                     voice.name.includes('male') || voice.name.includes('Male'))
                ) || voices.find(voice => voice.lang.includes('fr'));

                if (selectedVoice) {
                    currentUtterance.voice = selectedVoice;
                }

                currentUtterance.onstart = function() {
                    updateStatus('Louna parle...', 'speaking');
                    document.getElementById('play-btn').disabled = true;
                };

                currentUtterance.onend = function() {
                    updateStatus('Prêt à vous écouter', 'ready');
                    document.getElementById('play-btn').disabled = false;
                };

                currentUtterance.onerror = function(event) {
                    console.error('❌ Erreur synthèse vocale:', event.error);
                    updateStatus('Erreur de synthèse vocale', 'error');
                    document.getElementById('play-btn').disabled = false;
                };

                synthesis.speak(currentUtterance);
            }
        }

        // Rejouer la dernière réponse
        function playResponse() {
            if (currentUtterance) {
                synthesis.cancel();
                synthesis.speak(currentUtterance);
            }
        }

        // Gestionnaires d'événements pour les paramètres
        document.getElementById('language-select').addEventListener('change', function() {
            if (recognition) {
                recognition.lang = this.value;
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initSpeechRecognition();

            // Charger les voix disponibles
            if (synthesis) {
                synthesis.onvoiceschanged = function() {
                    console.log('🔊 Voix disponibles chargées');
                };
            }

            console.log('🎤 Interface vocale initialisée');
        });

        // Raccourcis clavier
        document.addEventListener('keydown', function(event) {
            if (event.code === 'Space' && event.ctrlKey) {
                event.preventDefault();
                if (!isRecording) {
                    startRecording();
                } else {
                    stopRecording();
                }
            }
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
