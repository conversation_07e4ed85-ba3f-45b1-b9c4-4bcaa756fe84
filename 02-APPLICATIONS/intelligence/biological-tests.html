<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tests Biologiques - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Principal */
        .bio-header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .bio-header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .bio-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bio-title i {
            font-size: 2.5rem;
            color: #4caf50;
            animation: pulse 2s infinite;
        }

        .bio-title h1 {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .bio-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-left: 10px;
        }

        .bio-nav {
            display: flex;
            gap: 15px;
        }

        .bio-nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .bio-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .bio-nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        .bio-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 25px;
            color: #4caf50;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        /* Container Principal */
        .bio-main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .bio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .bio-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bio-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .bio-card h2 {
            color: #4caf50;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bio-card h2 i {
            color: #8bc34a;
        }

        .test-btn {
            background: linear-gradient(135deg, #4caf50, #45a049);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 15px 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
            margin: 10px 0;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .test-btn.info {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .results-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4caf50;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
        }

        .result-value {
            color: #4caf50;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .vital-signs {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .vital-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .vital-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4caf50;
            margin-bottom: 5px;
        }

        .vital-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .heartbeat {
            animation: heartbeat 1s infinite;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .bio-grid {
                grid-template-columns: 1fr;
            }
            
            .bio-nav {
                flex-direction: column;
                gap: 10px;
            }
            
            .bio-header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Principal -->
    <div class="bio-header">
        <div class="bio-header-content">
            <div class="bio-title">
                <i class="fas fa-microscope"></i>
                <h1>Tests Biologiques</h1>
                <span class="bio-subtitle">Analyse biologique avancée - QI 225</span>
            </div>
            <div class="bio-nav">
                <a href="/" class="bio-nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/dashboard-master.html" class="bio-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    Tableau de Bord
                </a>
                <a href="/qi-manager.html" class="bio-nav-btn">
                    <i class="fas fa-brain"></i>
                    Gestionnaire QI
                </a>
            </div>
            <div class="bio-status">
                <div class="status-indicator"></div>
                <span>Systèmes Biologiques Actifs</span>
            </div>
        </div>
    </div>

    <!-- Container Principal -->
    <div class="bio-main-container">
        <!-- Grille principale -->
        <div class="bio-grid">
            <!-- Tests Neurologiques -->
            <div class="bio-card">
                <h2><i class="fas fa-brain"></i> Tests Neurologiques</h2>
                <button class="test-btn" onclick="runNeurologicalTest()">
                    <i class="fas fa-brain"></i>
                    Test Neurologique Complet
                </button>
                <button class="test-btn" onclick="runSynapticTest()">
                    <i class="fas fa-network-wired"></i>
                    Test Synaptique
                </button>
                <button class="test-btn" onclick="runCognitiveTest()">
                    <i class="fas fa-lightbulb"></i>
                    Test Cognitif
                </button>
                <div class="results-section" id="neuro-results" style="display: none;">
                    <h3>Résultats Neurologiques</h3>
                    <div class="result-item">
                        <span class="result-label">Activité Neuronale:</span>
                        <span class="result-value" id="neural-activity">--</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Connexions Synaptiques:</span>
                        <span class="result-value" id="synaptic-connections">--</span>
                    </div>
                </div>
            </div>

            <!-- Tests Cardiovasculaires -->
            <div class="bio-card">
                <h2><i class="fas fa-heartbeat"></i> Tests Cardiovasculaires</h2>
                <button class="test-btn danger" onclick="runCardiacTest()">
                    <i class="fas fa-heartbeat heartbeat"></i>
                    Test Cardiaque
                </button>
                <button class="test-btn danger" onclick="runCirculatoryTest()">
                    <i class="fas fa-tint"></i>
                    Test Circulatoire
                </button>
                <button class="test-btn danger" onclick="runPressureTest()">
                    <i class="fas fa-thermometer-half"></i>
                    Test de Pression
                </button>
                <div class="vital-signs">
                    <div class="vital-item">
                        <div class="vital-value" id="heart-rate">72</div>
                        <div class="vital-label">BPM</div>
                    </div>
                    <div class="vital-item">
                        <div class="vital-value" id="blood-pressure">120/80</div>
                        <div class="vital-label">Tension</div>
                    </div>
                </div>
            </div>

            <!-- Tests Métaboliques -->
            <div class="bio-card">
                <h2><i class="fas fa-flask"></i> Tests Métaboliques</h2>
                <button class="test-btn warning" onclick="runMetabolicTest()">
                    <i class="fas fa-flask"></i>
                    Test Métabolique
                </button>
                <button class="test-btn warning" onclick="runEnzymaticTest()">
                    <i class="fas fa-atom"></i>
                    Test Enzymatique
                </button>
                <button class="test-btn warning" onclick="runHormonalTest()">
                    <i class="fas fa-balance-scale"></i>
                    Test Hormonal
                </button>
                <div class="progress-bar">
                    <div class="progress-fill" id="metabolic-progress" style="width: 0%"></div>
                </div>
                <div class="results-section" id="metabolic-results" style="display: none;">
                    <h3>Résultats Métaboliques</h3>
                    <div class="result-item">
                        <span class="result-label">Métabolisme Basal:</span>
                        <span class="result-value" id="basal-metabolism">--</span>
                    </div>
                </div>
            </div>

            <!-- Tests Immunologiques -->
            <div class="bio-card">
                <h2><i class="fas fa-shield-alt"></i> Tests Immunologiques</h2>
                <button class="test-btn info" onclick="runImmuneTest()">
                    <i class="fas fa-shield-alt"></i>
                    Test Immunitaire
                </button>
                <button class="test-btn info" onclick="runAntibodyTest()">
                    <i class="fas fa-virus"></i>
                    Test d'Anticorps
                </button>
                <button class="test-btn info" onclick="runInflammationTest()">
                    <i class="fas fa-fire"></i>
                    Test d'Inflammation
                </button>
                <div class="vital-signs">
                    <div class="vital-item">
                        <div class="vital-value" id="immune-strength">95%</div>
                        <div class="vital-label">Force Immunitaire</div>
                    </div>
                    <div class="vital-item">
                        <div class="vital-value" id="white-cells">7.2K</div>
                        <div class="vital-label">Globules Blancs</div>
                    </div>
                </div>
            </div>

            <!-- Analyse Génétique -->
            <div class="bio-card">
                <h2><i class="fas fa-dna"></i> Analyse Génétique</h2>
                <button class="test-btn" onclick="runGeneticTest()">
                    <i class="fas fa-dna"></i>
                    Séquençage ADN
                </button>
                <button class="test-btn" onclick="runMutationTest()">
                    <i class="fas fa-random"></i>
                    Détection Mutations
                </button>
                <button class="test-btn" onclick="runExpressionTest()">
                    <i class="fas fa-code"></i>
                    Expression Génique
                </button>
                <div class="results-section" id="genetic-results" style="display: none;">
                    <h3>Profil Génétique</h3>
                    <div class="result-item">
                        <span class="result-label">Intégrité ADN:</span>
                        <span class="result-value" id="dna-integrity">--</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Mutations Détectées:</span>
                        <span class="result-value" id="mutations-count">--</span>
                    </div>
                </div>
            </div>

            <!-- Bilan Global -->
            <div class="bio-card">
                <h2><i class="fas fa-chart-line"></i> Bilan Global</h2>
                <button class="test-btn" onclick="runCompleteAnalysis()">
                    <i class="fas fa-clipboard-check"></i>
                    Analyse Complète
                </button>
                <div class="vital-signs">
                    <div class="vital-item">
                        <div class="vital-value" id="overall-health">98%</div>
                        <div class="vital-label">Santé Globale</div>
                    </div>
                    <div class="vital-item">
                        <div class="vital-value" id="biological-age">25</div>
                        <div class="vital-label">Âge Biologique</div>
                    </div>
                    <div class="vital-item">
                        <div class="vital-value" id="vitality-index">9.2</div>
                        <div class="vital-label">Indice Vitalité</div>
                    </div>
                    <div class="vital-item">
                        <div class="vital-value" id="longevity-score">A+</div>
                        <div class="vital-label">Score Longévité</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isTestRunning = false;
        let testResults = {};

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧬 Tests biologiques initialisés');
            updateVitalSigns();
            setInterval(updateVitalSigns, 3000); // Mise à jour toutes les 3 secondes
        });

        // Tests neurologiques
        async function runNeurologicalTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('🧠 Test neurologique en cours...', 'info');
            
            setTimeout(() => {
                const activity = (95 + Math.random() * 5).toFixed(1) + '%';
                const connections = (717 + Math.floor(Math.random() * 50));
                
                document.getElementById('neural-activity').textContent = activity;
                document.getElementById('synaptic-connections').textContent = connections;
                document.getElementById('neuro-results').style.display = 'block';
                
                showNotification('✅ Test neurologique terminé !', 'success');
                isTestRunning = false;
            }, 3000);
        }

        async function runSynapticTest() {
            if (isTestRunning) return;
            showNotification('⚡ Test synaptique en cours...', 'info');
            setTimeout(() => showNotification('✅ Synapses optimales !', 'success'), 2000);
        }

        async function runCognitiveTest() {
            if (isTestRunning) return;
            showNotification('💡 Test cognitif en cours...', 'info');
            setTimeout(() => showNotification('✅ Fonctions cognitives excellentes !', 'success'), 2500);
        }

        // Tests cardiovasculaires
        async function runCardiacTest() {
            if (isTestRunning) return;
            showNotification('❤️ Test cardiaque en cours...', 'info');
            setTimeout(() => showNotification('✅ Cœur en parfaite santé !', 'success'), 2000);
        }

        async function runCirculatoryTest() {
            if (isTestRunning) return;
            showNotification('🩸 Test circulatoire en cours...', 'info');
            setTimeout(() => showNotification('✅ Circulation optimale !', 'success'), 2500);
        }

        async function runPressureTest() {
            if (isTestRunning) return;
            showNotification('🌡️ Test de pression en cours...', 'info');
            setTimeout(() => showNotification('✅ Pression artérielle normale !', 'success'), 1500);
        }

        // Tests métaboliques
        async function runMetabolicTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('🧪 Test métabolique en cours...', 'info');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.getElementById('metabolic-progress').style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('basal-metabolism').textContent = '1850 kcal/jour';
                    document.getElementById('metabolic-results').style.display = 'block';
                    showNotification('✅ Métabolisme optimal !', 'success');
                    isTestRunning = false;
                }
            }, 200);
        }

        async function runEnzymaticTest() {
            if (isTestRunning) return;
            showNotification('⚗️ Test enzymatique en cours...', 'info');
            setTimeout(() => showNotification('✅ Activité enzymatique normale !', 'success'), 2000);
        }

        async function runHormonalTest() {
            if (isTestRunning) return;
            showNotification('⚖️ Test hormonal en cours...', 'info');
            setTimeout(() => showNotification('✅ Équilibre hormonal parfait !', 'success'), 2500);
        }

        // Tests immunologiques
        async function runImmuneTest() {
            if (isTestRunning) return;
            showNotification('🛡️ Test immunitaire en cours...', 'info');
            setTimeout(() => showNotification('✅ Système immunitaire robuste !', 'success'), 2000);
        }

        async function runAntibodyTest() {
            if (isTestRunning) return;
            showNotification('🦠 Test d\'anticorps en cours...', 'info');
            setTimeout(() => showNotification('✅ Anticorps en quantité optimale !', 'success'), 2500);
        }

        async function runInflammationTest() {
            if (isTestRunning) return;
            showNotification('🔥 Test d\'inflammation en cours...', 'info');
            setTimeout(() => showNotification('✅ Aucune inflammation détectée !', 'success'), 1500);
        }

        // Analyse génétique
        async function runGeneticTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('🧬 Séquençage ADN en cours...', 'info');
            
            setTimeout(() => {
                document.getElementById('dna-integrity').textContent = '99.8%';
                document.getElementById('mutations-count').textContent = '0 pathogènes';
                document.getElementById('genetic-results').style.display = 'block';
                showNotification('✅ Profil génétique excellent !', 'success');
                isTestRunning = false;
            }, 4000);
        }

        async function runMutationTest() {
            if (isTestRunning) return;
            showNotification('🔍 Détection de mutations...', 'info');
            setTimeout(() => showNotification('✅ Aucune mutation pathogène !', 'success'), 3000);
        }

        async function runExpressionTest() {
            if (isTestRunning) return;
            showNotification('📊 Analyse d\'expression génique...', 'info');
            setTimeout(() => showNotification('✅ Expression génique optimale !', 'success'), 3500);
        }

        // Analyse complète
        async function runCompleteAnalysis() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('📋 Analyse biologique complète...', 'info');
            
            setTimeout(() => {
                showNotification('✅ Analyse terminée ! Santé exceptionnelle.', 'success');
                updateVitalSigns();
                isTestRunning = false;
            }, 5000);
        }

        // Mettre à jour les signes vitaux
        function updateVitalSigns() {
            // Rythme cardiaque
            const heartRate = 70 + Math.floor(Math.random() * 10);
            document.getElementById('heart-rate').textContent = heartRate;
            
            // Tension artérielle
            const systolic = 115 + Math.floor(Math.random() * 10);
            const diastolic = 75 + Math.floor(Math.random() * 10);
            document.getElementById('blood-pressure').textContent = `${systolic}/${diastolic}`;
            
            // Force immunitaire
            const immuneStrength = 93 + Math.floor(Math.random() * 7);
            document.getElementById('immune-strength').textContent = immuneStrength + '%';
            
            // Globules blancs
            const whiteCells = (6.8 + Math.random() * 1.2).toFixed(1);
            document.getElementById('white-cells').textContent = whiteCells + 'K';
            
            // Santé globale
            const overallHealth = 96 + Math.floor(Math.random() * 4);
            document.getElementById('overall-health').textContent = overallHealth + '%';
            
            // Indice de vitalité
            const vitalityIndex = (8.8 + Math.random() * 1.2).toFixed(1);
            document.getElementById('vitality-index').textContent = vitalityIndex;
        }

        // Système de notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: ${type === 'warning' ? '#000' : '#fff'};
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
