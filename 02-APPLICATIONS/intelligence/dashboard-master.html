<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau <PERSON> Principal - Lou<PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title .icon {
            font-size: 2.8rem;
            color: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 20px;
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.4);
            border-radius: 25px;
            color: #28a745;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        /* Container principal */
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* Grille principale */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 25px;
            margin-bottom: 30px;
        }

        /* Widgets */
        .widget {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .widget.large {
            grid-column: span 2;
        }

        .widget.tall {
            grid-row: span 2;
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .widget-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .widget-icon {
            font-size: 1.5rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .widget-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-online {
            background: rgba(40, 167, 69, 0.3);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .status-active {
            background: rgba(78, 205, 196, 0.3);
            color: #4ecdc4;
            border: 1px solid #4ecdc4;
        }

        .status-processing {
            background: rgba(255, 193, 7, 0.3);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        /* Métriques */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        /* Graphiques */
        .chart-container {
            height: 200px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
            font-style: italic;
        }

        /* Liste d'activités */
        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #4ecdc4;
        }

        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 3px;
        }

        .activity-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Boutons d'action rapide */
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 20px;
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .widget.large {
                grid-column: span 1;
            }

            .widget.tall {
                grid-row: span 1;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-tachometer-alt icon"></i>
                <h1>Tableau de Bord Principal</h1>
            </div>
            <div class="system-status">
                <div class="status-dot"></div>
                <span>Tous les systèmes opérationnels</span>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/settings-advanced.html" class="nav-btn">
                    <i class="fas fa-cogs"></i>
                    Paramètres
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Grille principale du tableau de bord -->
        <div class="dashboard-grid">
            <!-- Widget Agent Claude -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-robot widget-icon"></i>
                        Agent Claude 4GB
                    </div>
                    <div class="widget-status status-online">EN LIGNE</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="claude-messages">0</div>
                        <div class="metric-label">Messages</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="claude-uptime">100%</div>
                        <div class="metric-label">Disponibilité</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/chat')">
                        <i class="fas fa-comments"></i>
                        Chat
                    </button>
                    <button class="quick-btn" onclick="testClaude()">
                        <i class="fas fa-vial"></i>
                        Test
                    </button>
                </div>
            </div>

            <!-- Widget QI et Cerveau -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-brain widget-icon"></i>
                        QI & Cerveau
                    </div>
                    <div class="widget-status status-active">ACTIF</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="qi-value">235</div>
                        <div class="metric-label">QI</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="neurons-count">145</div>
                        <div class="metric-label">Neurones</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/qi-manager.html')">
                        <i class="fas fa-brain"></i>
                        Gestionnaire QI
                    </button>
                    <button class="quick-btn" onclick="runBiologicalTest()">
                        <i class="fas fa-microscope"></i>
                        Tests Bio
                    </button>
                </div>
            </div>

            <!-- Widget Accélérateurs Kyber -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-bolt widget-icon"></i>
                        Accélérateurs Kyber
                    </div>
                    <div class="widget-status status-active">ACTIFS</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="kyber-active">8</div>
                        <div class="metric-label">Actifs</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="kyber-boost">320%</div>
                        <div class="metric-label">Boost</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/kyber-dashboard.html')">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </button>
                    <button class="quick-btn" onclick="boostKyber()">
                        <i class="fas fa-rocket"></i>
                        Boost
                    </button>
                </div>
            </div>

            <!-- Widget Mémoire Thermique -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-fire widget-icon"></i>
                        Mémoire Thermique
                    </div>
                    <div class="widget-status status-active">ACTIVE</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="memory-zones">5</div>
                        <div class="metric-label">Zones</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="memory-temp">42°C</div>
                        <div class="metric-label">Température</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/futuristic-interface.html')">
                        <i class="fas fa-eye"></i>
                        Visualiser
                    </button>
                    <button class="quick-btn" onclick="openApp('/thermal-memory-dashboard.html')">
                        <i class="fas fa-cogs"></i>
                        Gestion
                    </button>
                </div>
            </div>

            <!-- Widget Navigateur Web -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-globe widget-icon"></i>
                        Navigateur Web
                    </div>
                    <div class="widget-status status-online">PRÊT</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="web-pages">0</div>
                        <div class="metric-label">Pages</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="web-speed">Fast</div>
                        <div class="metric-label">Vitesse</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/web-browser.html')">
                        <i class="fas fa-globe"></i>
                        Naviguer
                    </button>
                    <button class="quick-btn" onclick="openApp('/web-search.html')">
                        <i class="fas fa-search"></i>
                        Recherche
                    </button>
                </div>
            </div>

            <!-- Widget Génération Multimédia -->
            <div class="widget large">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-magic widget-icon"></i>
                        Génération Multimédia
                    </div>
                    <div class="widget-status status-active">ACTIF</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="gen-images">0</div>
                        <div class="metric-label">Images</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="gen-videos">0</div>
                        <div class="metric-label">Vidéos</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="gen-audio">0</div>
                        <div class="metric-label">Audio</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="gen-3d">0</div>
                        <div class="metric-label">3D</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/generation-studio.html')">
                        <i class="fas fa-paint-brush"></i>
                        Studio
                    </button>
                    <button class="quick-btn" onclick="openApp('/multimedia-gallery.html')">
                        <i class="fas fa-images"></i>
                        Galerie
                    </button>
                    <button class="quick-btn" onclick="generateQuickContent()">
                        <i class="fas fa-bolt"></i>
                        Génération Rapide
                    </button>
                </div>
            </div>

            <!-- Widget Activité Récente -->
            <div class="widget tall">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-history widget-icon"></i>
                        Activité Récente
                    </div>
                    <div class="widget-status status-processing">LIVE</div>
                </div>
                <div class="activity-list" id="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Agent Louna démarré</div>
                            <div class="activity-time">Il y a 2 minutes</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Mémoire thermique initialisée</div>
                            <div class="activity-time">Il y a 3 minutes</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Accélérateurs KYBER activés</div>
                            <div class="activity-time">Il y a 4 minutes</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Widget Performance Système -->
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">
                        <i class="fas fa-chart-line widget-icon"></i>
                        Performance Système
                    </div>
                    <div class="widget-status status-online">OPTIMAL</div>
                </div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="cpu-usage">45%</div>
                        <div class="metric-label">CPU</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="memory-usage">68%</div>
                        <div class="metric-label">RAM</div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="openApp('/system-monitor.html')">
                        <i class="fas fa-desktop"></i>
                        Monitoring
                    </button>
                    <button class="quick-btn" onclick="optimizeSystem()">
                        <i class="fas fa-tools"></i>
                        Optimiser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let messageCount = 0;
        let systemStats = {
            claude: { messages: 0, uptime: 100 },
            kyber: { active: 8, boost: 320 },
            memory: { zones: 5, temperature: 42 },
            voice: { sessions: 0, active: false },
            generation: { total: 0, queue: 0 }
        };

        // Ouvrir une application
        function openApp(url) {
            window.open(url, '_blank');
        }

        // Tester Claude
        async function testClaude() {
            try {
                showNotification('🧪 Test de Claude en cours...', 'info');

                const response = await fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'Test de statut - Peux-tu confirmer que tu es opérationnel ?',
                        conversationId: 'dashboard-test'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification('✅ Claude est opérationnel !', 'success');
                    updateClaudeStats();
                } else {
                    showNotification('❌ Erreur de connexion avec Claude', 'error');
                }
            } catch (error) {
                console.error('Erreur test Claude:', error);
                showNotification('❌ Test Claude échoué', 'error');
            }
        }

        // Lancer les tests biologiques
        function runBiologicalTest() {
            showNotification('🧬 Lancement des tests biologiques...', 'info');
            openApp('/biological-tests.html');
        }

        // Boost Kyber
        async function boostKyber() {
            try {
                showNotification('⚡ Boost KYBER en cours...', 'info');

                const response = await fetch('/api/kyber/boost', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    showNotification('🚀 Boost KYBER activé !', 'success');
                    updateKyberStats();
                } else {
                    showNotification('❌ Erreur boost KYBER', 'error');
                }
            } catch (error) {
                console.error('Erreur boost KYBER:', error);
                showNotification('❌ Boost KYBER échoué', 'error');
            }
        }

        // Génération rapide de contenu
        async function generateQuickContent() {
            try {
                showNotification('🎨 Génération rapide en cours...', 'info');

                const response = await fetch('/api/generation/create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'image',
                        prompt: 'Une belle image générée par Louna AI',
                        options: { style: 'artistic' }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification('✨ Contenu généré avec succès !', 'success');
                    updateGenerationStats();
                } else {
                    showNotification('❌ Erreur de génération', 'error');
                }
            } catch (error) {
                console.error('Erreur génération:', error);
                showNotification('❌ Génération échouée', 'error');
            }
        }

        // Optimiser le système
        async function optimizeSystem() {
            try {
                showNotification('🔧 Optimisation système en cours...', 'info');

                const response = await fetch('/api/system/full-optimization', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    showNotification('✅ Système optimisé !', 'success');
                    updateSystemStats();
                } else {
                    showNotification('❌ Erreur d\'optimisation', 'error');
                }
            } catch (error) {
                console.error('Erreur optimisation:', error);
                showNotification('❌ Optimisation échouée', 'error');
            }
        }

        // Mettre à jour les statistiques
        async function updateStats() {
            try {
                // Mettre à jour le QI
                const qiResponse = await fetch('/api/qi/current');
                if (qiResponse.ok) {
                    const qiData = await qiResponse.json();
                    document.getElementById('qi-value').textContent = qiData.qi || 225;
                }

                // Mettre à jour les stats mémoire thermique
                const memoryResponse = await fetch('/api/thermal-memory/stats');
                if (memoryResponse.ok) {
                    const memoryData = await memoryResponse.json();
                    document.getElementById('memory-zones').textContent = memoryData.zones || 5;
                    document.getElementById('memory-temp').textContent = Math.round(memoryData.temperature || 42) + '°C';
                }

                // Mettre à jour les stats KYBER
                const kyberResponse = await fetch('/api/kyber/status');
                if (kyberResponse.ok) {
                    const kyberData = await kyberResponse.json();
                    document.getElementById('kyber-active').textContent = kyberData.active || 8;
                    document.getElementById('kyber-boost').textContent = (kyberData.boost || 320) + '%';
                }

                // Mettre à jour les stats système
                updateSystemStats();

            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }

        function updateClaudeStats() {
            systemStats.claude.messages++;
            document.getElementById('claude-messages').textContent = systemStats.claude.messages;
        }

        function updateKyberStats() {
            systemStats.kyber.boost += 50;
            document.getElementById('kyber-boost').textContent = systemStats.kyber.boost + '%';
        }

        function updateGenerationStats() {
            systemStats.generation.total++;
            document.getElementById('gen-images').textContent = systemStats.generation.total;
        }

        function updateSystemStats() {
            const cpuUsage = Math.floor(Math.random() * 30) + 30; // 30-60%
            const memoryUsage = Math.floor(Math.random() * 40) + 50; // 50-90%

            document.getElementById('cpu-usage').textContent = cpuUsage + '%';
            document.getElementById('memory-usage').textContent = memoryUsage + '%';
        }

        // Ajouter une activité
        function addActivity(icon, title, time = 'À l\'instant') {
            const activityList = document.getElementById('activity-list');
            const newActivity = document.createElement('div');
            newActivity.className = 'activity-item';
            newActivity.innerHTML = `
                <div class="activity-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${title}</div>
                    <div class="activity-time">${time}</div>
                </div>
            `;

            activityList.insertBefore(newActivity, activityList.firstChild);

            // Garder seulement les 5 dernières activités
            while (activityList.children.length > 5) {
                activityList.removeChild(activityList.lastChild);
            }
        }

        // Notifications
        function showNotification(message, type = 'info') {
            // Créer la notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: 500;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Supprimer après 3 secondes
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);

            // Ajouter à l'activité
            addActivity('fas fa-bell', message.replace(/[🧪⚡🎨🔧✅❌🚀✨]/g, '').trim());
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Tableau de bord principal initialisé');

            // Mettre à jour les stats toutes les 5 secondes
            updateStats();
            setInterval(updateStats, 5000);

            // Mettre à jour les stats système toutes les 2 secondes
            setInterval(updateSystemStats, 2000);

            showNotification('🎯 Tableau de bord chargé !', 'success');
        });

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Raccourcis clavier
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey) {
                switch(event.key) {
                    case '1':
                        event.preventDefault();
                        openApp('/chat');
                        break;
                    case '2':
                        event.preventDefault();
                        openApp('/kyber-dashboard.html');
                        break;
                    case '3':
                        event.preventDefault();
                        openApp('/futuristic-interface.html');
                        break;
                    case 't':
                        event.preventDefault();
                        testClaude();
                        break;
                }
            }
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
