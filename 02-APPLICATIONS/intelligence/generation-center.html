<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centre de Génération - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }

        .hero-section {
            margin-bottom: 60px;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 700;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-size: 20px;
            color: #ccc;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .generation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .generation-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .generation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(233, 30, 99, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .generation-card:hover {
            transform: translateY(-10px);
            border-color: #ff69b4;
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
        }

        .generation-card:hover::before {
            opacity: 1;
        }

        .card-content {
            position: relative;
            z-index: 1;
        }

        .card-icon {
            font-size: 64px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ff69b4;
        }

        .card-description {
            font-size: 16px;
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-features {
            list-style: none;
            text-align: left;
            margin-bottom: 30px;
        }

        .card-features li {
            font-size: 14px;
            color: #aaa;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-features li i {
            color: #4caf50;
            width: 16px;
        }

        .card-button {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
        }

        .stats-title {
            font-size: 24px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 14px;
            color: #ccc;
        }

        .completed {
            opacity: 0.6;
            pointer-events: none;
        }

        .completed .card-button {
            background: linear-gradient(135deg, #666, #444);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 36px;
            }
            
            .hero-subtitle {
                font-size: 18px;
            }
            
            .generation-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .generation-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-magic"></i>
            Centre de Génération IA
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/web-search.html" class="nav-btn">
                <i class="fas fa-search"></i>
                Recherche
            </a>
            <a href="/face-recognition.html" class="nav-btn">
                <i class="fas fa-user-check"></i>
                Reconnaissance
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Section héro -->
        <div class="hero-section">
            <h1 class="hero-title">Génération de Contenu IA</h1>
            <p class="hero-subtitle">
                Créez du contenu multimédia illimité avec l'intelligence artificielle avancée de Louna. 
                Images, vidéos, musique et plus encore, tout à portée de clic.
            </p>
        </div>

        <!-- Grille des types de génération -->
        <div class="generation-grid">
            <!-- Génération d'images -->
            <div class="generation-card" onclick="window.location.href='/image-generator-simple.html'">
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <h3 class="card-title">Génération d'Images</h3>
                    <p class="card-description">
                        Créez des images uniques et personnalisées à partir de descriptions textuelles avec notre IA avancée.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> Styles artistiques variés</li>
                        <li><i class="fas fa-check"></i> Résolutions jusqu'à 1024x1024</li>
                        <li><i class="fas fa-check"></i> Galerie et sauvegarde</li>
                        <li><i class="fas fa-check"></i> Export haute qualité</li>
                    </ul>
                    <a href="/image-generator-simple.html" class="card-button">
                        <i class="fas fa-magic"></i>
                        Créer des Images
                    </a>
                </div>
            </div>

            <!-- Génération de vidéos -->
            <div class="generation-card" onclick="window.location.href='/video-generator.html'">
                <div class="badge">LTX</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3 class="card-title">Génération Vidéo LTX</h3>
                    <p class="card-description">
                        Générez des vidéos cinématiques avec la technologie LTX de pointe pour des résultats époustouflants.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> Technologie LTX avancée</li>
                        <li><i class="fas fa-check"></i> Résolutions jusqu'à 4K</li>
                        <li><i class="fas fa-check"></i> Durées personnalisables</li>
                        <li><i class="fas fa-check"></i> Styles cinématiques</li>
                    </ul>
                    <a href="/video-generator.html" class="card-button">
                        <i class="fas fa-video"></i>
                        Créer des Vidéos
                    </a>
                </div>
            </div>

            <!-- Génération de musique -->
            <div class="generation-card completed">
                <div class="badge">COMPLET</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="card-title">Génération Musicale</h3>
                    <p class="card-description">
                        Composez de la musique originale dans tous les styles avec notre IA musicale avancée.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-clock"></i> Tous genres musicaux</li>
                        <li><i class="fas fa-clock"></i> Instruments virtuels</li>
                        <li><i class="fas fa-clock"></i> Export professionnel</li>
                        <li><i class="fas fa-clock"></i> Arrangements automatiques</li>
                    </ul>
                    <button class="card-button" onclick="window.location.href='/music-generator.html'">
                        <i class="fas fa-clock"></i>
                        Créer de la Musique
                    </button>
                </div>
            </div>

            <!-- Génération de modèles 3D -->
            <div class="generation-card completed">
                <div class="badge">COMPLET</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 class="card-title">Modèles 3D</h3>
                    <p class="card-description">
                        Créez des modèles 3D détaillés et texturés à partir de descriptions ou d'images de référence.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-clock"></i> Modélisation automatique</li>
                        <li><i class="fas fa-clock"></i> Textures réalistes</li>
                        <li><i class="fas fa-clock"></i> Export multi-formats</li>
                        <li><i class="fas fa-clock"></i> Optimisation pour jeux</li>
                    </ul>
                    <button class="card-button" onclick="window.location.href='/3d-generator.html'">
                        <i class="fas fa-cube"></i>
                        Créer des Modèles 3D
                    </button>
                </div>
            </div>
        </div>

        <!-- Section statistiques -->
        <div class="stats-section">
            <h2 class="stats-title">
                <i class="fas fa-chart-line"></i>
                Statistiques de Génération
            </h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalImages">0</div>
                    <div class="stat-label">Images Générées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalVideos">0</div>
                    <div class="stat-label">Vidéos Créées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Qualité IA</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">Créativité</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Centre de génération initialisé');
            loadStatistics();
        });

        function loadStatistics() {
            // Charger les statistiques depuis le localStorage
            const savedImages = localStorage.getItem('lounaGeneratedImages');
            const savedVideos = localStorage.getItem('lounaGeneratedVideos');
            
            let imageCount = 0;
            let videoCount = 0;
            
            if (savedImages) {
                try {
                    const images = JSON.parse(savedImages);
                    imageCount = images.length;
                } catch (e) {
                    console.warn('Erreur lecture images:', e);
                }
            }
            
            if (savedVideos) {
                try {
                    const videos = JSON.parse(savedVideos);
                    videoCount = videos.length;
                } catch (e) {
                    console.warn('Erreur lecture vidéos:', e);
                }
            }
            
            // Mettre à jour l'affichage avec animation
            animateNumber('totalImages', imageCount);
            animateNumber('totalVideos', videoCount);
        }

        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000; // 2 secondes
            const startTime = Date.now();
            
            function updateNumber() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Fonction d'easing pour une animation plus fluide
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);
                
                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            updateNumber();
        }

        // Ajouter des effets visuels aux cartes
        document.querySelectorAll('.generation-card:not(.completed)').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
