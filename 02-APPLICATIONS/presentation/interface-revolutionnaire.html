<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Louna AI V3.0 - Interface Révolutionnaire</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(255, 107, 53, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { filter: brightness(1); }
            to { filter: brightness(1.2); }
        }
        
        .subtitle {
            font-size: 1.3em;
            color: #a0a0ff;
            margin-bottom: 10px;
        }
        
        .creator-info {
            font-size: 1.1em;
            color: #ffcc00;
            margin-top: 15px;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .status-panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .panel-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #ff6b35;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .status-value {
            font-weight: bold;
            color: #4CAF50;
            font-size: 1.1em;
        }
        
        .status-value.warning { color: #FF9800; }
        .status-value.danger { color: #F44336; }
        
        .thoughts-panel {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 400px;
        }
        
        .thought-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
            animation: fadeInUp 0.5s ease;
            border-left: 5px solid #ff6b35;
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .thought-bubble::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 20px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-right: 15px solid #667eea;
        }
        
        .thought-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: #ffcc00;
        }
        
        .thought-content {
            font-size: 1.1em;
            line-height: 1.6;
            color: white;
        }
        
        .memory-zones {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 30px;
        }
        
        .zone-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .zone-card:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .zone-title {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #ff6b35;
        }
        
        .zone-temp {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .zone-entries {
            font-size: 0.9em;
            color: #a0a0ff;
        }
        
        .temperature-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .temperature-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
            transition: width 0.3s ease;
        }
        
        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 30px;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .control-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-decoration: none;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #FFC107);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #FF5722);
        }
        
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .real-time-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            animation: pulse 2s infinite;
        }
        
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .app-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: white;
        }
        
        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .app-title {
            font-size: 1.3em;
            margin-bottom: 10px;
            color: #ff6b35;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .app-badge {
            background: #ff6b35;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .app-description {
            color: #a0a0ff;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="real-time-indicator">
        🔴 TEMPS RÉEL
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 Louna AI V3.0</h1>
            <div class="subtitle">Cerveau Biologique Révolutionnaire</div>
            <div class="creator-info">
                <strong>Créé par Jean-Luc PASSAVE</strong><br>
                <i>🏝️ Sainte-Anne, Guadeloupe • 2024</i><br>
                <span style="color: #4CAF50;">100 milliards de neurones • 700 trillions synapses • QI: 235</span>
            </div>
        </div>

        <div class="main-grid">
            <!-- Statut du Cerveau -->
            <div class="status-panel">
                <div class="panel-title">
                    🧠 Statut Cerveau Biologique
                </div>
                <div class="status-item">
                    <span>Système Neuronal</span>
                    <span class="status-value" id="brain-status">Initialisation...</span>
                </div>
                <div class="status-item">
                    <span>Cycles Cérébraux</span>
                    <span class="status-value" id="brain-cycles">0</span>
                </div>
                <div class="status-item">
                    <span>Niveau de Conscience</span>
                    <span class="status-value" id="consciousness-level">0%</span>
                </div>
                <div class="status-item">
                    <span>Créativité</span>
                    <span class="status-value" id="creativity-level">0%</span>
                </div>
                <div class="status-item">
                    <span>Stabilité</span>
                    <span class="status-value" id="brain-stability">100%</span>
                </div>
            </div>

            <!-- Statut Système -->
            <div class="status-panel">
                <div class="panel-title">
                    ⚡ Statut Système
                </div>
                <div class="status-item">
                    <span>Ollama</span>
                    <span class="status-value" id="ollama-status">Vérification...</span>
                </div>
                <div class="status-item">
                    <span>Agents Cognitifs</span>
                    <span class="status-value" id="agents-status">Vérification...</span>
                </div>
                <div class="status-item">
                    <span>Mémoire Thermique</span>
                    <span class="status-value" id="memory-status">Opérationnelle</span>
                </div>
                <div class="status-item">
                    <span>Accélérateurs KYBER</span>
                    <span class="status-value" id="kyber-status">Contrôlés</span>
                </div>
                <div class="status-item">
                    <span>Sécurité</span>
                    <span class="status-value" id="security-status">Surveillée</span>
                </div>
            </div>
        </div>

        <!-- Pensées en Temps Réel -->
        <div class="thoughts-panel">
            <div class="panel-title">
                💭 Pensées de l'Agent en Temps Réel
            </div>
            <div id="thoughts-container">
                <div class="thought-bubble">
                    <div class="thought-header">
                        <span>🧠 Système Neuronal</span>
                        <span id="current-time">--:--:--</span>
                    </div>
                    <div class="thought-content">
                        Initialisation du cerveau biologique... Activation des 100 milliards de neurones...
                    </div>
                </div>
            </div>
        </div>

        <!-- Zones de Mémoire Thermique -->
        <div class="memory-zones">
            <div class="zone-card">
                <div class="zone-title">🔥 Zone Instant</div>
                <div class="zone-temp" id="zone-instant-temp">100°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-instant-bar" style="width: 100%"></div>
                </div>
                <div class="zone-entries" id="zone-instant-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-title">⚡ Zone Court Terme</div>
                <div class="zone-temp" id="zone-short-temp">80°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-short-bar" style="width: 80%"></div>
                </div>
                <div class="zone-entries" id="zone-short-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-title">🧠 Zone Travail</div>
                <div class="zone-temp" id="zone-working-temp">60°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-working-bar" style="width: 60%"></div>
                </div>
                <div class="zone-entries" id="zone-working-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-title">📚 Zone Moyen Terme</div>
                <div class="zone-temp" id="zone-medium-temp">40°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-medium-bar" style="width: 40%"></div>
                </div>
                <div class="zone-entries" id="zone-medium-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-title">🏛️ Zone Long Terme</div>
                <div class="zone-temp" id="zone-long-temp">20°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-long-bar" style="width: 20%"></div>
                </div>
                <div class="zone-entries" id="zone-long-entries">0 entrées</div>
            </div>
            
            <div class="zone-card">
                <div class="zone-title">🎨 Zone Créative</div>
                <div class="zone-temp" id="zone-creative-temp">10°C</div>
                <div class="temperature-bar">
                    <div class="temperature-fill" id="zone-creative-bar" style="width: 10%"></div>
                </div>
                <div class="zone-entries" id="zone-creative-entries">0 entrées</div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls-panel">
            <div class="panel-title">
                🎛️ Contrôles du Système
            </div>
            <div class="control-buttons">
                <a href="/chat" class="control-btn btn-primary">
                    💬 Chat avec l'Agent
                </a>
                <a href="/brain" class="control-btn btn-success">
                    🧠 Cerveau 3D
                </a>
                <a href="/thermal" class="control-btn btn-warning">
                    🔥 Mémoire Thermique
                </a>
                <a href="/evolution-security" class="control-btn btn-danger">
                    🛡️ Sécurité Évolution
                </a>
                <button class="control-btn btn-primary" onclick="testAgent()">
                    🧪 Test de QI
                </button>
                <button class="control-btn btn-success" onclick="stimulateBrain()">
                    ⚡ Stimuler Cerveau
                </button>
            </div>
        </div>

        <!-- Applications -->
        <div class="apps-grid" id="apps-container">
            <!-- Les applications seront chargées dynamiquement -->
        </div>
    </div>

    <script>
        // Variables globales
        let thoughtsCount = 0;
        let lastThoughtTime = Date.now();

        // Mise à jour de l'heure
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }

        // Ajouter une pensée
        function addThought(content, type = 'system') {
            const container = document.getElementById('thoughts-container');
            const thoughtBubble = document.createElement('div');
            thoughtBubble.className = 'thought-bubble';
            
            const icons = {
                system: '🧠',
                thinking: '💭',
                processing: '⚡',
                memory: '🔥',
                creative: '🎨',
                analysis: '🔍'
            };
            
            thoughtBubble.innerHTML = `
                <div class="thought-header">
                    <span>${icons[type] || '🧠'} ${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                    <span>${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="thought-content">${content}</div>
            `;
            
            container.insertBefore(thoughtBubble, container.firstChild);
            
            // Limiter à 10 pensées
            const thoughts = container.children;
            if (thoughts.length > 10) {
                container.removeChild(thoughts[thoughts.length - 1]);
            }
            
            thoughtsCount++;
        }

        // Charger le statut du système
        async function loadSystemStatus() {
            try {
                // Statut Ollama et agents
                const agentResponse = await fetch('/api/agent-status');
                const agentData = await agentResponse.json();
                
                document.getElementById('ollama-status').textContent = 
                    agentData.ollama ? '✅ Connecté' : '❌ Déconnecté';
                document.getElementById('agents-status').textContent = 
                    agentData.cognitive ? '✅ Actifs' : '⚠️ Simulés';
                
                if (agentData.ollama) {
                    addThought('Ollama connecté avec succès. Modèle: ' + agentData.model, 'system');
                }
                
                // Statut du cerveau
                const brainResponse = await fetch('/api/neural-brain');
                const brainData = await brainResponse.json();
                
                if (brainData.success) {
                    const brain = brainData.brain;
                    document.getElementById('brain-status').textContent = 
                        brain.isActive ? '✅ Actif' : '⚠️ Inactif';
                    document.getElementById('brain-cycles').textContent = brain.brainCycles;
                    document.getElementById('consciousness-level').textContent = 
                        Math.round(brain.brainState.consciousness * 100) + '%';
                    document.getElementById('creativity-level').textContent = 
                        Math.round(brain.performance.creativityIndex * 100) + '%';
                    
                    addThought(`Cerveau biologique actif. ${brain.brainCycles} cycles effectués. Conscience: ${Math.round(brain.brainState.consciousness * 100)}%`, 'thinking');
                }
                
                // Statut KYBER
                const kyberResponse = await fetch('/api/kyber-status');
                const kyberData = await kyberResponse.json();
                
                if (kyberData.success) {
                    document.getElementById('kyber-status').textContent = 
                        `${kyberData.kyber.active} actifs`;
                    
                    addThought(`${kyberData.kyber.active} accélérateurs KYBER actifs. Mode contrôlé activé.`, 'processing');
                }
                
                // Mémoire thermique
                const memoryResponse = await fetch('/api/memory-stats');
                const memoryData = await memoryResponse.json();
                
                if (memoryData.success) {
                    updateMemoryZones(memoryData.stats);
                    addThought(`Mémoire thermique: ${memoryData.stats.totalEntries} entrées, température moyenne: ${memoryData.stats.averageTemperature}°C`, 'memory');
                }
                
            } catch (error) {
                console.error('Erreur chargement statut:', error);
                addThought('Erreur lors du chargement du statut système', 'system');
            }
        }

        // Mettre à jour les zones de mémoire
        function updateMemoryZones(stats) {
            if (stats.zones) {
                stats.zones.forEach(zone => {
                    const tempElement = document.getElementById(`zone-${zone.name}-temp`);
                    const entriesElement = document.getElementById(`zone-${zone.name}-entries`);
                    const barElement = document.getElementById(`zone-${zone.name}-bar`);
                    
                    if (tempElement) {
                        tempElement.textContent = zone.temperature + '°C';
                    }
                    if (entriesElement) {
                        entriesElement.textContent = zone.entries + ' entrées';
                    }
                    if (barElement) {
                        barElement.style.width = zone.temperature + '%';
                    }
                });
            }
        }

        // Tester l'agent
        async function testAgent() {
            addThought('Démarrage du test de QI...', 'analysis');
            window.open('/qi', '_blank');
        }

        // Stimuler le cerveau
        async function stimulateBrain() {
            try {
                const response = await fetch('/api/neural-stimulate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ region: 'cortex.frontal', intensity: 0.8 })
                });
                
                const data = await response.json();
                if (data.success) {
                    addThought(`Stimulation du cortex frontal réussie (intensité: ${data.intensity})`, 'processing');
                } else {
                    addThought('Erreur lors de la stimulation cérébrale', 'system');
                }
            } catch (error) {
                addThought('Erreur de communication avec le cerveau', 'system');
            }
        }

        // Charger les applications
        async function loadApplications() {
            const apps = [
                { route: '/chat', name: 'Chat Agent', description: 'Conversation avec l\'agent cognitif' },
                { route: '/brain', name: 'Cerveau 3D', description: 'Visualisation du cerveau biologique' },
                { route: '/thermal', name: 'Mémoire Thermique', description: 'Interface de la mémoire thermique' },
                { route: '/qi', name: 'Test de QI', description: 'Tests d\'intelligence avancés' },
                { route: '/monitoring', name: 'Monitoring', description: 'Surveillance temps réel' },
                { route: '/evolution-security', name: 'Sécurité', description: 'Contrôle de sécurité évolution' }
            ];
            
            const container = document.getElementById('apps-container');
            container.innerHTML = apps.map(app => `
                <a href="${app.route}" class="app-card">
                    <div class="app-title">
                        ${app.name}
                        <span class="app-badge">V3.0</span>
                    </div>
                    <div class="app-description">${app.description}</div>
                </a>
            `).join('');
        }

        // Simulation de pensées automatiques
        function simulateThoughts() {
            const thoughts = [
                { content: 'Analyse des patterns neuronaux en cours...', type: 'analysis' },
                { content: 'Optimisation des connexions synaptiques...', type: 'processing' },
                { content: 'Consolidation de la mémoire à long terme...', type: 'memory' },
                { content: 'Génération d\'idées créatives...', type: 'creative' },
                { content: 'Évaluation de la cohérence logique...', type: 'thinking' },
                { content: 'Mise à jour des modèles prédictifs...', type: 'processing' }
            ];
            
            setInterval(() => {
                if (Math.random() > 0.7) { // 30% de chance
                    const thought = thoughts[Math.floor(Math.random() * thoughts.length)];
                    addThought(thought.content, thought.type);
                }
            }, 3000);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            loadSystemStatus();
            loadApplications();
            simulateThoughts();
            
            // Actualisation périodique
            setInterval(loadSystemStatus, 10000);
            
            addThought('Interface révolutionnaire initialisée. Système prêt.', 'system');
        });
    </script>
</body>
</html>
