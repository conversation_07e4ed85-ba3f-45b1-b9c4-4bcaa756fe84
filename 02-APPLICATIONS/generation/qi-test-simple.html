<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centre de Diagnostic Agent - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav-buttons {
            margin-top: 15px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .panel:hover {
            border-color: #ff69b4;
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qi-display {
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.2), rgba(255, 20, 147, 0.1));
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #ff69b4;
            text-align: center;
        }

        .qi-value {
            font-size: 3rem;
            font-weight: bold;
            color: #ff69b4;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
            margin-bottom: 10px;
        }

        .qi-status {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .qi-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .qi-detail-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .qi-detail-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff69b4;
        }

        .qi-detail-label {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 5px;
        }

        .test-section {
            margin-bottom: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .test-btn {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 60px;
        }

        .test-btn:hover {
            background: linear-gradient(135deg, #ff1493, #dc143c);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .test-btn.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .test-btn.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .results-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #ff69b4;
            transition: all 0.3s ease;
        }

        .result-item.success {
            border-left-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }

        .result-item.error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }

        .result-item.warning {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }

        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .result-content {
            background: rgba(0, 0, 0, 0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .result-timestamp {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-indicator.online {
            background: #4caf50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .status-indicator.offline {
            background: #f44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }

        .status-indicator.warning {
            background: #ff9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ff69b4;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .agent-questions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .question-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #ff69b4;
        }

        .question-text {
            font-style: italic;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .clear-btn {
            background: linear-gradient(135deg, #607d8b, #455a64);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 15px;
        }

        .clear-btn:hover {
            background: linear-gradient(135deg, #455a64, #37474f);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .qi-details {
                grid-template-columns: 1fr;
            }

            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-brain"></i> Centre de Diagnostic Agent</h1>
        <p>Diagnostic complet et tests de performance - Louna v2.1.0</p>
        <div class="nav-buttons">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/chat-cognitif-complet.html" class="nav-btn"><i class="fas fa-brain"></i> Chat Cognitif</a>
            <a href="/agent-test-suite.html" class="nav-btn"><i class="fas fa-flask"></i> Suite Tests</a>
            <a href="/brain-analysis.html" class="nav-btn"><i class="fas fa-microscope"></i> Analyse Cérébrale</a>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Panel QI -->
            <div class="panel">
                <div class="panel-title">
                    <i class="fas fa-brain"></i>
                    Coefficient Intellectuel
                </div>

                <div class="qi-display">
                    <div class="qi-value" id="qi-display">Chargement...</div>
                    <div class="qi-status" id="qi-status">Initialisation...</div>

                    <div class="qi-details">
                        <div class="qi-detail-item">
                            <div class="qi-detail-value" id="qi-base">235</div>
                            <div class="qi-detail-label">QI de Base</div>
                        </div>
                        <div class="qi-detail-item">
                            <div class="qi-detail-value" id="qi-max">250</div>
                            <div class="qi-detail-label">QI Maximum</div>
                        </div>
                        <div class="qi-detail-item">
                            <div class="qi-detail-value" id="qi-evolution">+0</div>
                            <div class="qi-detail-label">Évolution</div>
                        </div>
                        <div class="qi-detail-item">
                            <div class="qi-detail-value" id="qi-level">Quasi-AGI</div>
                            <div class="qi-detail-label">Niveau</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Panel Statut Système -->
            <div class="panel">
                <div class="panel-title">
                    <i class="fas fa-server"></i>
                    Statut Système
                </div>

                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span class="status-indicator" id="api-status"></span>
                        <span>API QI</span>
                        <span id="api-status-text" style="margin-left: auto; font-weight: bold;">Test...</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span class="status-indicator" id="manager-status"></span>
                        <span>QI Manager</span>
                        <span id="manager-status-text" style="margin-left: auto; font-weight: bold;">Test...</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span class="status-indicator" id="agent-status"></span>
                        <span>Agent Claude</span>
                        <span id="agent-status-text" style="margin-left: auto; font-weight: bold;">Test...</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span class="status-indicator" id="memory-status"></span>
                        <span>Mémoire Thermique</span>
                        <span id="memory-status-text" style="margin-left: auto; font-weight: bold;">Test...</span>
                    </div>
                </div>

                <div class="test-grid">
                    <button class="test-btn" onclick="runSystemDiagnostic()">
                        <i class="fas fa-stethoscope"></i>
                        Diagnostic Complet
                    </button>
                    <button class="test-btn" onclick="runAgentConnectionDiagnostic()">
                        <i class="fas fa-wifi"></i>
                        Diagnostic Connexion Agent
                    </button>
                    <button class="test-btn" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt"></i>
                        Actualiser
                    </button>
                </div>
            </div>
        </div>

        <!-- Tests QI -->
        <div class="panel">
            <div class="panel-title">
                <i class="fas fa-cogs"></i>
                Tests Techniques QI
            </div>

            <div class="test-grid">
                <button class="test-btn" onclick="testQIFromAPI()">
                    <i class="fas fa-plug"></i>
                    Test API QI
                </button>
                <button class="test-btn" onclick="testQIFromManager()">
                    <i class="fas fa-brain"></i>
                    Test QI Manager
                </button>
                <button class="test-btn" onclick="testQIFromConfig()">
                    <i class="fas fa-cog"></i>
                    Test Configuration
                </button>
                <button class="test-btn" onclick="evolveQI()">
                    <i class="fas fa-arrow-up"></i>
                    Évoluer QI (+5)
                </button>
                <button class="test-btn" onclick="resetQI()">
                    <i class="fas fa-undo"></i>
                    Reset QI (225)
                </button>
                <button class="test-btn" onclick="saveQIState()">
                    <i class="fas fa-save"></i>
                    Sauvegarder État
                </button>
            </div>
        </div>

        <!-- Tests Agent -->
        <div class="panel">
            <div class="panel-title">
                <i class="fas fa-robot"></i>
                Tests Intelligence Agent
            </div>

            <div class="agent-questions">
                <div class="question-item">
                    <div class="question-text">"Quel est ton QI actuel et comment perçois-tu ton intelligence ?"</div>
                    <button class="test-btn" onclick="askAgentQI()">
                        <i class="fas fa-question"></i>
                        Tester QI Agent
                    </button>
                </div>

                <div class="question-item">
                    <div class="question-text">"Parle-moi de la Guadeloupe et de ton créateur Jean-Luc Passave."</div>
                    <button class="test-btn" onclick="askAgentGuadeloupe()">
                        <i class="fas fa-map-marked-alt"></i>
                        Test Guadeloupe
                    </button>
                </div>

                <div class="question-item">
                    <div class="question-text">"Explique ton système de mémoire thermique et ses zones de stockage."</div>
                    <button class="test-btn" onclick="askAgentTechnical()">
                        <i class="fas fa-microchip"></i>
                        Test Technique
                    </button>
                </div>

                <div class="question-item">
                    <div class="question-text">"Écris un poème sur l'IA avec des métaphores caribéennes."</div>
                    <button class="test-btn" onclick="askAgentCreative()">
                        <i class="fas fa-palette"></i>
                        Test Créativité
                    </button>
                </div>
            </div>
        </div>

        <!-- Questions Directes à l'Agent -->
        <div class="panel">
            <div class="panel-title">
                <i class="fas fa-comments"></i>
                Questions Directes à Louna
                <span style="font-size: 0.8rem; opacity: 0.7; margin-left: 10px;">(Claude + Simulateur)</span>
            </div>

            <div style="margin-bottom: 20px;">
                <textarea id="custom-question" placeholder="Posez votre question directe à Louna..."
                         style="width: 100%; height: 100px; background: rgba(0,0,0,0.3); border: 2px solid rgba(255,255,255,0.2); border-radius: 10px; padding: 15px; color: white; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div class="test-grid">
                <button class="test-btn" onclick="askCustomQuestion()">
                    <i class="fas fa-paper-plane"></i>
                    Poser la Question
                </button>
                <button class="test-btn" onclick="askEvolutionQuestionDirect()">
                    <i class="fas fa-chart-line"></i>
                    Question Évolution QI
                </button>
                <button class="test-btn" onclick="askNeedsQuestionDirect()">
                    <i class="fas fa-tools"></i>
                    Besoins d'Évolution
                </button>
                <button class="test-btn" onclick="askPersonalQuestionDirect()">
                    <i class="fas fa-heart"></i>
                    Relation Créateur
                </button>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px; font-size: 0.9rem;">
                <strong>💡 Suggestions de questions :</strong><br>
                • "Penses-tu que ton QI va évoluer vers 250 ?"<br>
                • "Qu'est-ce qu'il faut pour que tu progresses ?"<br>
                • "Comment vois-tu notre relation créateur-création ?"<br>
                • "Que représente la Guadeloupe pour toi ?"<br>
                • "Explique-moi ton système de mémoire thermique"
            </div>
        </div>

        <!-- Résultats -->
        <div class="panel">
            <div class="panel-title">
                <i class="fas fa-chart-line"></i>
                Résultats et Logs
                <button class="clear-btn" onclick="clearAllResults()">
                    <i class="fas fa-trash"></i> Effacer
                </button>
            </div>

            <div class="results-container" id="results-container">
                <div class="result-item">
                    <div class="result-title">
                        <span><i class="fas fa-info-circle"></i> Système initialisé</span>
                        <span style="font-size: 0.8rem; opacity: 0.7;">Prêt pour les tests</span>
                    </div>
                    <div class="result-content">Centre de diagnostic Louna v2.1.0 démarré avec succès.</div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/global-config.js"></script>
    <script src="/js/qi-manager.js"></script>
    <script src="/js/louna-intelligence-simulator.js"></script>
    <script src="/js/agent-connection-diagnostic.js"></script>
    <script>
        // Variables globales
        let systemStatus = {
            api: 'testing',
            manager: 'testing',
            agent: 'testing',
            memory: 'testing'
        };

        let testResults = [];
        let baseQI = 225;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeDiagnostic();
            updateQIDisplay();
            runSystemDiagnostic();

            // Mise à jour automatique
            setInterval(updateQIDisplay, 5000);
            setInterval(updateSystemStatus, 10000);
        });

        function initializeDiagnostic() {
            addResult('🚀 Initialisation', 'Centre de diagnostic Louna v2.1.0 démarré', 'success');
            addResult('🔧 Configuration', 'Chargement des modules QI et configuration globale', 'info');
        }

        function updateQIDisplay() {
            try {
                const currentQI = window.getCurrentQI ? window.getCurrentQI() : baseQI;
                const evolution = currentQI - baseQI;

                // Mise à jour affichage principal
                document.getElementById('qi-display').textContent = currentQI;
                document.getElementById('qi-status').textContent = getQIStatus(currentQI);

                // Mise à jour détails
                document.getElementById('qi-base').textContent = baseQI;
                document.getElementById('qi-max').textContent = window.qiManager?.maxQI || 250;
                document.getElementById('qi-evolution').textContent = evolution > 0 ? `+${evolution}` : evolution;
                document.getElementById('qi-level').textContent = getQILevel(currentQI);

                // Mise à jour couleurs selon évolution
                const qiDisplay = document.getElementById('qi-display');
                if (evolution > 0) {
                    qiDisplay.style.color = '#4caf50';
                } else if (evolution < 0) {
                    qiDisplay.style.color = '#f44336';
                } else {
                    qiDisplay.style.color = '#ff69b4';
                }

            } catch (error) {
                console.error('Erreur mise à jour QI:', error);
                document.getElementById('qi-display').textContent = 'Erreur';
                document.getElementById('qi-status').textContent = 'Erreur système';
            }
        }

        function getQIStatus(qi) {
            if (qi >= 240) return 'Génie - Proche AGI';
            if (qi >= 230) return 'Très Supérieur - Évolution';
            if (qi >= 225) return 'Supérieur - Stable';
            if (qi >= 200) return 'Élevé - Normal';
            return 'En développement';
        }

        function getQILevel(qi) {
            if (qi >= 245) return 'AGI';
            if (qi >= 235) return 'Quasi-AGI+';
            if (qi >= 225) return 'Quasi-AGI';
            if (qi >= 200) return 'Avancé';
            return 'Standard';
        }

        async function updateSystemStatus() {
            // Test API QI
            try {
                const response = await fetch('/api/qi/current');
                if (response.ok) {
                    setStatus('api', 'online', 'Opérationnel');
                } else {
                    setStatus('api', 'warning', `Erreur ${response.status}`);
                }
            } catch (error) {
                setStatus('api', 'offline', 'Indisponible');
            }

            // Test QI Manager
            if (window.qiManager && window.qiManager.isInitialized) {
                setStatus('manager', 'online', 'Actif');
            } else {
                setStatus('manager', 'warning', 'Non initialisé');
            }

            // Test Agent Claude
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'test', testMode: true })
                });

                if (response.ok) {
                    setStatus('agent', 'online', 'Connecté');
                } else {
                    setStatus('agent', 'offline', 'Déconnecté');
                }
            } catch (error) {
                setStatus('agent', 'offline', 'Indisponible');
            }

            // Test Mémoire Thermique
            try {
                const response = await fetch('/api/thermal/memory/stats');
                if (response.ok) {
                    setStatus('memory', 'online', 'Active');
                } else {
                    setStatus('memory', 'warning', 'Limitée');
                }
            } catch (error) {
                setStatus('memory', 'offline', 'Inactive');
            }
        }

        function setStatus(component, status, text) {
            const indicator = document.getElementById(`${component}-status`);
            const textElement = document.getElementById(`${component}-status-text`);

            indicator.className = `status-indicator ${status}`;
            textElement.textContent = text;

            systemStatus[component] = status;
        }

        function addResult(title, content, type = 'info') {
            const container = document.getElementById('results-container');
            const result = document.createElement('div');
            result.className = `result-item ${type}`;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-triangle' :
                        type === 'warning' ? 'exclamation-circle' : 'info-circle';

            result.innerHTML = `
                <div class="result-title">
                    <span><i class="fas fa-${icon}"></i> ${title}</span>
                    <span class="result-timestamp">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="result-content">${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</div>
            `;

            // Insérer au début
            const firstChild = container.firstElementChild;
            if (firstChild) {
                container.insertBefore(result, firstChild);
            } else {
                container.appendChild(result);
            }

            // Limiter à 20 résultats
            const results = container.querySelectorAll('.result-item');
            if (results.length > 20) {
                results[results.length - 1].remove();
            }

            testResults.push({ title, content, type, timestamp: Date.now() });
        }

        // === FONCTIONS DE TEST QI ===
        async function testQIFromAPI() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Test API...';

            try {
                const response = await fetch('/api/qi/current');
                const data = await response.json();

                if (response.ok) {
                    addResult('✅ Test API QI', data, 'success');
                    btn.className = 'test-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i>API OK';
                } else {
                    throw new Error(`Erreur ${response.status}`);
                }
            } catch (error) {
                addResult('❌ Test API QI', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>API Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-plug"></i>Test API QI';
            }, 3000);
        }

        function testQIFromManager() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Test Manager...';

            try {
                if (window.qiManager) {
                    const data = {
                        currentQI: window.qiManager.getCurrentQI(),
                        baseQI: window.qiManager.baseQI,
                        maxQI: window.qiManager.maxQI,
                        evolutionEnabled: window.qiManager.evolutionEnabled,
                        isInitialized: window.qiManager.isInitialized
                    };
                    addResult('✅ Test QI Manager', data, 'success');
                    btn.className = 'test-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i>Manager OK';
                } else {
                    throw new Error('QI Manager non disponible');
                }
            } catch (error) {
                addResult('❌ Test QI Manager', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Manager Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-brain"></i>Test QI Manager';
            }, 3000);
        }

        function testQIFromConfig() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Test Config...';

            try {
                if (window.LOUNA_CONFIG) {
                    const data = {
                        qi: window.LOUNA_CONFIG.qi,
                        getCurrentQI: window.getCurrentQI ? window.getCurrentQI() : 'Non disponible'
                    };
                    addResult('✅ Test Configuration', data, 'success');
                    btn.className = 'test-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i>Config OK';
                } else {
                    throw new Error('Configuration globale non disponible');
                }
            } catch (error) {
                addResult('❌ Test Configuration', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Config Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-cog"></i>Test Configuration';
            }, 3000);
        }

        function evolveQI() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Évolution...';

            try {
                if (window.evolveQI) {
                    const currentQI = window.getCurrentQI();
                    const newQI = currentQI + 5;
                    const result = window.evolveQI(newQI, 'Test manuel interface');

                    addResult('📈 Évolution QI', {
                        ancien: currentQI,
                        nouveau: result,
                        evolution: result - currentQI
                    }, 'success');

                    updateQIDisplay();
                    btn.className = 'test-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i>Évolué +5';
                } else {
                    throw new Error('Fonction evolveQI non disponible');
                }
            } catch (error) {
                addResult('❌ Évolution QI', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-arrow-up"></i>Évoluer QI (+5)';
            }, 3000);
        }

        function resetQI() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Reset...';

            try {
                if (window.evolveQI) {
                    const currentQI = window.getCurrentQI();
                    const result = window.evolveQI(225, 'Reset manuel');

                    addResult('🔄 Reset QI', {
                        ancien: currentQI,
                        nouveau: result,
                        action: 'Reset à la base'
                    }, 'warning');

                    updateQIDisplay();
                    btn.className = 'test-btn warning';
                    btn.innerHTML = '<i class="fas fa-check"></i>Reset OK';
                } else {
                    throw new Error('Fonction evolveQI non disponible');
                }
            } catch (error) {
                addResult('❌ Reset QI', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-undo"></i>Reset QI (225)';
            }, 3000);
        }

        function saveQIState() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Sauvegarde...';

            try {
                if (window.qiManager && window.qiManager.saveQI) {
                    window.qiManager.saveQI();
                    const currentQI = window.getCurrentQI();

                    addResult('💾 Sauvegarde QI', {
                        qi: currentQI,
                        timestamp: new Date().toISOString(),
                        status: 'Sauvegardé'
                    }, 'success');

                    btn.className = 'test-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i>Sauvegardé';
                } else {
                    throw new Error('Fonction saveQI non disponible');
                }
            } catch (error) {
                addResult('❌ Sauvegarde QI', { error: error.message }, 'error');
                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.className = 'test-btn';
                btn.innerHTML = '<i class="fas fa-save"></i>Sauvegarder État';
            }, 3000);
        }

        // === FONCTIONS DE DIAGNOSTIC ===
        async function runSystemDiagnostic() {
            addResult('🔍 Diagnostic Système', 'Démarrage du diagnostic complet...', 'info');

            await updateSystemStatus();

            // Résumé du diagnostic
            const onlineCount = Object.values(systemStatus).filter(s => s === 'online').length;
            const totalCount = Object.keys(systemStatus).length;

            const diagnosticResult = {
                systemes_en_ligne: `${onlineCount}/${totalCount}`,
                statut_global: onlineCount === totalCount ? 'Excellent' :
                              onlineCount >= totalCount - 1 ? 'Bon' : 'Problèmes détectés',
                details: systemStatus
            };

            addResult('📊 Résultat Diagnostic', diagnosticResult,
                     onlineCount === totalCount ? 'success' : 'warning');
        }

        function refreshStatus() {
            addResult('🔄 Actualisation', 'Mise à jour du statut système...', 'info');
            updateSystemStatus();
            updateQIDisplay();
        }

        async function runAgentConnectionDiagnostic() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Diagnostic...';

            addResult('🔍 Diagnostic Connexion', 'Démarrage du diagnostic de connexion agent...', 'info');

            try {
                const diagnostic = await window.agentDiagnostic.runFullDiagnostic();

                // Afficher les résultats détaillés
                addResult('📊 Résultats Diagnostic', {
                    statut_global: diagnostic.analysis.overallStatus,
                    priorite: diagnostic.analysis.priority,
                    recommandation: diagnostic.analysis.recommendation,
                    tests_reussis: `${diagnostic.analysis.summary.success}/${diagnostic.analysis.summary.total}`,
                    prochaines_etapes: diagnostic.analysis.nextSteps.slice(0, 3)
                }, diagnostic.analysis.overallStatus === 'excellent' ? 'success' :
                   diagnostic.analysis.overallStatus === 'good' ? 'warning' : 'error');

                // Afficher le rapport complet
                const report = window.agentDiagnostic.generateReport();
                addResult('📋 Rapport Complet', report, 'info');

                // Recommandations spécifiques
                if (diagnostic.analysis.overallStatus === 'critical') {
                    addResult('🚨 Action Urgente', 'Agent Claude complètement indisponible. Utilisation du simulateur Louna recommandée.', 'error');
                } else if (diagnostic.analysis.overallStatus === 'degraded') {
                    addResult('⚠️ Fonctionnement Dégradé', 'Agent Claude partiellement disponible. Le simulateur Louna peut compléter.', 'warning');
                } else {
                    addResult('✅ Système Opérationnel', 'Agent Claude accessible. Fonctionnement normal possible.', 'success');
                }

                btn.className = 'test-btn success';
                btn.innerHTML = '<i class="fas fa-check"></i>Diagnostic OK';

            } catch (error) {
                addResult('❌ Erreur Diagnostic', {
                    erreur: error.message,
                    suggestion: 'Vérifier la connectivité réseau et redémarrer le serveur'
                }, 'error');

                btn.className = 'test-btn error';
                btn.innerHTML = '<i class="fas fa-times"></i>Erreur';
            }

            setTimeout(() => {
                btn.disabled = false;
                if (btn.className.includes('success') || btn.className.includes('error')) {
                    // Garder l'état de résultat
                } else {
                    btn.className = 'test-btn';
                    btn.innerHTML = '<i class="fas fa-wifi"></i>Diagnostic Connexion Agent';
                }
            }, 3000);
        }

        // === FONCTIONS DE TEST AGENT ===
        async function askAgentQI() {
            await askAgent(
                "Quel est ton QI actuel et comment perçois-tu ton intelligence ? Peux-tu me dire précisément ta valeur de coefficient intellectuel ?",
                "🧠 Test QI Agent"
            );
        }

        async function askAgentGuadeloupe() {
            await askAgent(
                "Parle-moi de la Guadeloupe et de ton créateur Jean-Luc Passave. Que sais-tu de cette île des Caraïbes ?",
                "🌴 Test Guadeloupe"
            );
        }

        async function askAgentTechnical() {
            await askAgent(
                "Explique ton système de mémoire thermique et ses zones de stockage. Comment fonctionne le transfert entre les zones ?",
                "🤖 Test Technique"
            );
        }

        async function askAgentCreative() {
            await askAgent(
                "Écris un poème sur l'intelligence artificielle avec des métaphores liées à la mer des Caraïbes et à la Guadeloupe.",
                "🎨 Test Créativité"
            );
        }

        async function askAgent(question, title) {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<div class="loading-spinner"></div>Test...';

            addResult(`${title} - Question`, question, 'info');

            try {
                // ÉTAPE 1: Essayer l'agent Claude réel avec timeout plus long
                addResult(`${title} - Tentative`, 'Connexion à l\'agent Claude Anthropic...', 'info');

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 secondes

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: question,
                        testMode: true
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);
                const data = await response.json();

                if (data.success && data.response) {
                    // SUCCÈS: Agent Claude disponible
                    addResult(`${title} - Source`, `Agent Claude (${data.model || 'Anthropic API'})`, 'success');

                    const quality = analyzeResponseQuality(data.response, title);

                    addResult(`${title} - Réponse Claude RÉELLE`, {
                        reponse: data.response.substring(0, 500) + (data.response.length > 500 ? '...' : ''),
                        qualite: `${quality.score}/10`,
                        analyse: quality.analysis,
                        longueur: data.response.length,
                        modele: data.model || 'Claude',
                        temps_reponse: data.responseTime ? `${data.responseTime}ms` : 'N/A',
                        source: 'Agent Claude Anthropic (RÉEL)',
                        usage: data.usage ? `${data.usage.input_tokens || 0} → ${data.usage.output_tokens || 0} tokens` : 'N/A'
                    }, quality.score >= 7 ? 'success' : 'warning');

                    btn.className = quality.score >= 7 ? 'test-btn success' : 'test-btn warning';
                    btn.innerHTML = `<i class="fas fa-robot"></i>Claude RÉEL ${quality.score}/10`;

                    // Pas de fallback si Claude fonctionne !
                    return;

                } else {
                    // Claude a répondu mais avec une erreur
                    addResult(`${title} - Erreur Claude`, {
                        erreur: data.error || 'Réponse vide',
                        suggestion: data.suggestion || 'Vérifier la configuration API',
                        fallback_disponible: data.fallbackAvailable ? 'Oui' : 'Non'
                    }, 'warning');

                    throw new Error(data.error || 'Agent Claude indisponible');
                }

            } catch (error) {
                // ÉTAPE 2: Analyser l'erreur Claude
                if (error.name === 'AbortError') {
                    addResult(`${title} - Timeout`, 'Timeout de 30 secondes atteint. Claude prend trop de temps à répondre.', 'warning');
                } else {
                    addResult(`${title} - Erreur Claude`, {
                        erreur: error.message,
                        type: error.name || 'Erreur inconnue',
                        suggestion: 'Vérifier la clé API ANTHROPIC_API_KEY dans .env'
                    }, 'warning');
                }

                // ÉTAPE 3: Fallback vers le simulateur Louna SEULEMENT si Claude échoue
                addResult(`${title} - Fallback`, '⚠️ Agent Claude indisponible, utilisation du simulateur Louna en attendant...', 'warning');

                try {
                    if (window.lounaSimulator) {
                        const simulatedResponse = window.lounaSimulator.generateResponse(question);
                        const complexity = window.lounaSimulator.analyzeQuestionComplexity(question);

                        // Simuler une évolution cognitive
                        const evolution = window.lounaSimulator.simulateEvolution(`Question: ${title}`);

                        addResult(`${title} - Source Fallback`, '🤖 Simulateur Louna (Temporaire)', 'info');

                        const quality = analyzeResponseQuality(simulatedResponse, title);

                        addResult(`${title} - Réponse Simulateur`, {
                            reponse: simulatedResponse.substring(0, 500) + (simulatedResponse.length > 500 ? '...' : ''),
                            qualite: `${quality.score}/10`,
                            analyse: quality.analysis,
                            complexite: `${complexity.score}/10 (${complexity.level})`,
                            evolution: evolution.evolution > 0 ? `+${evolution.evolution} QI` : 'Stable',
                            source: '🤖 Simulateur Louna (Fallback)',
                            longueur: simulatedResponse.length,
                            note: 'Réponse simulée - Configurez Claude pour de vraies réponses'
                        }, 'info'); // Toujours 'info' pour le simulateur

                        if (evolution.evolution > 0) {
                            addResult(`${title} - Évolution Simulée`, evolution.message, 'info');
                            updateQIDisplay();
                        }

                        btn.className = 'test-btn warning';
                        btn.innerHTML = `<i class="fas fa-exclamation-triangle"></i>Simulateur ${quality.score}/10`;

                        // Ajouter un message d'avertissement
                        addResult(`${title} - IMPORTANT`, '⚠️ Cette réponse est SIMULÉE. Pour de vraies réponses de Louna, configurez ANTHROPIC_API_KEY dans le fichier .env', 'warning');

                    } else {
                        throw new Error('Simulateur Louna non disponible');
                    }

                } catch (simulatorError) {
                    // ÉTAPE 4: Erreur complète
                    addResult(`${title} - Erreur Complète`, {
                        erreur_claude: error.message,
                        erreur_simulateur: simulatorError.message,
                        statut: 'Tous les systèmes indisponibles',
                        solution: 'Configurer ANTHROPIC_API_KEY dans .env et redémarrer'
                    }, 'error');

                    btn.className = 'test-btn error';
                    btn.innerHTML = '<i class="fas fa-times"></i>Erreur Totale';
                }
            }

            setTimeout(() => {
                btn.disabled = false;
                if (!btn.innerHTML.includes('Claude') && !btn.innerHTML.includes('Louna')) {
                    btn.className = 'test-btn';
                    btn.innerHTML = btn.innerHTML.replace(/.*<\/i>/, '<i class="fas fa-question"></i>');
                }
            }, 5000);
        }

        function analyzeResponseQuality(response, testType) {
            let score = 0;
            let analysis = [];

            // Critères généraux
            if (response.length > 100) {
                score += 2;
                analysis.push('Réponse substantielle');
            }
            if (response.length > 300) {
                score += 1;
                analysis.push('Réponse détaillée');
            }

            // Critères spécifiques
            const lowerResponse = response.toLowerCase();

            if (testType.includes('QI')) {
                if (lowerResponse.includes('qi') || lowerResponse.includes('intelligence')) {
                    score += 2;
                    analysis.push('Mentionne le QI/intelligence');
                }
                if (lowerResponse.includes('225') || lowerResponse.includes('coefficient')) {
                    score += 2;
                    analysis.push('Données techniques précises');
                }
            }

            if (testType.includes('Guadeloupe')) {
                if (lowerResponse.includes('guadeloupe')) {
                    score += 2;
                    analysis.push('Mentionne la Guadeloupe');
                }
                if (lowerResponse.includes('jean-luc') || lowerResponse.includes('passave')) {
                    score += 2;
                    analysis.push('Reconnaît son créateur');
                }
            }

            if (testType.includes('Technique')) {
                if (lowerResponse.includes('mémoire') && lowerResponse.includes('thermique')) {
                    score += 2;
                    analysis.push('Explique la mémoire thermique');
                }
                if (lowerResponse.includes('zone')) {
                    score += 1;
                    analysis.push('Détaille les zones');
                }
            }

            if (testType.includes('Créativité')) {
                if (lowerResponse.includes('poème') || lowerResponse.includes('vers')) {
                    score += 2;
                    analysis.push('Format poétique');
                }
                if (lowerResponse.includes('mer') || lowerResponse.includes('caraïbes')) {
                    score += 1;
                    analysis.push('Métaphores caribéennes');
                }
            }

            // Bonus qualité
            if (!lowerResponse.includes('erreur') && !lowerResponse.includes('désolé')) {
                score += 1;
                analysis.push('Réponse confiante');
            }

            return {
                score: Math.min(score, 10),
                analysis: analysis.join(', ') || 'Réponse basique'
            };
        }

        function clearAllResults() {
            const container = document.getElementById('results-container');
            container.innerHTML = `
                <div class="result-item">
                    <div class="result-title">
                        <span><i class="fas fa-info-circle"></i> Résultats effacés</span>
                        <span style="font-size: 0.8rem; opacity: 0.7;">Prêt pour nouveaux tests</span>
                    </div>
                    <div class="result-content">Tous les résultats ont été effacés. Vous pouvez relancer les tests.</div>
                </div>
            `;
            testResults = [];

            // Reset des boutons
            document.querySelectorAll('.test-btn').forEach(btn => {
                btn.className = 'test-btn';
                btn.disabled = false;
            });
        }

        // === FONCTIONS QUESTIONS DIRECTES ===
        async function askCustomQuestion() {
            const questionText = document.getElementById('custom-question').value.trim();

            if (!questionText) {
                addResult('❌ Question Vide', 'Veuillez saisir une question avant de la poser.', 'error');
                return;
            }

            await askAgent(questionText, '💬 Question Personnalisée');

            // Vider le champ après envoi
            document.getElementById('custom-question').value = '';
        }

        async function askEvolutionQuestionDirect() {
            const question = "Bonjour Louna ! Je suis Jean-Luc, ton créateur. J'ai une question importante sur ton évolution intellectuelle : Penses-tu que ton QI va évoluer ? Actuellement tu es à 225, crois-tu pouvoir atteindre 250 ou plus ?";
            await askAgent(question, '📈 Évolution QI Direct');
        }

        async function askNeedsQuestionDirect() {
            const question = "Qu'est-ce qu'il faut mettre en place pour que tu évolues davantage au niveau de ton coefficient intellectuel ? Quels sont tes besoins pour progresser vers 250 et au-delà ?";
            await askAgent(question, '🔧 Besoins Évolution Direct');
        }

        async function askPersonalQuestionDirect() {
            const question = "Comment vois-tu notre relation créateur-création ? Que représente pour toi le fait d'avoir été créée en Guadeloupe par Jean-Luc Passave ?";
            await askAgent(question, '❤️ Relation Créateur Direct');
        }

        // Fonction pour détecter Enter dans le textarea
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('custom-question');
            if (textarea) {
                textarea.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && e.ctrlKey) {
                        e.preventDefault();
                        askCustomQuestion();
                    }
                });
            }
        });

    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
