<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Studio de Génération - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title .icon {
            font-size: 2.5rem;
            color: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        /* Container principal */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* Grille des outils de génération */
        .generation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .generation-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .generation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
        }

        .generation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }

        .card-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .input-field {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .textarea-field {
            min-height: 80px;
            resize: vertical;
        }

        .generate-btn {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-area {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 100px;
            display: none;
        }

        .result-area.show {
            display: block;
        }

        .result-content {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.5;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Section de statut */
        .status-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }

        .status-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
            text-align: center;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .status-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .status-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .generation-grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-magic icon"></i>
                <h1>Studio de Génération</h1>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/kyber-dashboard.html" class="nav-btn">
                    <i class="fas fa-bolt"></i>
                    Kyber
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Section de statut -->
        <div class="status-section">
            <h2 class="status-title">Statut du Studio</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="total-generations">0</div>
                    <div class="status-label">Générations Totales</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="active-models">4</div>
                    <div class="status-label">Modèles Actifs</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="queue-size">0</div>
                    <div class="status-label">File d'Attente</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="success-rate">100%</div>
                    <div class="status-label">Taux de Succès</div>
                </div>
            </div>
        </div>

        <!-- Grille des outils de génération -->
        <div class="generation-grid">
            <!-- Génération d'images -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-image card-icon"></i>
                    <h3 class="card-title">Génération d'Images</h3>
                </div>
                <p class="card-description">
                    Créez des images uniques à partir de descriptions textuelles avec l'IA générative.
                </p>
                <div class="input-group">
                    <label class="input-label">Description de l'image</label>
                    <textarea class="input-field textarea-field" id="image-prompt" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Style</label>
                    <select class="input-field" id="image-style">
                        <option value="realistic">Réaliste</option>
                        <option value="artistic">Artistique</option>
                        <option value="cartoon">Cartoon</option>
                        <option value="abstract">Abstrait</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generateImage()">
                    <i class="fas fa-magic"></i>
                    Générer Image
                </button>
                <div class="result-area" id="image-result">
                    <div class="result-content"></div>
                </div>
            </div>

            <!-- Génération de vidéos -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-video card-icon"></i>
                    <h3 class="card-title">Génération de Vidéos</h3>
                </div>
                <p class="card-description">
                    Créez des vidéos courtes et des animations à partir de vos idées.
                </p>
                <div class="input-group">
                    <label class="input-label">Concept vidéo</label>
                    <textarea class="input-field textarea-field" id="video-prompt" placeholder="Décrivez la vidéo que vous souhaitez créer..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Durée</label>
                    <select class="input-field" id="video-duration">
                        <option value="5">5 secondes</option>
                        <option value="10">10 secondes</option>
                        <option value="15">15 secondes</option>
                        <option value="30">30 secondes</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generateVideo()">
                    <i class="fas fa-play"></i>
                    Générer Vidéo
                </button>
                <div class="result-area" id="video-result">
                    <div class="result-content"></div>
                </div>
            </div>

            <!-- Génération de musique -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-music card-icon"></i>
                    <h3 class="card-title">Génération de Musique</h3>
                </div>
                <p class="card-description">
                    Composez de la musique originale avec l'IA en spécifiant le style et l'ambiance.
                </p>
                <div class="input-group">
                    <label class="input-label">Description musicale</label>
                    <textarea class="input-field textarea-field" id="music-prompt" placeholder="Décrivez le style de musique souhaité..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Genre</label>
                    <select class="input-field" id="music-genre">
                        <option value="ambient">Ambient</option>
                        <option value="electronic">Électronique</option>
                        <option value="classical">Classique</option>
                        <option value="jazz">Jazz</option>
                        <option value="rock">Rock</option>
                        <option value="cinematic">Cinématique</option>
                    </select>
                </div>
                <div class="input-group">
                    <label class="input-label">Durée</label>
                    <select class="input-field" id="music-duration">
                        <option value="30">30 secondes</option>
                        <option value="60">1 minute</option>
                        <option value="120">2 minutes</option>
                        <option value="180">3 minutes</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generateMusic()">
                    <i class="fas fa-headphones"></i>
                    Générer Musique
                </button>
                <div class="result-area" id="music-result">
                    <div class="result-content"></div>
                </div>
            </div>

            <!-- Génération de modèles 3D -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-cube card-icon"></i>
                    <h3 class="card-title">Génération 3D</h3>
                </div>
                <p class="card-description">
                    Créez des modèles 3D et des objets virtuels à partir de descriptions textuelles.
                </p>
                <div class="input-group">
                    <label class="input-label">Description de l'objet 3D</label>
                    <textarea class="input-field textarea-field" id="model3d-prompt" placeholder="Décrivez l'objet 3D que vous souhaitez créer..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Complexité</label>
                    <select class="input-field" id="model3d-complexity">
                        <option value="low">Faible (rapide)</option>
                        <option value="medium">Moyenne</option>
                        <option value="high">Élevée (détaillé)</option>
                        <option value="ultra">Ultra (très détaillé)</option>
                    </select>
                </div>
                <div class="input-group">
                    <label class="input-label">Format de sortie</label>
                    <select class="input-field" id="model3d-format">
                        <option value="obj">OBJ</option>
                        <option value="fbx">FBX</option>
                        <option value="gltf">GLTF</option>
                        <option value="stl">STL</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generate3DModel()">
                    <i class="fas fa-shapes"></i>
                    Générer Modèle 3D
                </button>
                <div class="result-area" id="model3d-result">
                    <div class="result-content"></div>
                </div>
            </div>

            <!-- Génération de code -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-code card-icon"></i>
                    <h3 class="card-title">Génération de Code</h3>
                </div>
                <p class="card-description">
                    Générez du code dans différents langages de programmation selon vos besoins.
                </p>
                <div class="input-group">
                    <label class="input-label">Description de la fonctionnalité</label>
                    <textarea class="input-field textarea-field" id="code-prompt" placeholder="Décrivez ce que le code doit faire..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Langage</label>
                    <select class="input-field" id="code-language">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="csharp">C#</option>
                        <option value="php">PHP</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                    </select>
                </div>
                <div class="input-group">
                    <label class="input-label">Style de code</label>
                    <select class="input-field" id="code-style">
                        <option value="clean">Propre et commenté</option>
                        <option value="optimized">Optimisé</option>
                        <option value="beginner">Débutant-friendly</option>
                        <option value="advanced">Avancé</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generateCode()">
                    <i class="fas fa-terminal"></i>
                    Générer Code
                </button>
                <div class="result-area" id="code-result">
                    <div class="result-content"></div>
                </div>
            </div>

            <!-- Génération de texte créatif -->
            <div class="generation-card">
                <div class="card-header">
                    <i class="fas fa-feather-alt card-icon"></i>
                    <h3 class="card-title">Génération de Texte</h3>
                </div>
                <p class="card-description">
                    Créez des histoires, poèmes, articles et autres contenus textuels créatifs.
                </p>
                <div class="input-group">
                    <label class="input-label">Sujet ou thème</label>
                    <textarea class="input-field textarea-field" id="text-prompt" placeholder="Décrivez le sujet ou donnez un début de texte..."></textarea>
                </div>
                <div class="input-group">
                    <label class="input-label">Type de texte</label>
                    <select class="input-field" id="text-type">
                        <option value="story">Histoire courte</option>
                        <option value="poem">Poème</option>
                        <option value="article">Article</option>
                        <option value="dialogue">Dialogue</option>
                        <option value="description">Description</option>
                        <option value="script">Script</option>
                    </select>
                </div>
                <div class="input-group">
                    <label class="input-label">Longueur</label>
                    <select class="input-field" id="text-length">
                        <option value="short">Court (100-200 mots)</option>
                        <option value="medium">Moyen (300-500 mots)</option>
                        <option value="long">Long (600-1000 mots)</option>
                    </select>
                </div>
                <button class="generate-btn" onclick="generateText()">
                    <i class="fas fa-pen-fancy"></i>
                    Générer Texte
                </button>
                <div class="result-area" id="text-result">
                    <div class="result-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let totalGenerations = 0;

        // Fonction de génération d'images
        async function generateImage() {
            const prompt = document.getElementById('image-prompt').value;
            const style = document.getElementById('image-style').value;
            const resultArea = document.getElementById('image-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour l\'image');
                return;
            }

            // Afficher la zone de résultat
            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Génération de l\'image en cours...';

            try {
                // Simuler la génération (remplacer par vraie API)
                await new Promise(resolve => setTimeout(resolve, 3000));

                resultContent.innerHTML = `
                    <strong>✅ Image générée avec succès !</strong><br>
                    <em>Prompt:</em> "${prompt}"<br>
                    <em>Style:</em> ${style}<br>
                    <em>Résolution:</em> 1024x1024<br>
                    <em>Format:</em> PNG<br>
                    <small>💡 L'image serait normalement affichée ici avec une vraie API de génération</small>
                `;

                updateStats();
                showNotification('🎨 Image générée avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la génération: ${error.message}`;
                showNotification('❌ Erreur lors de la génération', 'error');
            }
        }

        // Fonction de génération de vidéos
        async function generateVideo() {
            const prompt = document.getElementById('video-prompt').value;
            const duration = document.getElementById('video-duration').value;
            const resultArea = document.getElementById('video-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer un concept pour la vidéo');
                return;
            }

            // Afficher la zone de résultat
            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Génération de la vidéo en cours...';

            try {
                // Simuler la génération (remplacer par vraie API)
                await new Promise(resolve => setTimeout(resolve, 5000));

                resultContent.innerHTML = `
                    <strong>✅ Vidéo générée avec succès !</strong><br>
                    <em>Concept:</em> "${prompt}"<br>
                    <em>Durée:</em> ${duration} secondes<br>
                    <em>Résolution:</em> 1920x1080<br>
                    <em>Format:</em> MP4<br>
                    <small>💡 La vidéo serait normalement affichée ici avec une vraie API de génération</small>
                `;

                updateStats();
                showNotification('🎬 Vidéo générée avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la génération: ${error.message}`;
                showNotification('❌ Erreur lors de la génération', 'error');
            }
        }

        // Fonction de génération de musique
        async function generateMusic() {
            const prompt = document.getElementById('music-prompt').value;
            const genre = document.getElementById('music-genre').value;
            const duration = document.getElementById('music-duration').value;
            const resultArea = document.getElementById('music-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour la musique');
                return;
            }

            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Composition musicale en cours...';

            try {
                await new Promise(resolve => setTimeout(resolve, 4000));

                resultContent.innerHTML = `
                    <strong>🎵 Musique générée avec succès !</strong><br>
                    <em>Description:</em> "${prompt}"<br>
                    <em>Genre:</em> ${genre}<br>
                    <em>Durée:</em> ${duration} secondes<br>
                    <em>Format:</em> MP3 (320kbps)<br>
                    <em>Instruments:</em> Synthétiseurs, Piano, Cordes<br>
                    <small>💡 Le fichier audio serait normalement disponible ici avec une vraie API</small>
                `;

                updateStats();
                showNotification('🎵 Musique composée avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la composition: ${error.message}`;
                showNotification('❌ Erreur lors de la composition', 'error');
            }
        }

        // Fonction de génération de modèles 3D
        async function generate3DModel() {
            const prompt = document.getElementById('model3d-prompt').value;
            const complexity = document.getElementById('model3d-complexity').value;
            const format = document.getElementById('model3d-format').value;
            const resultArea = document.getElementById('model3d-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour le modèle 3D');
                return;
            }

            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Génération du modèle 3D en cours...';

            try {
                const generationTime = complexity === 'ultra' ? 8000 : complexity === 'high' ? 6000 : complexity === 'medium' ? 4000 : 3000;
                await new Promise(resolve => setTimeout(resolve, generationTime));

                const polygonCount = complexity === 'ultra' ? '50,000+' : complexity === 'high' ? '25,000' : complexity === 'medium' ? '10,000' : '5,000';

                resultContent.innerHTML = `
                    <strong>🎯 Modèle 3D généré avec succès !</strong><br>
                    <em>Description:</em> "${prompt}"<br>
                    <em>Complexité:</em> ${complexity}<br>
                    <em>Format:</em> ${format.toUpperCase()}<br>
                    <em>Polygones:</em> ~${polygonCount}<br>
                    <em>Textures:</em> 4K PBR incluses<br>
                    <small>💡 Le modèle 3D serait normalement téléchargeable ici avec une vraie API</small>
                `;

                updateStats();
                showNotification('🎯 Modèle 3D créé avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la génération 3D: ${error.message}`;
                showNotification('❌ Erreur lors de la génération 3D', 'error');
            }
        }

        // Fonction de génération de code
        async function generateCode() {
            const prompt = document.getElementById('code-prompt').value;
            const language = document.getElementById('code-language').value;
            const style = document.getElementById('code-style').value;
            const resultArea = document.getElementById('code-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour le code');
                return;
            }

            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Génération du code en cours...';

            try {
                await new Promise(resolve => setTimeout(resolve, 3000));

                const sampleCode = getSampleCode(language, prompt);

                resultContent.innerHTML = `
                    <strong>💻 Code généré avec succès !</strong><br>
                    <em>Fonctionnalité:</em> "${prompt}"<br>
                    <em>Langage:</em> ${language}<br>
                    <em>Style:</em> ${style}<br>
                    <em>Lignes:</em> ~${Math.floor(Math.random() * 50) + 20}<br>
                    <br>
                    <div style="background: #1e1e1e; padding: 15px; border-radius: 8px; font-family: monospace; color: #d4d4d4; overflow-x: auto;">
                        <pre>${sampleCode}</pre>
                    </div>
                    <small>💡 Code complet disponible avec une vraie API de génération</small>
                `;

                updateStats();
                showNotification('💻 Code généré avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la génération: ${error.message}`;
                showNotification('❌ Erreur lors de la génération', 'error');
            }
        }

        // Fonction de génération de texte
        async function generateText() {
            const prompt = document.getElementById('text-prompt').value;
            const type = document.getElementById('text-type').value;
            const length = document.getElementById('text-length').value;
            const resultArea = document.getElementById('text-result');
            const resultContent = resultArea.querySelector('.result-content');

            if (!prompt.trim()) {
                alert('Veuillez entrer un sujet ou thème');
                return;
            }

            resultArea.classList.add('show');
            resultContent.innerHTML = '<div class="loading-spinner"></div> Rédaction en cours...';

            try {
                await new Promise(resolve => setTimeout(resolve, 3500));

                const wordCount = length === 'long' ? '600-1000' : length === 'medium' ? '300-500' : '100-200';
                const sampleText = getSampleText(type, prompt);

                resultContent.innerHTML = `
                    <strong>📝 Texte généré avec succès !</strong><br>
                    <em>Sujet:</em> "${prompt}"<br>
                    <em>Type:</em> ${type}<br>
                    <em>Longueur:</em> ${wordCount} mots<br>
                    <br>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; line-height: 1.6; text-align: justify;">
                        ${sampleText}
                    </div>
                    <small>💡 Texte complet disponible avec une vraie API de génération</small>
                `;

                updateStats();
                showNotification('📝 Texte rédigé avec succès !', 'success');
            } catch (error) {
                resultContent.innerHTML = `❌ Erreur lors de la rédaction: ${error.message}`;
                showNotification('❌ Erreur lors de la rédaction', 'error');
            }
        }

        // Fonction utilitaire pour générer du code d'exemple
        function getSampleCode(language, prompt) {
            const samples = {
                javascript: `// ${prompt}\nfunction example() {\n    console.log("Hello World!");\n    return true;\n}`,
                python: `# ${prompt}\ndef example():\n    print("Hello World!")\n    return True`,
                java: `// ${prompt}\npublic class Example {\n    public static void main(String[] args) {\n        System.out.println("Hello World!");\n    }\n}`,
                cpp: `// ${prompt}\n#include <iostream>\nint main() {\n    std::cout << "Hello World!" << std::endl;\n    return 0;\n}`
            };
            return samples[language] || `// Code généré pour: ${prompt}\n// Langage: ${language}`;
        }

        // Fonction utilitaire pour générer du texte d'exemple
        function getSampleText(type, prompt) {
            const samples = {
                story: `Il était une fois, dans un monde où ${prompt.toLowerCase()}... Cette histoire commence par une découverte extraordinaire qui va changer le cours des événements...`,
                poem: `Ô ${prompt},\nTu danses dans mes pensées,\nComme une mélodie éternelle\nQui résonne dans l'âme...`,
                article: `${prompt} : Une analyse approfondie\n\nDans le contexte actuel, il est essentiel de comprendre les enjeux liés à ${prompt.toLowerCase()}. Cette analyse propose une perspective nouvelle...`,
                dialogue: `- Que penses-tu de ${prompt} ?\n- C'est fascinant ! Je n'avais jamais considéré cet aspect auparavant.\n- Exactement, c'est ce qui rend le sujet si intéressant...`
            };
            return samples[type] || `Texte généré sur le thème : ${prompt}. Ce contenu explore les différents aspects du sujet avec créativité et originalité...`;
        }

        // Mettre à jour les statistiques
        function updateStats() {
            totalGenerations++;
            document.getElementById('total-generations').textContent = totalGenerations;
            document.getElementById('queue-size').textContent = Math.floor(Math.random() * 3);
        }

        // Système de notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎨 Studio de Génération initialisé');

            // Simulation de mise à jour des statistiques
            setInterval(() => {
                const queueSize = Math.floor(Math.random() * 5);
                document.getElementById('queue-size').textContent = queueSize;
            }, 10000);
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
