<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accélérateurs Kyber - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title .icon {
            font-size: 2.5rem;
            color: #feca57;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        /* Container principal */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* Statistiques globales */
        .global-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #feca57;
            margin-bottom: 10px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Grille des accélérateurs */
        .accelerators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .accelerator-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .accelerator-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #feca57, #ff6b6b, #48dbfb);
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(254, 202, 87, 0.2);
            border-color: rgba(254, 202, 87, 0.5);
        }

        .accelerator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .accelerator-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #ffffff;
        }

        .accelerator-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(40, 167, 69, 0.3);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .status-standby {
            background: rgba(255, 193, 7, 0.3);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .accelerator-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #feca57;
            margin-bottom: 5px;
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .accelerator-progress {
            margin-top: 15px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #feca57, #ff6b6b);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Section de contrôle */
        .control-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 40px;
        }

        .control-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 25px;
            text-align: center;
        }

        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
        }

        .btn-boost {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            color: #333;
        }

        .btn-optimize {
            background: linear-gradient(135deg, #48dbfb, #0abde3);
            color: white;
        }

        .btn-reset {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .global-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .accelerators-grid {
                grid-template-columns: 1fr;
            }

            .control-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-bolt icon"></i>
                <h1>Accélérateurs Kyber</h1>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/chat" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/futuristic-interface.html" class="nav-btn">
                    <i class="fas fa-fire"></i>
                    Mémoire
                </a>
            </div>
        </div>
    </div>

    <!-- Container principal -->
    <div class="container">
        <!-- Statistiques globales -->
        <div class="global-stats">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-bolt"></i></div>
                <div class="stat-value" id="total-accelerators">8</div>
                <div class="stat-label">Accélérateurs Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="stat-value" id="total-boost">320%</div>
                <div class="stat-label">Boost Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-fire"></i></div>
                <div class="stat-value" id="energy-consumption">2.4kW</div>
                <div class="stat-label">Consommation</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                <div class="stat-value" id="efficiency">96%</div>
                <div class="stat-label">Efficacité</div>
            </div>
        </div>

        <!-- Grille des accélérateurs -->
        <div class="accelerators-grid" id="accelerators-grid">
            <!-- Les accélérateurs seront générés dynamiquement -->
        </div>

        <!-- Section de contrôle -->
        <div class="control-section">
            <h2 class="control-title">Contrôles Système</h2>
            <div class="control-buttons">
                <button class="control-btn btn-boost" onclick="boostAll()">
                    <i class="fas fa-rocket"></i>
                    Boost Maximum
                </button>
                <button class="control-btn btn-optimize" onclick="optimize()">
                    <i class="fas fa-cogs"></i>
                    Optimiser
                </button>
                <button class="control-btn btn-reset" onclick="resetAll()">
                    <i class="fas fa-redo"></i>
                    Réinitialiser
                </button>
            </div>
        </div>
    </div>

    <script>
        // Configuration des accélérateurs
        const accelerators = [
            { name: 'Quantum Booster', boost: '45%', efficiency: 98, status: 'active', type: 'quantum' },
            { name: 'Neural Enhancer', boost: '38%', efficiency: 95, status: 'active', type: 'neural' },
            { name: 'Memory Accelerator', boost: '42%', efficiency: 97, status: 'active', type: 'memory' },
            { name: 'Processing Amplifier', boost: '35%', efficiency: 93, status: 'active', type: 'processing' },
            { name: 'Thermal Optimizer', boost: '28%', efficiency: 91, status: 'standby', type: 'thermal' },
            { name: 'QI Enhancer', boost: '52%', efficiency: 99, status: 'active', type: 'intelligence' },
            { name: 'Response Accelerator', boost: '33%', efficiency: 94, status: 'active', type: 'response' },
            { name: 'Learning Booster', boost: '29%', efficiency: 92, status: 'standby', type: 'learning' }
        ];

        // Générer les cartes d'accélérateurs
        function generateAcceleratorCards() {
            const grid = document.getElementById('accelerators-grid');
            grid.innerHTML = '';

            accelerators.forEach((acc, index) => {
                const card = document.createElement('div');
                card.className = 'accelerator-card';
                card.innerHTML = `
                    <div class="accelerator-header">
                        <div class="accelerator-name">${acc.name}</div>
                        <div class="accelerator-status ${acc.status === 'active' ? 'status-active' : 'status-standby'}">
                            ${acc.status === 'active' ? 'ACTIF' : 'VEILLE'}
                        </div>
                    </div>
                    <div class="accelerator-metrics">
                        <div class="metric">
                            <div class="metric-value">${acc.boost}</div>
                            <div class="metric-label">Boost</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${acc.efficiency}%</div>
                            <div class="metric-label">Efficacité</div>
                        </div>
                    </div>
                    <div class="accelerator-progress">
                        <div class="progress-label">
                            <span>Performance</span>
                            <span>${acc.efficiency}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${acc.efficiency}%"></div>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const activeCount = accelerators.filter(acc => acc.status === 'active').length;
            const totalBoost = accelerators
                .filter(acc => acc.status === 'active')
                .reduce((sum, acc) => sum + parseInt(acc.boost), 0);

            document.getElementById('total-accelerators').textContent = activeCount;
            document.getElementById('total-boost').textContent = totalBoost + '%';
            document.getElementById('energy-consumption').textContent = (activeCount * 0.3).toFixed(1) + 'kW';

            const avgEfficiency = accelerators
                .filter(acc => acc.status === 'active')
                .reduce((sum, acc) => sum + acc.efficiency, 0) / activeCount;
            document.getElementById('efficiency').textContent = Math.round(avgEfficiency) + '%';
        }

        // Fonctions de contrôle
        function boostAll() {
            accelerators.forEach(acc => {
                acc.status = 'active';
                acc.efficiency = Math.min(100, acc.efficiency + Math.floor(Math.random() * 5));
            });
            generateAcceleratorCards();
            updateStats();
            showNotification('🚀 Boost maximum activé !', 'success');
        }

        function optimize() {
            accelerators.forEach(acc => {
                if (acc.status === 'active') {
                    acc.efficiency = Math.min(100, acc.efficiency + Math.floor(Math.random() * 3));
                }
            });
            generateAcceleratorCards();
            updateStats();
            showNotification('⚡ Optimisation terminée !', 'info');
        }

        function resetAll() {
            accelerators.forEach((acc, index) => {
                acc.efficiency = 90 + Math.floor(Math.random() * 10);
                acc.status = index < 6 ? 'active' : 'standby';
            });
            generateAcceleratorCards();
            updateStats();
            showNotification('🔄 Système réinitialisé !', 'warning');
        }

        // Système de notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            generateAcceleratorCards();
            updateStats();

            // Mise à jour périodique
            setInterval(() => {
                accelerators.forEach(acc => {
                    if (acc.status === 'active') {
                        acc.efficiency = Math.max(85, Math.min(100,
                            acc.efficiency + (Math.random() - 0.5) * 2
                        ));
                    }
                });
                generateAcceleratorCards();
                updateStats();
            }, 5000);

            console.log('🚀 Dashboard Accélérateurs Kyber initialisé');
        });
    </script>
</body>
</html>
