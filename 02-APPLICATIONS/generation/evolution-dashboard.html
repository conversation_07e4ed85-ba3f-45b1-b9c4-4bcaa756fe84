<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Évolution Temps Réel - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="/js/global-config.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            margin-top: 8px;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #ff69b4;
        }

        .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #4ecdc4;
            text-align: center;
            margin: 15px 0;
        }

        .card-change {
            font-size: 14px;
            text-align: center;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 600;
        }

        .change-positive {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .change-negative {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .change-neutral {
            background: rgba(158, 158, 158, 0.2);
            color: #9e9e9e;
        }

        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
        }

        .alerts-panel {
            grid-column: 1 / -1;
            max-height: 300px;
            overflow-y: auto;
        }

        .alert-item {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
        }

        .alert-critical {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }

        .alert-warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }

        .alert-info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }

        .alert-icon {
            font-size: 16px;
            min-width: 20px;
        }

        .alert-time {
            font-size: 11px;
            opacity: 0.7;
            margin-left: auto;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 20px;
            font-weight: 600;
            color: #4ecdc4;
        }

        .evolution-timeline {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #ff69b4;
        }

        .timeline-time {
            font-size: 11px;
            color: #888;
            margin-right: 15px;
            min-width: 60px;
        }

        .timeline-event {
            flex: 1;
            font-size: 13px;
        }

        .timeline-qi {
            font-size: 14px;
            font-weight: 600;
            color: #4ecdc4;
            margin-left: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            margin: 3px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-chart-line"></i>
            Dashboard Évolution Temps Réel
        </h1>
        <div class="live-indicator">
            <div class="live-dot"></div>
            <span>SURVEILLANCE ACTIVE</span>
        </div>

        <div style="margin-top: 10px;">
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
            <a href="/agent-evolution-analyzer.html" class="nav-btn">
                <i class="fas fa-microscope"></i>
                Analyseur
            </a>
            <a href="/brain-monitoring-complete.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Monitoring
            </a>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-grid">
            <!-- QI Actuel -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-brain"></i>
                        QI Actuel
                    </div>
                </div>
                <div class="card-value" id="currentQI 225</div>
                <div class="card-change change-positive" id="qiChange">+55 depuis le début</div>
                <div class="chart-container">
                    <canvas id="qiChart"></canvas>
                </div>
            </div>

            <!-- Taux d'Évolution -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-rocket"></i>
                        Taux d'Évolution
                    </div>
                </div>
                <div class="card-value" id="evolutionRate">45.8</div>
                <div class="card-change change-positive">points/heure</div>
                <div class="chart-container">
                    <canvas id="rateChart"></canvas>
                </div>
            </div>

            <!-- Activité Neuronale -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-network-wired"></i>
                        Activité Neuronale
                    </div>
                </div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Neurones Actifs</div>
                        <div class="metric-value" id="activeNeurons">78/89</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Efficacité</div>
                        <div class="metric-value" id="neuronEfficiency">94.0%</div>
                    </div>
                </div>
            </div>

            <!-- Système -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-cogs"></i>
                        Performance Système
                    </div>
                </div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">KYBER Actifs</div>
                        <div class="metric-value" id="kyberActive">8/16</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Mémoire</div>
                        <div class="metric-value" id="memoryUsage">61%</div>
                    </div>
                </div>
            </div>

            <!-- Alertes -->
            <div class="dashboard-card alerts-panel">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-bell"></i>
                        Alertes Récentes
                    </div>
                </div>
                <div id="alertsList">
                    <!-- Les alertes seront chargées ici -->
                </div>
            </div>

            <!-- Timeline d'Évolution -->
            <div class="dashboard-card evolution-timeline">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-history"></i>
                        Timeline d'Évolution
                    </div>
                </div>
                <div id="evolutionTimeline">
                    <!-- La timeline sera chargée ici -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let qiChart, rateChart;
        let qiData = [];
        let rateData = [];
        let startTime = Date.now();

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadInitialData();
            startRealTimeUpdates();
            console.log('📊 Dashboard évolution temps réel initialisé');
        });

        // Initialiser les graphiques
        function initializeCharts() {
            // Graphique QI
            const qiCtx = document.getElementById('qiChart').getContext('2d');
            qiChart = new Chart(qiCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'QI',
                        data: [],
                        borderColor: '#ff69b4',
                        backgroundColor: 'rgba(255, 105, 180, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 140,
                            max: 220,
                            grid: { color: 'rgba(255,255,255,0.1)' },
                            ticks: { color: '#fff' }
                        },
                        x: {
                            grid: { color: 'rgba(255,255,255,0.1)' },
                            ticks: { color: '#fff' }
                        }
                    }
                }
            });

            // Graphique taux d'évolution
            const rateCtx = document.getElementById('rateChart').getContext('2d');
            rateChart = new Chart(rateCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Taux',
                        data: [],
                        backgroundColor: 'rgba(78, 205, 196, 0.7)',
                        borderColor: '#4ecdc4',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255,255,255,0.1)' },
                            ticks: { color: '#fff' }
                        },
                        x: {
                            grid: { color: 'rgba(255,255,255,0.1)' },
                            ticks: { color: '#fff' }
                        }
                    }
                }
            });
        }

        // Charger les données initiales
        async function loadInitialData() {
            try {
                const response = await fetch('/api/evolution/monitor/report');
                const data = await response.json();

                updateDashboard(data);
                loadAlerts();
                loadTimeline(data);

            } catch (error) {
                console.error('Erreur chargement données:', error);
            }
        }

        // Mettre à jour le dashboard
        function updateDashboard(data) {
            // Mettre à jour QI
            document.getElementById('currentQI 225;

            const qiChange = data.totalGrowth || 55;
            const qiChangeEl = document.getElementById('qiChange');
            qiChangeEl.textContent = `${qiChange > 0 ? '+' : ''}${qiChange} depuis le début`;
            qiChangeEl.className = `card-change ${qiChange > 0 ? 'change-positive' : qiChange < 0 ? 'change-negative' : 'change-neutral'}`;

            // Mettre à jour taux d'évolution
            const rate = data.averageGrowthRate || 45.8;
            document.getElementById('evolutionRate').textContent = rate.toFixed(1);

            // Mettre à jour métriques
            const metrics = data.currentMetrics || {};
            if (metrics.neurons) {
                document.getElementById('activeNeurons').textContent = `${metrics.neurons.active || 78}/${metrics.neurons.total || 89}`;
                document.getElementById('neuronEfficiency').textContent = `${(metrics.neurons.efficiency || 94.0).toFixed(1)}%`;
            }

            if (metrics.kyber) {
                document.getElementById('kyberActive').textContent = `${metrics.kyber.active || 8}/${metrics.kyber.total || 16}`;
            }

            if (metrics.memory) {
                const memoryPercent = ((metrics.memory.used || 1247) / (metrics.memory.capacity || 2048) * 100).toFixed(0);
                document.getElementById('memoryUsage').textContent = `${memoryPercent}%`;
            }

            // Mettre à jour graphiques
            updateCharts(data);
        }

        // Mettre à jour les graphiques
        function updateCharts(data) {
            const now = new Date();
            const timeLabel = now.toLocaleTimeString();

            // Graphique QI
            qiData.push(data.currentQI 225);
            if (qiData.length > 20) qiData.shift();

            qiChart.data.labels.push(timeLabel);
            if (qiChart.data.labels.length > 20) qiChart.data.labels.shift();

            qiChart.data.datasets[0].data = [...qiData];
            qiChart.update('none');

            // Graphique taux
            const rate = data.averageGrowthRate || Math.random() * 50;
            rateData.push(rate);
            if (rateData.length > 10) rateData.shift();

            rateChart.data.labels.push(timeLabel);
            if (rateChart.data.labels.length > 10) rateChart.data.labels.shift();

            rateChart.data.datasets[0].data = [...rateData];
            rateChart.update('none');
        }

        // Charger les alertes
        async function loadAlerts() {
            try {
                const response = await fetch('/api/evolution/monitor/alerts?limit=10');
                const data = await response.json();

                const alertsList = document.getElementById('alertsList');

                if (data.alerts && data.alerts.length > 0) {
                    alertsList.innerHTML = data.alerts.map(alert => `
                        <div class="alert-item alert-${alert.severity.toLowerCase()}">
                            <i class="fas ${getAlertIcon(alert.type)} alert-icon"></i>
                            <div class="alert-content">
                                <strong>${alert.category}:</strong> ${alert.message}
                            </div>
                            <div class="alert-time">${new Date(alert.timestamp).toLocaleTimeString()}</div>
                        </div>
                    `).join('');
                } else {
                    alertsList.innerHTML = '<div style="text-align: center; padding: 20px; color: rgba(255,255,255,0.5);">Aucune alerte récente</div>';
                }

            } catch (error) {
                console.error('Erreur chargement alertes:', error);
            }
        }

        // Charger la timeline
        function loadTimeline(data) {
            const timeline = document.getElementById('evolutionTimeline');

            if (data.evolutionHistory && data.evolutionHistory.length > 0) {
                const recentEvents = data.evolutionHistory.slice(-10).reverse();

                timeline.innerHTML = recentEvents.map(event => `
                    <div class="timeline-item">
                        <div class="timeline-time">${new Date(event.timestamp).toLocaleTimeString()}</div>
                        <div class="timeline-event">${event.description}</div>
                        <div class="timeline-qi">QI: ${event.qi}</div>
                    </div>
                `).join('');
            } else {
                timeline.innerHTML = '<div style="text-align: center; padding: 20px; color: rgba(255,255,255,0.5);">Aucun événement récent</div>';
            }
        }

        // Obtenir l'icône d'alerte
        function getAlertIcon(type) {
            const icons = {
                'WARNING': 'fa-exclamation-triangle',
                'CRITICAL': 'fa-exclamation-circle',
                'INFO': 'fa-info-circle',
                'PATTERN': 'fa-chart-line',
                'METRIC_UPDATE': 'fa-sync-alt'
            };
            return icons[type] || 'fa-bell';
        }

        // Démarrer les mises à jour temps réel
        function startRealTimeUpdates() {
            // Mise à jour toutes les 5 secondes
            setInterval(async () => {
                try {
                    const response = await fetch('/api/evolution/monitor/report');
                    const data = await response.json();
                    updateDashboard(data);
                } catch (error) {
                    console.error('Erreur mise à jour temps réel:', error);
                }
            }, 5000);

            // Mise à jour des alertes toutes les 10 secondes
            setInterval(() => {
                loadAlerts();
            }, 10000);

            // Simulation de données pour démonstration
            setInterval(() => {
                simulateEvolutionData();
            }, 3000);
        }

        // Simuler des données d'évolution pour la démonstration
        function simulateEvolutionData() {
            const variation = Math.sin(Date.now() / 10000) * 2;
            const currentQI 225 + variation;

            // Mettre à jour les graphiques avec des données simulées
            const now = new Date();
            const timeLabel = now.toLocaleTimeString();

            qiData.push(currentQI);
            if (qiData.length > 20) qiData.shift();

            qiChart.data.labels.push(timeLabel);
            if (qiChart.data.labels.length > 20) qiChart.data.labels.shift();

            qiChart.data.datasets[0].data = [...qiData];
            qiChart.update('none');

            // Simuler le taux d'évolution
            const rate = 40 + Math.random() * 20;
            rateData.push(rate);
            if (rateData.length > 10) rateData.shift();

            rateChart.data.labels.push(timeLabel);
            if (rateChart.data.labels.length > 10) rateChart.data.labels.shift();

            rateChart.data.datasets[0].data = [...rateData];
            rateChart.update('none');

            // Mettre à jour l'affichage du QI
            document.getElementById('currentQI').textContent = Math.round(currentQI);
            document.getElementById('evolutionRate').textContent = rate.toFixed(1);
        }

        console.log('📊 Dashboard évolution temps réel chargé');
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
