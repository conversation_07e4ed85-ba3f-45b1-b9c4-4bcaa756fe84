<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'Images - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .gallery-controls {
            display: flex;
            gap: 10px;
        }

        .gallery-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .gallery-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            flex: 1;
        }

        .image-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 14px;
        }

        .image-info {
            padding: 10px;
        }

        .image-prompt {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }

        .image-meta {
            font-size: 11px;
            color: #999;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff69b4;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            text-align: center;
            padding: 40px;
            color: #888;
            font-style: italic;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #e91e63);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-image"></i>
            Générateur d'Images IA
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Panel de contrôles -->
        <div class="controls-panel">
            <div class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Paramètres de Génération
            </div>

            <form id="imageForm">
                <div class="form-group">
                    <label class="form-label" for="prompt">
                        <i class="fas fa-pen"></i> Description de l'image
                    </label>
                    <textarea
                        id="prompt"
                        class="form-input form-textarea"
                        placeholder="Décrivez l'image que vous voulez générer... (ex: un chat robot futuriste dans un paysage cyberpunk)"
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="style">
                        <i class="fas fa-palette"></i> Style artistique
                    </label>
                    <select id="style" class="form-select">
                        <option value="realistic">Réaliste</option>
                        <option value="artistic">Artistique</option>
                        <option value="anime">Anime/Manga</option>
                        <option value="cyberpunk">Cyberpunk</option>
                        <option value="fantasy">Fantasy</option>
                        <option value="abstract">Abstrait</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="resolution">
                        <i class="fas fa-expand"></i> Résolution
                    </label>
                    <select id="resolution" class="form-select">
                        <option value="512x512">512x512 (Rapide)</option>
                        <option value="768x768">768x768 (Standard)</option>
                        <option value="1024x1024">1024x1024 (Haute qualité)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="quality">
                        <i class="fas fa-star"></i> Qualité
                    </label>
                    <select id="quality" class="form-select">
                        <option value="draft">Brouillon (Rapide)</option>
                        <option value="standard">Standard</option>
                        <option value="high">Haute qualité</option>
                        <option value="ultra">Ultra (Lent)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    Générer l'Image
                </button>
            </form>

            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Panel des résultats -->
        <div class="results-panel">
            <div class="results-header">
                <div class="results-title">
                    <i class="fas fa-images"></i>
                    Galerie d'Images
                </div>
                <div class="gallery-controls">
                    <button class="gallery-btn" onclick="clearGallery()">
                        <i class="fas fa-trash"></i>
                        Effacer
                    </button>
                    <button class="gallery-btn" onclick="downloadAll()">
                        <i class="fas fa-download"></i>
                        Télécharger
                    </button>
                </div>
            </div>

            <div class="image-grid" id="imageGrid">
                <div class="status-message">
                    <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>Aucune image générée pour le moment.</p>
                    <p>Utilisez le panneau de gauche pour créer votre première image !</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let generatedImages = [];
        let isGenerating = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Générateur d\'images initialisé');
            setupEventListeners();
            loadSavedImages();
        });

        function setupEventListeners() {
            const form = document.getElementById('imageForm');
            form.addEventListener('submit', handleImageGeneration);
        }

        async function handleImageGeneration(event) {
            event.preventDefault();

            if (isGenerating) return;

            const formData = new FormData(event.target);
            const prompt = document.getElementById('prompt').value.trim();

            if (!prompt) {
                alert('Veuillez entrer une description pour l'image');
                return;
            }

            isGenerating = true;
            updateGenerateButton(true);
            showProgress();

            try {
                const imageData = {
                    prompt: prompt,
                    style: document.getElementById('style').value,
                    resolution: document.getElementById('resolution').value,
                    quality: document.getElementById('quality').value,
                    timestamp: new Date().toISOString()
                };

                // Générer l'image avec l'API
                await generateImageWithAPI(imageData);

            } catch (error) {
                console.error('Erreur génération image:', error);
                alert('Erreur lors de la génération de l\'image');
            } finally {
                isGenerating = false;
                updateGenerateButton(false);
                hideProgress();
            }
        }

        async function generateImageWithAPI(imageData) {
            try {
                updateProgress(10);

                // Appeler l'API de génération d'images
                const response = await fetch('/api/images/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(imageData)
                });

                updateProgress(30);

                if (!response.ok) {
                    throw new Error(`Erreur API: ${response.status}`);
                }

                updateProgress(60);

                const result = await response.json();

                updateProgress(90);

                if (!result.success) {
                    throw new Error(result.error || 'Erreur génération image');
                }

                updateProgress(100);

                // Créer l'objet image avec les données de l'API
                const newImage = {
                    id: Date.now(),
                    url: result.image.url,
                    prompt: imageData.prompt,
                    style: imageData.style,
                    resolution: imageData.resolution,
                    quality: imageData.quality,
                    timestamp: imageData.timestamp,
                    source: result.image.source,
                    isPlaceholder: result.image.isPlaceholder || false,
                    metadata: result.metadata
                };

                generatedImages.unshift(newImage);
                addImageToGallery(newImage);
                saveImages();

                // Afficher un message de succès
                if (result.image.isPlaceholder) {
                    showNotification('Image placeholder générée (APIs externes non configurées)', 'warning');
                } else {
                    showNotification(`Image générée avec ${result.image.source}!`, 'success');
                }

                return newImage;

            } catch (error) {
                console.error('Erreur génération API:', error);

                // Fallback vers image placeholder
                updateProgress(100);

                const fallbackImage = {
                    id: Date.now(),
                    url: `https://picsum.photos/400/400?random=${Date.now()}`,
                    prompt: imageData.prompt,
                    style: imageData.style,
                    resolution: imageData.resolution,
                    quality: imageData.quality,
                    timestamp: imageData.timestamp,
                    source: 'fallback',
                    isPlaceholder: true,
                    error: error.message
                };

                generatedImages.unshift(fallbackImage);
                addImageToGallery(fallbackImage);
                saveImages();

                showNotification('Erreur API - Image placeholder générée', 'error');

                return fallbackImage;
            }
        }

        function addImageToGallery(imageData) {
            const grid = document.getElementById('imageGrid');

            // Supprimer le message de statut s'il existe
            const statusMessage = grid.querySelector('.status-message');
            if (statusMessage) {
                statusMessage.remove();
            }

            const imageCard = document.createElement('div');
            imageCard.className = 'image-card';
            imageCard.innerHTML = `
                <img src="${imageData.url}" alt="${imageData.prompt}"
                     style="width: 100%; height: 200px; object-fit: cover;"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                <div class="image-placeholder" style="display: none;">
                    <i class="fas fa-image"></i>
                    Image non disponible
                </div>
                <div class="image-info">
                    <div class="image-prompt">${imageData.prompt}</div>
                    <div class="image-meta">
                        ${imageData.style} • ${imageData.resolution} • ${imageData.quality}
                    </div>
                </div>
            `;

            imageCard.addEventListener('click', () => openImageModal(imageData));
            grid.insertBefore(imageCard, grid.firstChild);
        }

        function updateGenerateButton(generating) {
            const btn = document.getElementById('generateBtn');
            if (generating) {
                btn.innerHTML = '<div class="loading-spinner"></div> Génération en cours...';
                btn.disabled = true;
            } else {
                btn.innerHTML = '<i class="fas fa-magic"></i> Générer l\'Image';
                btn.disabled = false;
            }
        }

        function showProgress() {
            document.getElementById('progressBar').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function clearGallery() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les images ?')) {
                generatedImages = [];
                const grid = document.getElementById('imageGrid');
                grid.innerHTML = `
                    <div class="status-message">
                        <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>Galerie effacée.</p>
                        <p>Générez de nouvelles images !</p>
                    </div>
                `;
                saveImages();
            }
        }

        function downloadAll() {
            if (generatedImages.length === 0) {
                alert('Aucune image à télécharger');
                return;
            }

            generatedImages.forEach((image, index) => {
                const link = document.createElement('a');
                link.href = image.url;
                link.download = `louna-image-${index + 1}.jpg`;
                link.click();
            });
        }

        function openImageModal(imageData) {
            // TODO: Implémenter modal d'image
            console.log('Ouverture modal pour:', imageData);
        }

        function saveImages() {
            localStorage.setItem('lounaGeneratedImages', JSON.stringify(generatedImages));
        }

        function loadSavedImages() {
            const saved = localStorage.getItem('lounaGeneratedImages');
            if (saved) {
                generatedImages = JSON.parse(saved);
                generatedImages.forEach(image => addImageToGallery(image));
            }
        }

        function showNotification(message, type = 'info') {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            switch(type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                    notification.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                    break;
                case 'warning':
                    notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                    notification.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    notification.innerHTML = `<i class="fas fa-times-circle"></i> ${message}`;
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #2196f3, #1976d2)';
                    notification.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            }

            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // Suppression automatique
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function addToThermalMemory(imageData) {
            // Intégration avec la mémoire thermique
            fetch('/api/memory/add-instant', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        type: 'image_generation',
                        prompt: imageData.prompt,
                        style: imageData.style,
                        resolution: imageData.resolution,
                        timestamp: imageData.timestamp,
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe'
                    },
                    options: {
                        critical: false,
                        source: 'image_generator',
                        type: 'creative_content'
                    }
                })
            }).catch(error => {
                console.warn('Erreur sauvegarde mémoire thermique:', error);
            });
        }
    </script>
</body>
</html>
