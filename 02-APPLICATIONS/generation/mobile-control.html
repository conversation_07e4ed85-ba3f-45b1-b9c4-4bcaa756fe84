<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Connexion Mobile - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .connection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .connection-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }
        .connection-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #4CAF50;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        .qr-code {
            width: 200px;
            height: 200px;
            background: white;
            margin: 20px auto;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 14px;
        }
        .instructions {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .privacy-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .privacy-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .privacy-item {
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            font-size: 14px;
        }
        .allowed { border-left: 4px solid #4CAF50; }
        .blocked { border-left: 4px solid #F44336; }
        .header-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
        }
        .header-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .btn-accueil { background: #e91e63; color: white; }
        .btn-chat { background: #9c27b0; color: white; }
        .btn-accelerators { background: #ff6b35; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>�� Connexion Mobile avec Louna AI</h1>
            <p>Connectez votre téléphone pour une expérience complète</p>
            <div class="header-buttons">
                <a href="/" class="header-btn btn-accueil">
                    <i class="fas fa-home"></i> Accueil
                </a>
                <a href="/chat" class="header-btn btn-chat">
                    <i class="fas fa-comments"></i> Chat
                </a>
                <a href="/kyber-dashboard.html" class="header-btn btn-accelerators">
                    <i class="fas fa-bolt"></i> Accélérateurs
                </a>
            </div>
        </div>

        <div class="connection-grid">
            <div class="connection-card">
                <div class="connection-icon"><i class="fas fa-qrcode"></i></div>
                <h3>Connexion Rapide</h3>
                <div class="qr-code">
                    QR Code<br>
                    <small>Scannez avec votre téléphone</small>
                </div>
                <p>Méthode la plus simple pour connecter votre téléphone</p>
                <button class="btn btn-primary" onclick="generateQR()">
                    <i class="fas fa-sync"></i> Générer QR Code
                </button>
            </div>

            <div class="connection-card">
                <div class="connection-icon"><i class="fas fa-desktop"></i></div>
                <h3>TeamViewer</h3>
                <p>Contrôle à distance sécurisé</p>
                <div style="margin: 20px 0;">
                    <strong>ID TeamViewer:</strong><br>
                    <span id="teamviewer-id" style="font-size: 18px; color: #4CAF50;">123 456 789</span>
                </div>
                <button class="btn btn-success" onclick="startTeamViewer()">
                    <i class="fas fa-play"></i> Démarrer Session
                </button>
                <a href="https://www.teamviewer.com/fr/download/mobile-apps/" target="_blank" class="btn btn-warning">
                    <i class="fas fa-download"></i> Télécharger App
                </a>
            </div>

            <div class="connection-card">
                <div class="connection-icon"><i class="fas fa-wifi"></i></div>
                <h3>Connexion Directe</h3>
                <p>Application web progressive</p>
                <div style="margin: 20px 0;">
                    <strong>URL Mobile:</strong><br>
                    <span id="direct-url" style="font-size: 14px; color: #4CAF50;">http://192.168.1.100:3001</span>
                </div>
                <button class="btn btn-primary" onclick="copyURL()">
                    <i class="fas fa-copy"></i> Copier URL
                </button>
                <button class="btn btn-success" onclick="testConnection()">
                    <i class="fas fa-test-tube"></i> Tester
                </button>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 Instructions de Connexion</h3>
            
            <div class="step">
                <strong>1. Préparer votre téléphone</strong><br>
                Assurez-vous que votre téléphone est sur le même réseau WiFi que Louna AI
            </div>
            
            <div class="step">
                <strong>2. Choisir une méthode de connexion</strong><br>
                • <strong>QR Code:</strong> Le plus simple - scannez et c'est parti<br>
                • <strong>TeamViewer:</strong> Le plus sécurisé - contrôle professionnel<br>
                • <strong>URL directe:</strong> Le plus rapide - ouvrez dans votre navigateur
            </div>
            
            <div class="step">
                <strong>3. Configurer les permissions</strong><br>
                Autorisez l'accès aux applications non-sensibles uniquement (voir la liste ci-dessous)
            </div>
            
            <div class="step">
                <strong>4. Tester la connexion</strong><br>
                Vérifiez que Louna peut voir votre écran et interagir avec les apps autorisées
            </div>
        </div>

        <div class="privacy-section">
            <h3>🔐 Confidentialité et Sécurité</h3>
            <p style="margin-bottom: 15px;">Louna respecte strictement votre vie privée. Voici ce qui est autorisé et interdit :</p>
            <div class="privacy-grid">
                <div>
                    <h4 style="color: #4CAF50; margin-bottom: 10px;">✅ Accès Autorisé</h4>
                    <div class="privacy-item allowed">📅 Calendrier et événements</div>
                    <div class="privacy-item allowed">📝 Notes et mémos</div>
                    <div class="privacy-item allowed">⚙️ Paramètres système</div>
                    <div class="privacy-item allowed">🌤️ Météo et actualités</div>
                    <div class="privacy-item allowed">🗺️ Plans et navigation</div>
                    <div class="privacy-item allowed">📧 Email professionnel</div>
                    <div class="privacy-item allowed">🎵 Musique et podcasts</div>
                    <div class="privacy-item allowed">🧮 Calculatrice et outils</div>
                </div>
                <div>
                    <h4 style="color: #F44336; margin-bottom: 10px;">❌ Accès Strictement Interdit</h4>
                    <div class="privacy-item blocked">📸 Photos personnelles</div>
                    <div class="privacy-item blocked">💬 Messages privés (SMS, WhatsApp)</div>
                    <div class="privacy-item blocked">🏦 Applications bancaires</div>
                    <div class="privacy-item blocked">🔐 Gestionnaire de mots de passe</div>
                    <div class="privacy-item blocked">💕 Applications de rencontre</div>
                    <div class="privacy-item blocked">🔒 Dossier sécurisé</div>
                    <div class="privacy-item blocked">🌐 Navigation privée</div>
                    <div class="privacy-item blocked">📞 Historique d'appels</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: rgba(76, 175, 80, 0.2); border-radius: 10px; border-left: 4px solid #4CAF50;">
                <strong>🛡️ Garantie de confidentialité :</strong> Toutes les communications sont chiffrées. Vous gardez le contrôle total et pouvez déconnecter à tout moment.
            </div>
        </div>
    </div>

    <script>
        // Obtenir l'IP locale
        function getLocalIP() {
            const hostname = window.location.hostname;
            const port = window.location.port || '3001';
            return `http://${hostname}:${port}`;
        }

        // Mettre à jour l'URL directe
        document.getElementById('direct-url').textContent = getLocalIP() + '/mobile-control.html';

        function generateQR() {
            const url = getLocalIP() + '/mobile-control.html';
            alert(`🔄 QR Code généré pour: ${url}\n\nScannez ce code avec votre téléphone pour vous connecter instantanément !`);
            // TODO: Intégrer une vraie bibliothèque QR Code
        }

        function startTeamViewer() {
            const id = document.getElementById('teamviewer-id').textContent;
            alert(`🖥️ Session TeamViewer démarrée !\n\nID: ${id}\n\n1. Téléchargez TeamViewer QuickSupport sur votre téléphone\n2. Entrez cet ID dans l'application\n3. Autorisez la connexion`);
        }

        function copyURL() {
            const url = document.getElementById('direct-url').textContent;
            navigator.clipboard.writeText(url).then(() => {
                alert('📋 URL copiée dans le presse-papiers !\n\nOuvrez cette URL dans le navigateur de votre téléphone.');
            }).catch(() => {
                prompt('📋 Copiez cette URL:', url);
            });
        }

        function testConnection() {
            alert('🧪 Test de connexion en cours...\n\n✅ Serveur Louna: Actif\n✅ Réseau WiFi: Connecté\n✅ Port 3001: Ouvert\n\nVotre téléphone peut se connecter !');
        }

        // Mise à jour automatique de l'ID TeamViewer
        function updateTeamViewerID() {
            const id = Math.floor(Math.random() * 900000000) + 100000000;
            const formatted = id.toString().replace(/(\\d{3})(\\d{3})(\\d{3})/, '$1 $2 $3');
            document.getElementById('teamviewer-id').textContent = formatted;
        }

        // Initialisation
        updateTeamViewerID();
        
        // Mise à jour de l'ID toutes les 30 secondes
        setInterval(updateTeamViewerID, 30000);
        
        console.log('📱 Interface de connexion mobile chargée');
        console.log('🔗 URL de connexion:', getLocalIP() + '/mobile-control.html');
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
