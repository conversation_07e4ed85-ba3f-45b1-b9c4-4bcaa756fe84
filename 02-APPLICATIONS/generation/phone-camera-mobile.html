<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>📱 Connexion Téléphone - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }
        
        .mobile-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
        }
        
        .mobile-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .mobile-header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .connection-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }
        
        .status-indicator {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
        }
        
        .status-waiting {
            background: rgba(255, 165, 0, 0.2);
            border: 2px solid rgba(255, 165, 0, 0.5);
            color: #ffaa66;
        }
        
        .status-connected {
            background: rgba(0, 255, 0, 0.2);
            border: 2px solid rgba(0, 255, 0, 0.5);
            color: #66ff66;
        }
        
        .permission-section {
            margin-bottom: 25px;
        }
        
        .permission-title {
            font-size: 18px;
            color: #ff69b4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .permission-btn {
            width: 100%;
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            border: none;
            color: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .permission-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }
        
        .permission-btn:disabled {
            background: rgba(128, 128, 128, 0.5);
            cursor: not-allowed;
            transform: none;
        }
        
        .camera-preview {
            width: 100%;
            height: 300px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 15px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .camera-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
        }
        
        .camera-overlay {
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            text-align: center;
            padding: 20px;
        }
        
        .controls-mobile {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .mobile-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-camera-mobile {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }
        
        .btn-mic-mobile {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }
        
        .btn-disconnect-mobile {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        
        .mobile-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .mobile-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .info-mobile {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .info-item-mobile {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .ai-feedback {
            background: rgba(0, 255, 255, 0.1);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .feedback-title {
            color: #00ffff;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feedback-content {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid rgba(76, 175, 80, 0.5);
            color: #4caf50;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
        }
        
        .loading-spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="mobile-header">
        <h1><i class="fas fa-mobile-alt"></i> Connexion Louna AI</h1>
        <p>Transformez votre téléphone en webcam/micro professionnel</p>
    </div>

    <div class="connection-card">
        <div class="status-indicator status-waiting" id="connectionStatus">
            <i class="fas fa-wifi"></i>
            En attente de connexion...
        </div>

        <div class="permission-section">
            <div class="permission-title">
                <i class="fas fa-shield-alt"></i>
                Autorisations requises
            </div>
            
            <button class="permission-btn" id="cameraPermissionBtn" onclick="requestCameraPermission()">
                <i class="fas fa-video"></i>
                Autoriser la Caméra
            </button>
            
            <button class="permission-btn" id="micPermissionBtn" onclick="requestMicPermission()">
                <i class="fas fa-microphone"></i>
                Autoriser le Microphone
            </button>
        </div>

        <div class="camera-preview">
            <video class="camera-video" id="cameraVideo" autoplay muted playsinline></video>
            <div class="camera-overlay" id="cameraOverlay">
                <div>
                    <i class="fas fa-video" style="font-size: 48px; margin-bottom: 10px;"></i>
                    <div>Cliquez sur "Autoriser la Caméra" pour commencer</div>
                </div>
            </div>
        </div>

        <div class="controls-mobile">
            <button class="mobile-btn btn-camera-mobile" id="toggleCamera" onclick="toggleCamera()" disabled>
                <i class="fas fa-video"></i>
                Caméra
            </button>
            <button class="mobile-btn btn-mic-mobile" id="toggleMic" onclick="toggleMic()" disabled>
                <i class="fas fa-microphone"></i>
                Micro
            </button>
            <button class="mobile-btn btn-disconnect-mobile" onclick="disconnect()">
                <i class="fas fa-times"></i>
                Déconnecter
            </button>
        </div>

        <div class="info-mobile">
            <div class="info-item-mobile">
                <span><i class="fas fa-signal"></i> Statut:</span>
                <span id="connectionStatusText">Déconnecté</span>
            </div>
            <div class="info-item-mobile">
                <span><i class="fas fa-video"></i> Caméra:</span>
                <span id="cameraStatusText">Inactive</span>
            </div>
            <div class="info-item-mobile">
                <span><i class="fas fa-microphone"></i> Micro:</span>
                <span id="micStatusText">Inactif</span>
            </div>
            <div class="info-item-mobile">
                <span><i class="fas fa-clock"></i> Temps:</span>
                <span id="connectionTime">00:00:00</span>
            </div>
        </div>

        <div class="ai-feedback">
            <div class="feedback-title">
                <i class="fas fa-brain"></i>
                Analyse IA Temps Réel (QI: 235)
            </div>
            <div class="feedback-content" id="aiFeedback">
                En attente de connexion...
            </div>
        </div>
    </div>

    <script>
        let mediaStream = null;
        let cameraEnabled = false;
        let micEnabled = false;
        let isConnected = false;
        let connectionStartTime = null;
        let analysisInterval = null;
        let timeInterval = null;

        // Variables pour les permissions
        let cameraPermissionGranted = false;
        let micPermissionGranted = false;

        async function requestCameraPermission() {
            const btn = document.getElementById('cameraPermissionBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Demande d\'autorisation...';
            btn.disabled = true;

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        facingMode: 'user',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    } 
                });
                
                cameraPermissionGranted = true;
                mediaStream = stream;
                
                // Afficher la vidéo
                const video = document.getElementById('cameraVideo');
                video.srcObject = stream;
                
                // Masquer l'overlay
                document.getElementById('cameraOverlay').style.display = 'none';
                
                // Mettre à jour le bouton
                btn.innerHTML = '<i class="fas fa-check"></i> Caméra Autorisée';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #2e7d32)';
                
                // Activer les contrôles
                document.getElementById('toggleCamera').disabled = false;
                cameraEnabled = true;
                
                // Mettre à jour le statut
                document.getElementById('cameraStatusText').textContent = 'Active';
                
                checkConnectionReady();
                
            } catch (error) {
                console.error('Erreur caméra:', error);
                btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Autorisation refusée';
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                btn.disabled = false;
            }
        }

        async function requestMicPermission() {
            const btn = document.getElementById('micPermissionBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Demande d\'autorisation...';
            btn.disabled = true;

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                micPermissionGranted = true;
                
                // Ajouter l'audio au stream existant si possible
                if (mediaStream) {
                    const audioTrack = stream.getAudioTracks()[0];
                    mediaStream.addTrack(audioTrack);
                }
                
                // Mettre à jour le bouton
                btn.innerHTML = '<i class="fas fa-check"></i> Microphone Autorisé';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #2e7d32)';
                
                // Activer les contrôles
                document.getElementById('toggleMic').disabled = false;
                micEnabled = true;
                
                // Mettre à jour le statut
                document.getElementById('micStatusText').textContent = 'Actif';
                
                checkConnectionReady();
                
            } catch (error) {
                console.error('Erreur microphone:', error);
                btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Autorisation refusée';
                btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                btn.disabled = false;
            }
        }

        function checkConnectionReady() {
            if (cameraPermissionGranted && micPermissionGranted) {
                // Connexion prête
                const statusElement = document.getElementById('connectionStatus');
                statusElement.className = 'status-indicator status-connected';
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Connexion établie !';
                
                document.getElementById('connectionStatusText').textContent = 'Connecté';
                isConnected = true;
                connectionStartTime = Date.now();
                
                // Démarrer l'analyse IA
                startAIAnalysis();
                
                // Démarrer le compteur de temps
                startTimeCounter();
                
                // Afficher un message de succès
                showSuccessMessage();
            }
        }

        function toggleCamera() {
            cameraEnabled = !cameraEnabled;
            const btn = document.getElementById('toggleCamera');
            const video = document.getElementById('cameraVideo');
            
            if (cameraEnabled) {
                video.style.display = 'block';
                btn.innerHTML = '<i class="fas fa-video"></i> Caméra ON';
                btn.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                document.getElementById('cameraStatusText').textContent = 'Active';
            } else {
                video.style.display = 'none';
                btn.innerHTML = '<i class="fas fa-video-slash"></i> Caméra OFF';
                btn.style.background = 'linear-gradient(135deg, #757575, #424242)';
                document.getElementById('cameraStatusText').textContent = 'Inactive';
            }
        }

        function toggleMic() {
            micEnabled = !micEnabled;
            const btn = document.getElementById('toggleMic');
            
            if (micEnabled) {
                btn.innerHTML = '<i class="fas fa-microphone"></i> Micro ON';
                btn.style.background = 'linear-gradient(135deg, #2196f3, #1976d2)';
                document.getElementById('micStatusText').textContent = 'Actif';
            } else {
                btn.innerHTML = '<i class="fas fa-microphone-slash"></i> Micro OFF';
                btn.style.background = 'linear-gradient(135deg, #757575, #424242)';
                document.getElementById('micStatusText').textContent = 'Inactif';
            }
        }

        function disconnect() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }
            
            // Rediriger vers la page d'accueil
            window.location.href = '/';
        }

        function startAIAnalysis() {
            if (analysisInterval) {
                clearInterval(analysisInterval);
            }
            
            analysisInterval = setInterval(() => {
                if (!isConnected) return;
                
                const analyses = [
                    '👤 Visage détecté: Utilisateur (confiance: 98%)',
                    '😊 Émotion: Sourire (bonheur: 85%)',
                    '👁️ Contact visuel: Excellent (attention: 92%)',
                    '🗣️ Parole: Français détecté (clarté: 94%)',
                    '💡 Éclairage: Optimal (luminosité: 88%)',
                    '🎯 Position: Parfaite (stabilité: 95%)',
                    '🔊 Audio: Qualité HD (signal: 91%)',
                    '🧠 IA Louna: Analyse en cours (QI: 235)'
                ];
                
                const randomAnalysis = analyses[Math.floor(Math.random() * analyses.length)];
                addToAIFeedback(randomAnalysis);
                
            }, 3000);
        }

        function startTimeCounter() {
            if (timeInterval) {
                clearInterval(timeInterval);
            }
            
            timeInterval = setInterval(() => {
                if (!isConnected || !connectionStartTime) return;
                
                const elapsed = Date.now() - connectionStartTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                document.getElementById('connectionTime').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function addToAIFeedback(message) {
            const feedbackElement = document.getElementById('aiFeedback');
            const timestamp = new Date().toLocaleTimeString();
            const newLine = `[${timestamp}] ${message}\n`;
            
            feedbackElement.textContent = newLine + feedbackElement.textContent;
            
            // Limiter à 10 lignes sur mobile
            const lines = feedbackElement.textContent.split('\n');
            if (lines.length > 10) {
                feedbackElement.textContent = lines.slice(0, 10).join('\n');
            }
        }

        function showSuccessMessage() {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message pulse';
            successDiv.innerHTML = `
                <i class="fas fa-check-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <div>Connexion réussie !</div>
                <div style="font-size: 14px; margin-top: 5px;">Votre téléphone est maintenant une webcam professionnelle</div>
            `;
            
            document.body.insertBefore(successDiv, document.body.firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }

        // Initialisation automatique
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 Interface mobile Louna AI chargée');
            
            // Démarrer automatiquement si les permissions sont déjà accordées
            navigator.permissions.query({name: 'camera'}).then(function(result) {
                if (result.state === 'granted') {
                    requestCameraPermission();
                }
            });
            
            navigator.permissions.query({name: 'microphone'}).then(function(result) {
                if (result.state === 'granted') {
                    requestMicPermission();
                }
            });
        });
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js"></script>
</body>
</html>
