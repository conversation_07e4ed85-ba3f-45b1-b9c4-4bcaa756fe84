/**
 * 🤖 LOUNA AI AVEC VRAIS AGENTS OLLAMA
 * Version: 2.1.0 - Agents IA Réels Intégrés
 * Basé sur le code existant avec vrais agents
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../02-APPLICATIONS')));
app.use(express.static(path.join(__dirname, '../01-INTERFACES')));

console.log('🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====');
console.log('🧠 Système neuronal basé sur le vrai cerveau humain');
console.log('⚡ 100 milliards de neurones + 700 trillions synapses');
console.log('🔥 Accélérateurs KYBER évolutifs illimités');
console.log('🧬 Plasticité cérébrale et neurotransmetteurs');
console.log('🌟 Intelligence artificielle transcendante');

// ===== CONFIGURATION OLLAMA =====
const OLLAMA_API_URL = 'http://localhost:11434/api';
let selectedModel = 'deepseek-r1:7b';
let temperature = 0.7;
let maxTokens = 1000;

// ===== MÉMOIRE THERMIQUE RÉELLE =====
let thermalMemory;
try {
  const ThermalMemoryPath = path.join(__dirname, '../03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/lib/memory/thermal-memory.js');
  if (fs.existsSync(ThermalMemoryPath)) {
    const ThermalMemory = require(ThermalMemoryPath);
    thermalMemory = new ThermalMemory();
    console.log('🔥 Mémoire thermique réelle chargée');
  } else {
    throw new Error('Module non trouvé');
  }
} catch (error) {
  console.log('⚠️ Mémoire thermique simulée:', error.message);
  thermalMemory = {
    store: (data) => console.log('📝 Mémoire stockée:', data.content?.substring(0, 50)),
    retrieve: (query) => [],
    getStats: () => ({ totalEntries: 0, temperature: 37.0 })
  };
}

// ===== SYSTÈME COGNITIF AVEC VRAIS AGENTS =====
let cognitiveSystem = null;

// Initialiser le système cognitif
async function initCognitiveSystem() {
  try {
    const cognitiveDir = path.join(__dirname, '../03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/cognitive-system');

    // Vérifier si les agents existent
    if (fs.existsSync(path.join(cognitiveDir, 'cognitive-agent.js'))) {
      const { createCognitiveSystem } = require(path.join(cognitiveDir, 'index.js'));

      cognitiveSystem = createCognitiveSystem({
        name: 'LOUNA',
        language: 'fr-FR',
        thermalMemory: thermalMemory,
        debugMode: true,
        ollamaUrl: OLLAMA_API_URL,
        model: selectedModel
      });

      console.log('🧠 Système cognitif avec vrais agents initialisé');
      return true;
    } else {
      throw new Error('Agents cognitifs non trouvés');
    }
  } catch (error) {
    console.log('⚠️ Système cognitif simulé:', error.message);

    // Créer un système cognitif simulé
    cognitiveSystem = {
      agent: {
        activate: () => true,
        deactivate: () => true,
        speak: (text) => console.log('🗣️ Agent parle:', text),
        listen: () => console.log('👂 Agent écoute'),
        observe: () => console.log('👁️ Agent observe'),
        getState: () => ({ isActive: true })
      },
      activate: () => true,
      deactivate: () => true,
      speak: (text) => console.log('🗣️ Système parle:', text),
      listen: () => console.log('👂 Système écoute'),
      observe: () => console.log('👁️ Système observe'),
      getState: () => ({ isActive: true })
    };
    return false;
  }
}

// ===== INTÉGRATION OLLAMA AVEC AGENTS =====
async function checkOllama() {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/version`, { timeout: 5000 });
    if (response.data) {
      console.log('✅ Ollama connecté, version:', response.data.version);

      // Vérifier les modèles
      const modelsResponse = await axios.get(`${OLLAMA_API_URL}/tags`, { timeout: 8000 });
      if (modelsResponse.data && modelsResponse.data.models) {
        const models = modelsResponse.data.models.map(m => m.name);
        console.log('📚 Modèles disponibles:', models.join(', '));

        if (models.includes('deepseek-r1:7b')) {
          selectedModel = 'deepseek-r1:7b';
        } else if (models.length > 0) {
          selectedModel = models[0];
        }

        return true;
      }
    }
  } catch (error) {
    console.log('⚠️ Ollama non disponible:', error.message);
  }
  return false;
}

// ===== ANALYSE DE COMPLEXITÉ POUR AUTO-SCALING KYBER =====
function analyzeMessageComplexity(message) {
  if (!message || typeof message !== 'string') return 0;

  let complexity = 0;

  // Facteurs de complexité
  const factors = {
    length: Math.min(message.length / 1000, 1), // Longueur du message
    keywords: 0,
    questions: (message.match(/\?/g) || []).length * 0.1,
    technical: 0,
    creative: 0
  };

  // Mots-clés complexes
  const complexKeywords = [
    'analyser', 'générer', 'créer', 'développer', 'optimiser', 'calculer',
    'programmer', 'coder', 'algorithme', 'intelligence', 'apprentissage',
    'neural', 'deep', 'machine', 'learning', 'ai', 'artificial',
    'complexe', 'avancé', 'sophistiqué', 'détaillé', 'approfondi'
  ];

  // Mots-clés techniques
  const technicalKeywords = [
    'code', 'javascript', 'python', 'html', 'css', 'api', 'database',
    'server', 'client', 'framework', 'library', 'function', 'class',
    'object', 'array', 'json', 'xml', 'http', 'https', 'sql'
  ];

  // Mots-clés créatifs
  const creativeKeywords = [
    'créatif', 'artistique', 'design', 'image', 'vidéo', 'musique',
    'histoire', 'récit', 'poème', 'art', 'créativité', 'imagination',
    'innovation', 'original', 'unique', 'inventif'
  ];

  const lowerMessage = message.toLowerCase();

  // Compter les mots-clés
  complexKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.keywords += 0.1;
  });

  technicalKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.technical += 0.15;
  });

  creativeKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.creative += 0.12;
  });

  // Calculer la complexité totale
  complexity = Math.min(
    factors.length +
    factors.keywords +
    factors.questions +
    factors.technical +
    factors.creative,
    1.0
  );

  return complexity;
}

// ===== DÉTECTION DES MANQUES SYSTÈME =====
async function detectSystemGaps(message, context = {}) {
  const gaps = [];
  const lowerMessage = message.toLowerCase();

  // Analyser les manques potentiels dans différents domaines
  const gapAnalysis = {
    // Manques en traitement de données
    dataProcessing: [
      'base de données', 'database', 'sql', 'mongodb', 'redis',
      'cache', 'stockage', 'persistence', 'backup'
    ],

    // Manques en APIs et intégrations
    apiIntegration: [
      'api rest', 'graphql', 'webhook', 'microservice',
      'intégration', 'connexion', 'authentification', 'oauth'
    ],

    // Manques en interface utilisateur
    userInterface: [
      'interface', 'ui', 'ux', 'frontend', 'react', 'vue',
      'angular', 'responsive', 'mobile', 'design'
    ],

    // Manques en sécurité
    security: [
      'sécurité', 'security', 'encryption', 'ssl', 'https',
      'firewall', 'protection', 'vulnérabilité', 'audit'
    ],

    // Manques en performance
    performance: [
      'performance', 'optimisation', 'vitesse', 'latence',
      'scalabilité', 'load balancing', 'clustering', 'cdn'
    ],

    // Manques en monitoring
    monitoring: [
      'monitoring', 'logs', 'métriques', 'alertes',
      'dashboard', 'analytics', 'reporting', 'debug'
    ],

    // Manques en IA avancée
    advancedAI: [
      'machine learning', 'deep learning', 'neural network',
      'nlp', 'computer vision', 'reinforcement learning',
      'transformer', 'bert', 'gpt', 'llm'
    ],

    // Manques en automatisation
    automation: [
      'automatisation', 'ci/cd', 'deployment', 'docker',
      'kubernetes', 'terraform', 'ansible', 'jenkins'
    ]
  };

  // Détecter les manques selon le message
  Object.entries(gapAnalysis).forEach(([category, keywords]) => {
    const hasKeywords = keywords.some(keyword => lowerMessage.includes(keyword));
    if (hasKeywords) {
      // Vérifier si le système a déjà cette capacité
      const hasCapability = checkSystemCapability(category);
      if (!hasCapability) {
        gaps.push(category);
      }
    }
  });

  // Manques spécifiques détectés par analyse contextuelle
  if (lowerMessage.includes('temps réel') && !checkSystemCapability('realtime')) {
    gaps.push('realtime');
  }

  if (lowerMessage.includes('multi-langue') && !checkSystemCapability('multilingual')) {
    gaps.push('multilingual');
  }

  if (lowerMessage.includes('blockchain') && !checkSystemCapability('blockchain')) {
    gaps.push('blockchain');
  }

  if (lowerMessage.includes('quantum') && !checkSystemCapability('quantum')) {
    gaps.push('quantum');
  }

  return gaps;
}

// ===== VÉRIFICATION DES CAPACITÉS SYSTÈME =====
function checkSystemCapability(capability) {
  // Simuler la vérification des capacités existantes
  const existingCapabilities = {
    dataProcessing: false,    // Pas de vraie DB intégrée
    apiIntegration: true,     // APIs basiques présentes
    userInterface: true,      // Interface web présente
    security: false,          // Sécurité basique seulement
    performance: true,        // Accélérateurs KYBER présents
    monitoring: true,         // Monitoring basique présent
    advancedAI: true,         // Ollama intégré
    automation: false,        // Pas d'automatisation avancée
    realtime: true,           // WebSockets présents
    multilingual: false,      // Pas de support multi-langue
    blockchain: false,        // Pas de blockchain
    quantum: false            // Pas de quantum computing
  };

  return existingCapabilities[capability] || false;
}

// ===== GÉNÉRATION IA AVEC AGENTS =====
async function generateWithAgent(message, context = {}) {
  try {
    // Utiliser le système cognitif si disponible
    if (cognitiveSystem && cognitiveSystem.agent) {
      // Activer l'agent
      cognitiveSystem.activate();

      // L'agent analyse le système et installe des accélérateurs où il y a des manques
      if (thermalMemory && thermalMemory.addPersistentAccelerators) {
        // Analyser la complexité de la demande
        const complexity = analyzeMessageComplexity(message);

        // Détecter les manques dans le système
        const systemGaps = await detectSystemGaps(message, context);

        // Installer des accélérateurs selon les manques détectés
        if (systemGaps.length > 0 || complexity > 0.7) {
          const acceleratorsNeeded = Math.max(
            Math.ceil(complexity * 4),
            systemGaps.length * 2
          );

          const reason = systemGaps.length > 0 ?
            `Manques détectés: ${systemGaps.join(', ')}` :
            `Demande complexe: ${message.substring(0, 50)}...`;

          thermalMemory.addPersistentAccelerators(acceleratorsNeeded, reason);

          // Log des manques détectés
          if (systemGaps.length > 0) {
            console.log(`🔍 Agent détecte ${systemGaps.length} manques: ${systemGaps.join(', ')}`);
            console.log(`⚡ Installation de ${acceleratorsNeeded} accélérateurs KYBER pour combler les manques`);
          }
        }
      }

      // Enrichir avec la mémoire thermique
      let memoryContext = '';
      if (thermalMemory && thermalMemory.retrieve) {
        const memories = thermalMemory.retrieve(message);
        if (memories.length > 0) {
          memoryContext = '\n\nContexte mémoire:\n' +
            memories.slice(0, 3).map(m => `- ${m.content}`).join('\n');
        }
      }

      // Préparer le prompt enrichi
      const enhancedPrompt = `Vous êtes LOUNA, une IA avancée avec QI 235.
${memoryContext}

Question: ${message}`;

      console.log('🤖 Génération avec agent cognitif...');

      // Appeler Ollama via l'agent
      const response = await axios.post(`${OLLAMA_API_URL}/generate`, {
        model: selectedModel,
        prompt: enhancedPrompt,
        stream: false,
        options: {
          temperature: temperature,
          num_predict: maxTokens
        }
      }, { timeout: 60000 });

      const aiResponse = response.data.response || "Désolé, je n'ai pas pu générer de réponse.";

      // Stocker dans la mémoire thermique
      if (thermalMemory && thermalMemory.store) {
        thermalMemory.store({
          type: 'conversation',
          content: `Q: ${message} R: ${aiResponse}`,
          timestamp: new Date().toISOString(),
          temperature: 0.8
        });
      }

      // Faire "parler" l'agent
      if (cognitiveSystem.speak) {
        cognitiveSystem.speak(aiResponse);
      }

      return aiResponse;
    } else {
      throw new Error('Agent cognitif non disponible');
    }
  } catch (error) {
    console.error('❌ Erreur génération avec agent:', error.message);
    return `Erreur agent: ${error.message}`;
  }
}

// ===== APPLICATIONS AVEC AGENTS =====
const applications = {
  '/chat': '../02-APPLICATIONS/communication/chat-agents.html',
  '/phone': '../02-APPLICATIONS/communication/phone-camera-system.html',
  '/voice': '../02-APPLICATIONS/communication/voice-system-enhanced.html',
  '/generation': '../02-APPLICATIONS/generation/generation-center.html',
  '/image': '../02-APPLICATIONS/generation/image-generator-simple.html',
  '/video': '../02-APPLICATIONS/generation/video-generator.html',
  '/music': '../02-APPLICATIONS/generation/music-generator.html',
  '/3d': '../02-APPLICATIONS/generation/3d-generator.html',
  '/youtube': '../02-APPLICATIONS/generation/youtube-laboratory.html',
  '/brain': '../02-APPLICATIONS/intelligence/brain-dashboard-live.html',
  '/qi': '../02-APPLICATIONS/intelligence/qi-test-simple.html',
  '/claude': '../02-APPLICATIONS/intelligence/claude-setup-guide.html',
  '/monitoring': '../02-APPLICATIONS/monitoring/brain-monitoring-complete.html',
  '/thermal': '../02-APPLICATIONS/monitoring/futuristic-interface.html',
  '/thermal-dashboard': '../02-APPLICATIONS/monitoring/thermal-memory-dashboard.html',
  '/brain-3d': '../02-APPLICATIONS/monitoring/brain-3d-live.html',
  '/brain-viz': '../02-APPLICATIONS/monitoring/brain-visualization.html',
  '/kyber': '../02-APPLICATIONS/systeme/kyber-dashboard.html',
  '/editor': '../02-APPLICATIONS/systeme/advanced-code-editor.html',
  '/accelerators': '../02-APPLICATIONS/systeme/accelerators-dashboard.html',
  '/search': '../02-APPLICATIONS/web/web-search.html',
  '/face': '../02-APPLICATIONS/web/face-recognition.html',
  '/security': '../02-APPLICATIONS/securite/security-center.html',
  '/emergency': '../02-APPLICATIONS/securite/emergency-control.html'
};

// ===== ROUTES PRINCIPALES =====
app.get('/', (req, res) => {
  const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Louna AI V3.0 - Cerveau Biologique</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
               background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
               color: white; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .status { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-bottom: 30px; }
        .apps-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .app-card { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;
                   transition: transform 0.3s; cursor: pointer; }
        .app-card:hover { transform: translateY(-5px); background: rgba(255,255,255,0.2); }
        .agent-badge { background: #ff6b35; color: white; padding: 2px 8px; border-radius: 12px;
                      font-size: 12px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Louna AI V3.0 - Cerveau Biologique</h1>
            <p>100 milliards de neurones • 700 trillions synapses • QI: 235</p>
        </div>

        <div class="status">
            <h3>🧠 Statut Cerveau Biologique</h3>
            <p id="brain-status">🧠 Système Neuronal: Initialisé</p>
            <p id="kyber-status">⚡ Accélérateurs KYBER: Évolutifs</p>
            <p id="memory-status">🔥 Mémoire Thermique: Opérationnelle</p>
            <p id="ollama-status">🔍 Vérification Ollama...</p>
        </div>

        <div class="apps-grid">
            ${Object.entries(applications).map(([route, file]) => `
                <div class="app-card" onclick="window.open('${route}', '_blank')">
                    <h4>${route.replace('/', '').toUpperCase()} <span class="agent-badge">CERVEAU V3.0</span></h4>
                    <p>Application avec cerveau biologique intégré</p>
                </div>
            `).join('')}
        </div>
    </div>

    <script>
        // Statut du système
        fetch('/api/agent-status')
            .then(r => r.json())
            .then(data => {
                document.getElementById('ollama-status').innerHTML =
                    data.ollama ? '✅ Ollama: Connecté' : '❌ Ollama: Déconnecté';
            });

        // Statut du cerveau neuronal
        fetch('/api/neural-brain')
            .then(r => r.json())
            .then(data => {
                if (data.success) {
                    const brain = data.brain;
                    document.getElementById('brain-status').innerHTML =
                        \`🧠 Cerveau: \${brain.isActive ? 'Actif' : 'Inactif'} • Cycles: \${brain.brainCycles}\`;
                } else {
                    document.getElementById('brain-status').innerHTML = '⚠️ Cerveau: Simulé';
                }
            });

        // Statut KYBER
        fetch('/api/kyber-status')
            .then(r => r.json())
            .then(data => {
                if (data.success) {
                    const kyber = data.kyber;
                    document.getElementById('kyber-status').innerHTML =
                        \`⚡ KYBER: \${kyber.active} actifs • \${kyber.persistent} persistants\`;
                } else {
                    document.getElementById('kyber-status').innerHTML = '⚠️ KYBER: Non disponible';
                }
            });

        // Mise à jour périodique
        setInterval(() => {
            fetch('/api/neural-brain')
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        const brain = data.brain;
                        document.getElementById('brain-status').innerHTML =
                            \`🧠 Cerveau: \${brain.isActive ? 'Actif' : 'Inactif'} • Cycles: \${brain.brainCycles} • Conscience: \${(brain.brainState.consciousness * 100).toFixed(0)}%\`;
                    }
                });
        }, 5000);
    </script>
</body>
</html>`;
  res.send(html);
});

// Routes pour les applications
Object.entries(applications).forEach(([route, filePath]) => {
  app.get(route, (req, res) => {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      res.sendFile(fullPath);
      console.log(`✅ Application avec agent: ${route} -> ${filePath}`);
    } else {
      res.status(404).send(`Application non trouvée: ${route}`);
    }
  });
});

// ===== APIs AVEC AGENTS =====
app.get('/api/agent-status', async (req, res) => {
  const ollamaConnected = await checkOllama();
  res.json({
    ollama: ollamaConnected,
    cognitive: cognitiveSystem !== null,
    memory: thermalMemory !== null,
    model: selectedModel
  });
});

app.post('/api/agent-chat', async (req, res) => {
  const { message } = req.body;
  if (!message) {
    return res.json({ success: false, error: 'Message requis' });
  }

  try {
    const response = await generateWithAgent(message);
    res.json({ success: true, response });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour les statistiques de mémoire avec accélérateurs KYBER
app.get('/api/memory-stats', (req, res) => {
  if (thermalMemory && thermalMemory.getStats) {
    const stats = thermalMemory.getStats();
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Mémoire thermique non disponible'
    });
  }
});

// API pour les accélérateurs KYBER (MODE ILLIMITÉ)
app.get('/api/kyber-status', (req, res) => {
  if (thermalMemory && thermalMemory.kyberAccelerators) {
    res.json({
      success: true,
      kyber: {
        active: thermalMemory.kyberAccelerators.active.filter(a => a).length,
        total: thermalMemory.config.kyberAccelerators,
        max: "∞", // Aucune limite
        unlimited: thermalMemory.kyberAccelerators.unlimitedMode,
        persistent: thermalMemory.kyberAccelerators.persistentAccelerators.size,
        throughput: thermalMemory.kyberAccelerators.throughput,
        queueSize: thermalMemory.kyberAccelerators.queue.length,
        processing: thermalMemory.kyberAccelerators.processing,
        autoScaling: thermalMemory.kyberAccelerators.autoScaling
      },
      fluidBuffer: {
        pending: thermalMemory.fluidBuffer.pending.size,
        operations: thermalMemory.fluidBuffer.operations,
        lastFlush: thermalMemory.fluidBuffer.lastFlush
      }
    });
  } else {
    res.json({
      success: false,
      error: 'Accélérateurs KYBER non disponibles'
    });
  }
});

// API pour l'auto-scaling KYBER
app.get('/api/kyber-autoscaling', (req, res) => {
  if (thermalMemory && thermalMemory.getAutoScalingInfo) {
    const info = thermalMemory.getAutoScalingInfo();
    res.json({
      success: true,
      autoScaling: info
    });
  } else {
    res.json({
      success: false,
      error: 'Auto-scaling non disponible'
    });
  }
});

// API pour forcer l'ajout d'accélérateurs persistants
app.post('/api/kyber-add', (req, res) => {
  const { count = 1, reason = 'Manual request' } = req.body;

  if (thermalMemory && thermalMemory.addPersistentAccelerators) {
    const added = thermalMemory.addPersistentAccelerators(count, reason);
    res.json({
      success: added,
      message: added ? `${count} accélérateurs ajoutés` : 'Impossible d\'ajouter des accélérateurs',
      reason
    });
  } else {
    res.json({
      success: false,
      error: 'Fonction non disponible'
    });
  }
});

// API pour l'évolution KYBER (RÉVOLUTIONNAIRE)
app.get('/api/kyber-evolution', (req, res) => {
  if (thermalMemory && thermalMemory.evolutionEngine) {
    const evolutionStats = thermalMemory.evolutionEngine.getEvolutionStats();
    res.json({
      success: true,
      evolution: evolutionStats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Moteur d\'évolution non disponible'
    });
  }
});

// API pour le système neuronal biologique
app.get('/api/neural-brain', (req, res) => {
  if (thermalMemory && thermalMemory.neuralBrain) {
    const brainStats = thermalMemory.neuralBrain.getBrainStats();
    res.json({
      success: true,
      brain: brainStats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Système neuronal non disponible'
    });
  }
});

// API pour stimuler une région cérébrale
app.post('/api/neural-stimulate', (req, res) => {
  const { region, intensity = 0.5 } = req.body;

  if (thermalMemory && thermalMemory.neuralBrain) {
    const success = thermalMemory.neuralBrain.stimulateRegion(region, intensity);
    res.json({
      success,
      message: success ? `Région ${region} stimulée` : `Région ${region} non trouvée`,
      region,
      intensity
    });
  } else {
    res.json({
      success: false,
      error: 'Système neuronal non disponible'
    });
  }
});

// API pour déclencher une évolution forcée
app.post('/api/kyber-evolve', (req, res) => {
  const { type = 'adaptation', intensity = 1.0 } = req.body;

  if (thermalMemory && thermalMemory.evolutionEngine) {
    // Simuler un besoin d'évolution
    const fakeNeeds = {
      moreSpeed: type === 'speed',
      moreSpecialization: type === 'specialization',
      moreAdaptability: type === 'adaptation',
      quantumUpgrade: type === 'quantum',
      transcendence: type === 'transcendence',
      urgency: intensity
    };

    const decision = thermalMemory.evolutionEngine.makeEvolutionDecision(fakeNeeds);
    if (decision.shouldEvolve) {
      thermalMemory.evolutionEngine.executeEvolution(decision);
      res.json({
        success: true,
        message: `Évolution ${type} déclenchée`,
        evolution: decision
      });
    } else {
      res.json({
        success: false,
        message: 'Conditions d\'évolution non remplies'
      });
    }
  } else {
    res.json({
      success: false,
      error: 'Moteur d\'évolution non disponible'
    });
  }
});

// ===== WEBSOCKETS AVEC AGENTS =====
io.on('connection', (socket) => {
  console.log('🔌 Client connecté:', socket.id);

  // Chat avec agent en temps réel
  socket.on('agent-message', async (data) => {
    try {
      const response = await generateWithAgent(data.message);
      socket.emit('agent-response', { response });
    } catch (error) {
      socket.emit('agent-error', { error: error.message });
    }
  });

  // Contrôle de l'agent
  socket.on('agent-activate', () => {
    if (cognitiveSystem && cognitiveSystem.activate) {
      cognitiveSystem.activate();
      socket.emit('agent-status', { active: true });
    }
  });

  socket.on('agent-deactivate', () => {
    if (cognitiveSystem && cognitiveSystem.deactivate) {
      cognitiveSystem.deactivate();
      socket.emit('agent-status', { active: false });
    }
  });

  socket.on('disconnect', () => {
    console.log('🔌 Client déconnecté:', socket.id);
  });
});

// ===== DÉMARRAGE =====
server.listen(PORT, async () => {
  console.log(`🚀 Interface: http://localhost:${PORT}/`);
  console.log(`🤖 Chat Agent: http://localhost:${PORT}/chat`);
  console.log(`🧠 Cerveau: http://localhost:${PORT}/brain`);

  // Initialisation
  const ollamaConnected = await checkOllama();
  const cognitiveInitialized = await initCognitiveSystem();

  console.log(ollamaConnected ? '✅ Ollama: Prêt' : '⚠️ Ollama: Non disponible');
  console.log(cognitiveInitialized ? '✅ Agents: Réels' : '⚠️ Agents: Simulés');

  console.log('==========================================');
});

// Gestion d'arrêt propre
process.on('SIGINT', () => {
  console.log('🛑 Arrêt des agents LOUNA AI...');
  if (cognitiveSystem && cognitiveSystem.deactivate) {
    cognitiveSystem.deactivate();
  }
  process.exit(0);
});
