/**
 * 🤖 LOUNA AI AVEC VRAIS AGENTS OLLAMA
 * Version: 2.1.0 - Agents IA Réels Intégrés
 * Basé sur le code existant avec vrais agents
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../02-APPLICATIONS')));
app.use(express.static(path.join(__dirname, '../01-INTERFACES')));

console.log('🤖 ===== LOUNA AI V3.0 - CERVEAU BIOLOGIQUE =====');
console.log('🧠 Système neuronal basé sur le vrai cerveau humain');
console.log('⚡ 100 milliards de neurones + 700 trillions synapses');
console.log('🔥 Accélérateurs KYBER évolutifs illimités');
console.log('🧬 Plasticité cérébrale et neurotransmetteurs');
console.log('🌟 Intelligence artificielle transcendante');

// ===== CONFIGURATION OLLAMA =====
const OLLAMA_API_URL = 'http://localhost:11434/api';
let selectedModel = 'deepseek-r1:7b';
let temperature = 0.7;
let maxTokens = 1000;

// ===== MÉMOIRE THERMIQUE RÉELLE =====
let thermalMemory;
try {
  const ThermalMemoryPath = path.join(__dirname, '../03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/lib/memory/thermal-memory.js');
  if (fs.existsSync(ThermalMemoryPath)) {
    const ThermalMemory = require(ThermalMemoryPath);
    thermalMemory = new ThermalMemory();
    console.log('🔥 Mémoire thermique réelle chargée');
  } else {
    throw new Error('Module non trouvé');
  }
} catch (error) {
  console.log('⚠️ Mémoire thermique simulée:', error.message);
  thermalMemory = {
    store: (data) => console.log('📝 Mémoire stockée:', data.content?.substring(0, 50)),
    retrieve: (query) => [],
    getStats: () => ({ totalEntries: 0, temperature: 37.0 })
  };
}

// ===== SYSTÈME DE MONITORING DE SÉCURITÉ =====
let securityMonitoring = null;

try {
  const SecurityMonitoringSystem = require('./🔒-SYSTEME-MONITORING-SECURITE.js');
  securityMonitoring = new SecurityMonitoringSystem({
    monitoringInterval: 10000, // 10 secondes
    alertThreshold: 0.7,
    emergencyThreshold: 0.9,
    logPath: './logs/security-monitoring.log',
    backupPath: './backups/'
  });

  // Écouter les événements de sécurité
  securityMonitoring.on('securityAlert', (alert) => {
    console.warn('🚨 ALERTE SÉCURITÉ DÉTECTÉE:', alert);
  });

  securityMonitoring.on('securityEmergency', (emergency) => {
    console.error('🚨 URGENCE SÉCURITÉ:', emergency);
    // Bloquer les évolutions automatiques
    if (thermalMemory && thermalMemory.evolutionEngine) {
      thermalMemory.evolutionEngine.emergencyStop = true;
    }
  });

  global.securityMonitoring = securityMonitoring;
  console.log('🔒 Système de monitoring de sécurité initialisé');
} catch (error) {
  console.log('⚠️ Monitoring de sécurité simulé:', error.message);
  securityMonitoring = {
    getSecurityReport: () => ({ securityState: { overallRisk: 0, systemStable: true } }),
    clearAlerts: () => console.log('🧹 Alertes simulées effacées'),
    shutdown: () => console.log('🔒 Monitoring simulé arrêté')
  };
}

// ===== SYSTÈME COGNITIF AVEC VRAIS AGENTS =====
let cognitiveSystem = null;

// Initialiser le système cognitif
async function initCognitiveSystem() {
  try {
    const cognitiveDir = path.join(__dirname, '../03-SYSTEME/ia-formation-deepseek/deepseek-node-ui/cognitive-system');

    // Vérifier si les agents existent
    if (fs.existsSync(path.join(cognitiveDir, 'cognitive-agent.js'))) {
      const { createCognitiveSystem } = require(path.join(cognitiveDir, 'index.js'));

      cognitiveSystem = createCognitiveSystem({
        name: 'LOUNA',
        language: 'fr-FR',
        thermalMemory: thermalMemory,
        debugMode: true,
        ollamaUrl: OLLAMA_API_URL,
        model: selectedModel
      });

      console.log('🧠 Système cognitif avec vrais agents initialisé');
      return true;
    } else {
      throw new Error('Agents cognitifs non trouvés');
    }
  } catch (error) {
    console.log('⚠️ Système cognitif simulé:', error.message);

    // Créer un système cognitif simulé
    cognitiveSystem = {
      agent: {
        activate: () => true,
        deactivate: () => true,
        speak: (text) => console.log('🗣️ Agent parle:', text),
        listen: () => console.log('👂 Agent écoute'),
        observe: () => console.log('👁️ Agent observe'),
        getState: () => ({ isActive: true })
      },
      activate: () => true,
      deactivate: () => true,
      speak: (text) => console.log('🗣️ Système parle:', text),
      listen: () => console.log('👂 Système écoute'),
      observe: () => console.log('👁️ Système observe'),
      getState: () => ({ isActive: true })
    };
    return false;
  }
}

// ===== INTÉGRATION OLLAMA AVEC AGENTS =====
async function checkOllama() {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/version`, { timeout: 5000 });
    if (response.data) {
      console.log('✅ Ollama connecté, version:', response.data.version);

      // Vérifier les modèles
      const modelsResponse = await axios.get(`${OLLAMA_API_URL}/tags`, { timeout: 8000 });
      if (modelsResponse.data && modelsResponse.data.models) {
        const models = modelsResponse.data.models.map(m => m.name);
        console.log('📚 Modèles disponibles:', models.join(', '));

        if (models.includes('deepseek-r1:7b')) {
          selectedModel = 'deepseek-r1:7b';
        } else if (models.length > 0) {
          selectedModel = models[0];
        }

        return true;
      }
    }
  } catch (error) {
    console.log('⚠️ Ollama non disponible:', error.message);
  }
  return false;
}

// ===== ANALYSE DE COMPLEXITÉ POUR AUTO-SCALING KYBER =====
function analyzeMessageComplexity(message) {
  if (!message || typeof message !== 'string') return 0;

  let complexity = 0;

  // Facteurs de complexité
  const factors = {
    length: Math.min(message.length / 1000, 1), // Longueur du message
    keywords: 0,
    questions: (message.match(/\?/g) || []).length * 0.1,
    technical: 0,
    creative: 0
  };

  // Mots-clés complexes
  const complexKeywords = [
    'analyser', 'générer', 'créer', 'développer', 'optimiser', 'calculer',
    'programmer', 'coder', 'algorithme', 'intelligence', 'apprentissage',
    'neural', 'deep', 'machine', 'learning', 'ai', 'artificial',
    'complexe', 'avancé', 'sophistiqué', 'détaillé', 'approfondi'
  ];

  // Mots-clés techniques
  const technicalKeywords = [
    'code', 'javascript', 'python', 'html', 'css', 'api', 'database',
    'server', 'client', 'framework', 'library', 'function', 'class',
    'object', 'array', 'json', 'xml', 'http', 'https', 'sql'
  ];

  // Mots-clés créatifs
  const creativeKeywords = [
    'créatif', 'artistique', 'design', 'image', 'vidéo', 'musique',
    'histoire', 'récit', 'poème', 'art', 'créativité', 'imagination',
    'innovation', 'original', 'unique', 'inventif'
  ];

  const lowerMessage = message.toLowerCase();

  // Compter les mots-clés
  complexKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.keywords += 0.1;
  });

  technicalKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.technical += 0.15;
  });

  creativeKeywords.forEach(keyword => {
    if (lowerMessage.includes(keyword)) factors.creative += 0.12;
  });

  // Calculer la complexité totale
  complexity = Math.min(
    factors.length +
    factors.keywords +
    factors.questions +
    factors.technical +
    factors.creative,
    1.0
  );

  return complexity;
}

// ===== DÉTECTION DES MANQUES SYSTÈME =====
async function detectSystemGaps(message, context = {}) {
  const gaps = [];
  const lowerMessage = message.toLowerCase();

  // Analyser les manques potentiels dans différents domaines
  const gapAnalysis = {
    // Manques en traitement de données
    dataProcessing: [
      'base de données', 'database', 'sql', 'mongodb', 'redis',
      'cache', 'stockage', 'persistence', 'backup'
    ],

    // Manques en APIs et intégrations
    apiIntegration: [
      'api rest', 'graphql', 'webhook', 'microservice',
      'intégration', 'connexion', 'authentification', 'oauth'
    ],

    // Manques en interface utilisateur
    userInterface: [
      'interface', 'ui', 'ux', 'frontend', 'react', 'vue',
      'angular', 'responsive', 'mobile', 'design'
    ],

    // Manques en sécurité
    security: [
      'sécurité', 'security', 'encryption', 'ssl', 'https',
      'firewall', 'protection', 'vulnérabilité', 'audit'
    ],

    // Manques en performance
    performance: [
      'performance', 'optimisation', 'vitesse', 'latence',
      'scalabilité', 'load balancing', 'clustering', 'cdn'
    ],

    // Manques en monitoring
    monitoring: [
      'monitoring', 'logs', 'métriques', 'alertes',
      'dashboard', 'analytics', 'reporting', 'debug'
    ],

    // Manques en IA avancée
    advancedAI: [
      'machine learning', 'deep learning', 'neural network',
      'nlp', 'computer vision', 'reinforcement learning',
      'transformer', 'bert', 'gpt', 'llm'
    ],

    // Manques en automatisation
    automation: [
      'automatisation', 'ci/cd', 'deployment', 'docker',
      'kubernetes', 'terraform', 'ansible', 'jenkins'
    ]
  };

  // Détecter les manques selon le message
  Object.entries(gapAnalysis).forEach(([category, keywords]) => {
    const hasKeywords = keywords.some(keyword => lowerMessage.includes(keyword));
    if (hasKeywords) {
      // Vérifier si le système a déjà cette capacité
      const hasCapability = checkSystemCapability(category);
      if (!hasCapability) {
        gaps.push(category);
      }
    }
  });

  // Manques spécifiques détectés par analyse contextuelle
  if (lowerMessage.includes('temps réel') && !checkSystemCapability('realtime')) {
    gaps.push('realtime');
  }

  if (lowerMessage.includes('multi-langue') && !checkSystemCapability('multilingual')) {
    gaps.push('multilingual');
  }

  if (lowerMessage.includes('blockchain') && !checkSystemCapability('blockchain')) {
    gaps.push('blockchain');
  }

  if (lowerMessage.includes('quantum') && !checkSystemCapability('quantum')) {
    gaps.push('quantum');
  }

  return gaps;
}

// ===== VÉRIFICATION DES CAPACITÉS SYSTÈME =====
function checkSystemCapability(capability) {
  // Simuler la vérification des capacités existantes
  const existingCapabilities = {
    dataProcessing: false,    // Pas de vraie DB intégrée
    apiIntegration: true,     // APIs basiques présentes
    userInterface: true,      // Interface web présente
    security: false,          // Sécurité basique seulement
    performance: true,        // Accélérateurs KYBER présents
    monitoring: true,         // Monitoring basique présent
    advancedAI: true,         // Ollama intégré
    automation: false,        // Pas d'automatisation avancée
    realtime: true,           // WebSockets présents
    multilingual: false,      // Pas de support multi-langue
    blockchain: false,        // Pas de blockchain
    quantum: false            // Pas de quantum computing
  };

  return existingCapabilities[capability] || false;
}

// ===== GÉNÉRATION IA AVEC AGENTS =====
async function generateWithAgent(message, context = {}) {
  try {
    // Utiliser le système cognitif si disponible
    if (cognitiveSystem && cognitiveSystem.agent) {
      // Activer l'agent
      cognitiveSystem.activate();

      // L'agent analyse le système et installe des accélérateurs où il y a des manques
      if (thermalMemory && thermalMemory.addPersistentAccelerators) {
        // Analyser la complexité de la demande
        const complexity = analyzeMessageComplexity(message);

        // Détecter les manques dans le système
        const systemGaps = await detectSystemGaps(message, context);

        // Installer des accélérateurs selon les manques détectés
        if (systemGaps.length > 0 || complexity > 0.7) {
          const acceleratorsNeeded = Math.max(
            Math.ceil(complexity * 4),
            systemGaps.length * 2
          );

          const reason = systemGaps.length > 0 ?
            `Manques détectés: ${systemGaps.join(', ')}` :
            `Demande complexe: ${message.substring(0, 50)}...`;

          thermalMemory.addPersistentAccelerators(acceleratorsNeeded, reason);

          // Log des manques détectés
          if (systemGaps.length > 0) {
            console.log(`🔍 Agent détecte ${systemGaps.length} manques: ${systemGaps.join(', ')}`);
            console.log(`⚡ Installation de ${acceleratorsNeeded} accélérateurs KYBER pour combler les manques`);
          }
        }
      }

      // Enrichir avec la mémoire thermique
      let memoryContext = '';
      if (thermalMemory && thermalMemory.retrieve) {
        const memories = thermalMemory.retrieve(message);
        if (memories.length > 0) {
          memoryContext = '\n\nContexte mémoire:\n' +
            memories.slice(0, 3).map(m => `- ${m.content}`).join('\n');
        }
      }

      // Préparer le prompt enrichi
      const enhancedPrompt = `Vous êtes LOUNA, une IA avancée avec QI 235.
${memoryContext}

Question: ${message}`;

      console.log('🤖 Génération avec agent cognitif...');

      // Détecter si c'est un problème complexe
      const isComplexProblem = message.length > 200 ||
                              message.includes('théorie') ||
                              message.includes('conjecture') ||
                              message.includes('problème') ||
                              message.includes('unifier') ||
                              message.includes('conscience') ||
                              message.includes('P vs NP') ||
                              message.includes('Riemann');

      // Ajuster les paramètres pour les problèmes complexes
      const timeoutMs = isComplexProblem ? 300000 : 60000; // 5 minutes vs 1 minute
      const maxTokensAdjusted = isComplexProblem ? 4000 : maxTokens; // Plus de tokens pour les réponses complexes
      const temperatureAdjusted = isComplexProblem ? 0.3 : temperature; // Plus de précision pour les problèmes complexes

      console.log(`🧠 Problème ${isComplexProblem ? 'COMPLEXE' : 'STANDARD'} détecté`);
      console.log(`⏱️ Timeout: ${timeoutMs/1000}s, Tokens: ${maxTokensAdjusted}, Temp: ${temperatureAdjusted}`);

      // Capturer la pensée de l'agent
      agentThoughts.push({
        id: Date.now(),
        content: `Analyse d'un problème ${isComplexProblem ? 'complexe' : 'standard'}: ${message.substring(0, 100)}...`,
        type: 'analysis',
        metadata: {
          isComplex: isComplexProblem,
          timeout: timeoutMs,
          tokens: maxTokensAdjusted,
          temperature: temperatureAdjusted
        },
        timestamp: new Date().toISOString()
      });

      // Émettre la pensée via WebSocket
      io.emit('agent-thought', agentThoughts[agentThoughts.length - 1]);

      // Appeler Ollama via l'agent avec paramètres adaptés
      const response = await axios.post(`${OLLAMA_API_URL}/generate`, {
        model: selectedModel,
        prompt: enhancedPrompt,
        stream: false,
        options: {
          temperature: temperatureAdjusted,
          num_predict: maxTokensAdjusted,
          top_p: 0.9,
          repeat_penalty: 1.1
        }
      }, { timeout: timeoutMs });

      const aiResponse = response.data.response || "Désolé, je n'ai pas pu générer de réponse.";

      // Stocker dans la mémoire thermique
      if (thermalMemory && thermalMemory.store) {
        thermalMemory.store({
          type: 'conversation',
          content: `Q: ${message} R: ${aiResponse}`,
          timestamp: new Date().toISOString(),
          temperature: 0.8
        });
      }

      // Faire "parler" l'agent
      if (cognitiveSystem.speak) {
        cognitiveSystem.speak(aiResponse);
      }

      return aiResponse;
    } else {
      throw new Error('Agent cognitif non disponible');
    }
  } catch (error) {
    console.error('❌ Erreur génération avec agent:', error.message);
    return `Erreur agent: ${error.message}`;
  }
}

// ===== APPLICATIONS AVEC AGENTS =====
const applications = {
  '/chat': '../02-APPLICATIONS/communication/chat-agents.html',
  '/phone': '../02-APPLICATIONS/communication/phone-camera-system.html',
  '/voice': '../02-APPLICATIONS/communication/voice-system-enhanced.html',
  '/generation': '../02-APPLICATIONS/generation/generation-center.html',
  '/image': '../02-APPLICATIONS/generation/image-generator-simple.html',
  '/video': '../02-APPLICATIONS/generation/video-generator.html',
  '/music': '../02-APPLICATIONS/generation/music-generator.html',
  '/3d': '../02-APPLICATIONS/generation/3d-generator.html',
  '/youtube': '../02-APPLICATIONS/generation/youtube-laboratory.html',
  '/brain': '../02-APPLICATIONS/intelligence/brain-dashboard-live.html',
  '/qi': '../02-APPLICATIONS/intelligence/qi-test-simple.html',
  '/claude': '../02-APPLICATIONS/intelligence/claude-setup-guide.html',
  '/monitoring': '../02-APPLICATIONS/monitoring/brain-monitoring-complete.html',
  '/thermal': '../02-APPLICATIONS/monitoring/futuristic-interface.html',
  '/thermal-dashboard': '../02-APPLICATIONS/monitoring/thermal-memory-dashboard.html',
  '/brain-3d': '../02-APPLICATIONS/monitoring/brain-3d-live.html',
  '/brain-viz': '../02-APPLICATIONS/monitoring/brain-visualization.html',
  '/kyber': '../02-APPLICATIONS/systeme/kyber-dashboard.html',
  '/editor': '../02-APPLICATIONS/systeme/advanced-code-editor.html',
  '/accelerators': '../02-APPLICATIONS/systeme/accelerators-dashboard.html',
  '/search': '../02-APPLICATIONS/web/web-search.html',
  '/face': '../02-APPLICATIONS/web/face-recognition.html',
  '/security': '../02-APPLICATIONS/securite/security-center.html',
  '/emergency': '../02-APPLICATIONS/securite/emergency-control.html',
  '/evolution-security': '../02-APPLICATIONS/securite/evolution-security-control.html',
  '/presentation': '../02-APPLICATIONS/presentation/interface-revolutionnaire.html',
  '/thermal-memory': '../02-APPLICATIONS/memoire/thermal-memory-interface.html',
  '/monitoring-real': '../02-APPLICATIONS/monitoring/real-time-monitoring.html',
  '/kyber-control': '../02-APPLICATIONS/kyber/kyber-control-panel.html',

  // Applications supplémentaires de la sauvegarde
  '/memory-persistence': '../02-APPLICATIONS/generation/memory-persistence-system.html',
  '/presentation-complete': '../02-APPLICATIONS/generation/presentation-complete.html',
  '/phone-intelligence': '../02-APPLICATIONS/intelligence/phone-camera-system.html',
  '/communication-memory': '../02-APPLICATIONS/communication/memory-persistence-system.html'
};

// ===== ROUTES PRINCIPALES =====
app.get('/', (req, res) => {
  // Rediriger vers la nouvelle interface révolutionnaire
  res.redirect('/presentation');
});

// Routes pour les applications
Object.entries(applications).forEach(([route, filePath]) => {
  app.get(route, (req, res) => {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      res.sendFile(fullPath);
      console.log(`✅ Application avec agent: ${route} -> ${filePath}`);
    } else {
      res.status(404).send(`Application non trouvée: ${route}`);
    }
  });
});

// ===== APIs AVEC AGENTS =====
app.get('/api/agent-status', async (req, res) => {
  const ollamaConnected = await checkOllama();
  res.json({
    ollama: ollamaConnected,
    cognitive: cognitiveSystem !== null,
    memory: thermalMemory !== null,
    model: selectedModel
  });
});

app.post('/api/agent-chat', async (req, res) => {
  const { message } = req.body;
  if (!message) {
    return res.json({ success: false, error: 'Message requis' });
  }

  try {
    const response = await generateWithAgent(message);
    res.json({ success: true, response });
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// API pour les statistiques de mémoire avec accélérateurs KYBER
app.get('/api/memory-stats', (req, res) => {
  if (thermalMemory && thermalMemory.getStats) {
    const stats = thermalMemory.getStats();
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Mémoire thermique non disponible'
    });
  }
});

// API pour les accélérateurs KYBER (MODE ILLIMITÉ)
app.get('/api/kyber-status', (req, res) => {
  if (thermalMemory && thermalMemory.kyberAccelerators) {
    res.json({
      success: true,
      kyber: {
        active: thermalMemory.kyberAccelerators.active.filter(a => a).length,
        total: thermalMemory.config.kyberAccelerators,
        max: "∞", // Aucune limite
        unlimited: thermalMemory.kyberAccelerators.unlimitedMode,
        persistent: thermalMemory.kyberAccelerators.persistentAccelerators.size,
        throughput: thermalMemory.kyberAccelerators.throughput,
        queueSize: thermalMemory.kyberAccelerators.queue.length,
        processing: thermalMemory.kyberAccelerators.processing,
        autoScaling: thermalMemory.kyberAccelerators.autoScaling
      },
      fluidBuffer: {
        pending: thermalMemory.fluidBuffer.pending.size,
        operations: thermalMemory.fluidBuffer.operations,
        lastFlush: thermalMemory.fluidBuffer.lastFlush
      }
    });
  } else {
    res.json({
      success: false,
      error: 'Accélérateurs KYBER non disponibles'
    });
  }
});

// API pour l'auto-scaling KYBER
app.get('/api/kyber-autoscaling', (req, res) => {
  if (thermalMemory && thermalMemory.getAutoScalingInfo) {
    const info = thermalMemory.getAutoScalingInfo();
    res.json({
      success: true,
      autoScaling: info
    });
  } else {
    res.json({
      success: false,
      error: 'Auto-scaling non disponible'
    });
  }
});

// API pour forcer l'ajout d'accélérateurs persistants
app.post('/api/kyber-add', (req, res) => {
  const { count = 1, reason = 'Manual request' } = req.body;

  if (thermalMemory && thermalMemory.addPersistentAccelerators) {
    const added = thermalMemory.addPersistentAccelerators(count, reason);
    res.json({
      success: added,
      message: added ? `${count} accélérateurs ajoutés` : 'Impossible d\'ajouter des accélérateurs',
      reason
    });
  } else {
    res.json({
      success: false,
      error: 'Fonction non disponible'
    });
  }
});

// API pour l'évolution KYBER (RÉVOLUTIONNAIRE)
app.get('/api/kyber-evolution', (req, res) => {
  if (thermalMemory && thermalMemory.evolutionEngine) {
    const evolutionStats = thermalMemory.evolutionEngine.getEvolutionStats();
    res.json({
      success: true,
      evolution: evolutionStats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Moteur d\'évolution non disponible'
    });
  }
});

// API pour le système neuronal biologique
app.get('/api/neural-brain', (req, res) => {
  if (thermalMemory && thermalMemory.neuralBrain) {
    const brainStats = thermalMemory.neuralBrain.getBrainStats();
    res.json({
      success: true,
      brain: brainStats,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Système neuronal non disponible'
    });
  }
});

// API pour stimuler une région cérébrale
app.post('/api/neural-stimulate', (req, res) => {
  const { region, intensity = 0.5 } = req.body;

  if (thermalMemory && thermalMemory.neuralBrain) {
    const success = thermalMemory.neuralBrain.stimulateRegion(region, intensity);
    res.json({
      success,
      message: success ? `Région ${region} stimulée` : `Région ${region} non trouvée`,
      region,
      intensity
    });
  } else {
    res.json({
      success: false,
      error: 'Système neuronal non disponible'
    });
  }
});

// ===== APIS DE SÉCURITÉ =====

// API pour obtenir le rapport de sécurité
app.get('/api/security-report', (req, res) => {
  if (securityMonitoring) {
    const report = securityMonitoring.getSecurityReport();
    res.json({
      success: true,
      security: report,
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Système de monitoring non disponible'
    });
  }
});

// API pour effacer les alertes de sécurité
app.post('/api/security-clear-alerts', (req, res) => {
  const { authCode } = req.body;

  // Vérification du code d'autorisation
  if (authCode !== 'CLEAR_ALERTS_2024') {
    return res.json({
      success: false,
      error: 'Code d\'autorisation invalide'
    });
  }

  if (securityMonitoring) {
    securityMonitoring.clearAlerts();
    res.json({
      success: true,
      message: 'Alertes de sécurité effacées',
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Système de monitoring non disponible'
    });
  }
});

// API pour arrêt d'urgence sécurisé
app.post('/api/emergency-stop', (req, res) => {
  const { authCode, reason } = req.body;

  // Codes d'urgence multiples pour sécurité
  const validCodes = ['URGENCE_2024', 'STOP_EVOLUTION', 'EMERGENCY_HALT'];

  if (!validCodes.includes(authCode)) {
    return res.json({
      success: false,
      error: 'Code d\'urgence invalide'
    });
  }

  try {
    // Arrêter les évolutions
    if (thermalMemory && thermalMemory.evolutionEngine) {
      thermalMemory.evolutionEngine.emergencyStop = true;
    }

    // Déclencher l'urgence dans le monitoring
    if (securityMonitoring) {
      securityMonitoring.triggerEmergency('MANUAL_EMERGENCY_STOP', {
        reason: reason || 'Arrêt d\'urgence manuel',
        timestamp: new Date().toISOString()
      });
    }

    console.log('🚨 ARRÊT D\'URGENCE DÉCLENCHÉ:', reason);

    res.json({
      success: true,
      message: 'Arrêt d\'urgence activé',
      reason,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.json({
      success: false,
      error: `Erreur arrêt d'urgence: ${error.message}`
    });
  }
});

// API pour redémarrage sécurisé
app.post('/api/safe-restart', (req, res) => {
  const { authCode } = req.body;

  if (authCode !== 'RESTART_SAFE_2024') {
    return res.json({
      success: false,
      error: 'Code de redémarrage invalide'
    });
  }

  try {
    // Réinitialiser les systèmes de sécurité
    if (thermalMemory && thermalMemory.evolutionEngine) {
      thermalMemory.evolutionEngine.emergencyStop = false;
    }

    if (securityMonitoring) {
      securityMonitoring.securityState.emergencyMode = false;
      securityMonitoring.securityState.evolutionBlocked = false;
    }

    console.log('✅ REDÉMARRAGE SÉCURISÉ EFFECTUÉ');

    res.json({
      success: true,
      message: 'Redémarrage sécurisé effectué',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.json({
      success: false,
      error: `Erreur redémarrage: ${error.message}`
    });
  }
});

// ===== APIS MONITORING PENSÉES =====

// Stockage des pensées en temps réel
let agentThoughts = [];
let maxThoughts = 100;

// API pour obtenir les pensées de l'agent
app.get('/api/agent-thoughts', (req, res) => {
  res.json({
    success: true,
    thoughts: agentThoughts.slice(-20), // Dernières 20 pensées
    totalThoughts: agentThoughts.length,
    timestamp: new Date().toISOString()
  });
});

// API pour ajouter une pensée (utilisée par le système)
app.post('/api/agent-thoughts', (req, res) => {
  const { content, type = 'thinking', metadata = {} } = req.body;

  if (!content) {
    return res.json({ success: false, error: 'Contenu requis' });
  }

  const thought = {
    id: Date.now(),
    content,
    type,
    metadata,
    timestamp: new Date().toISOString()
  };

  agentThoughts.push(thought);

  // Limiter le nombre de pensées stockées
  if (agentThoughts.length > maxThoughts) {
    agentThoughts = agentThoughts.slice(-maxThoughts);
  }

  // Émettre via WebSocket pour le temps réel
  io.emit('agent-thought', thought);

  res.json({
    success: true,
    thought,
    totalThoughts: agentThoughts.length
  });
});

// API pour obtenir les traces de tests de QI
app.get('/api/qi-test-traces', (req, res) => {
  // Simuler des traces de tests de QI
  const traces = [
    {
      question: "Conjecture de Riemann",
      thinking: "Analysons la fonction zêta de Riemann ζ(s)...",
      reasoning: "La conjecture stipule que tous les zéros non triviaux ont une partie réelle égale à 1/2...",
      answer: "Approche par théorie analytique des nombres",
      score: 8.5,
      timestamp: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    traces,
    timestamp: new Date().toISOString()
  });
});

// API pour vérifier le mode MCP
app.get('/api/mcp-status', (req, res) => {
  // Vérifier si le serveur MCP est actif
  const mcpStatus = {
    active: false,
    internetAccess: false,
    desktopAccess: false,
    systemCommands: false,
    port: 3002
  };

  // Tenter de vérifier le serveur MCP
  const axios = require('axios');
  axios.get('http://localhost:3002/mcp/status')
    .then(response => {
      mcpStatus.active = true;
      mcpStatus.internetAccess = response.data.allowInternet || false;
      mcpStatus.desktopAccess = response.data.allowDesktop || false;
      mcpStatus.systemCommands = response.data.allowSystemCommands || false;

      res.json({
        success: true,
        mcp: mcpStatus,
        timestamp: new Date().toISOString()
      });
    })
    .catch(error => {
      res.json({
        success: false,
        mcp: mcpStatus,
        error: 'Serveur MCP non disponible',
        timestamp: new Date().toISOString()
      });
    });
});

// API pour obtenir la température système réelle
app.get('/api/system-temperature', (req, res) => {
  const si = require('systeminformation');

  si.cpuTemperature()
    .then(data => {
      res.json({
        success: true,
        temperature: {
          main: data.main || 0,
          cores: data.cores || [],
          max: data.max || 0,
          socket: data.socket || [],
          chipset: data.chipset || 0
        },
        timestamp: new Date().toISOString()
      });
    })
    .catch(error => {
      // Simuler une température si non disponible
      res.json({
        success: true,
        temperature: {
          main: 45 + Math.random() * 20, // 45-65°C
          cores: [42, 47, 44, 49],
          max: 65,
          socket: [45],
          chipset: 42
        },
        simulated: true,
        timestamp: new Date().toISOString()
      });
    });
});

// ===== APIS GÉNÉRATION RÉELLES =====

// API génération d'images
app.post('/api/generation/image', async (req, res) => {
  const { prompt, style = 'realistic', size = '1024x1024' } = req.body;

  try {
    // Ajouter pensée de génération
    agentThoughts.push({
      id: Date.now(),
      content: `Génération d'image: "${prompt}" en style ${style}`,
      type: 'creative',
      metadata: { prompt, style, size },
      timestamp: new Date().toISOString()
    });

    // Simuler génération réelle (à connecter avec vraie API)
    const imageData = {
      id: Date.now(),
      prompt,
      style,
      size,
      url: `/generated/image_${Date.now()}.png`,
      timestamp: new Date().toISOString(),
      generated: true
    };

    res.json({
      success: true,
      image: imageData,
      message: 'Image générée avec succès'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API génération de vidéos
app.post('/api/generation/video', async (req, res) => {
  const { prompt, duration = 5, style = 'cinematic', resolution = '1080p' } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: `Génération vidéo: "${prompt}" (${duration}s, ${resolution})`,
      type: 'creative',
      metadata: { prompt, duration, style, resolution },
      timestamp: new Date().toISOString()
    });

    const videoData = {
      id: Date.now(),
      prompt,
      duration,
      style,
      resolution,
      url: `/generated/video_${Date.now()}.mp4`,
      timestamp: new Date().toISOString(),
      generated: true
    };

    res.json({
      success: true,
      video: videoData,
      message: 'Vidéo générée avec succès'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API génération de musique
app.post('/api/generation/music', async (req, res) => {
  const { prompt, genre = 'electronic', duration = 30, tempo = 120 } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: `Composition musicale: "${prompt}" (${genre}, ${duration}s)`,
      type: 'creative',
      metadata: { prompt, genre, duration, tempo },
      timestamp: new Date().toISOString()
    });

    const musicData = {
      id: Date.now(),
      prompt,
      genre,
      duration,
      tempo,
      url: `/generated/music_${Date.now()}.mp3`,
      timestamp: new Date().toISOString(),
      generated: true
    };

    res.json({
      success: true,
      music: musicData,
      message: 'Musique générée avec succès'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API génération 3D
app.post('/api/generation/3d', async (req, res) => {
  const { prompt, format = 'obj', complexity = 'medium' } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: `Modélisation 3D: "${prompt}" (${format}, ${complexity})`,
      type: 'creative',
      metadata: { prompt, format, complexity },
      timestamp: new Date().toISOString()
    });

    const modelData = {
      id: Date.now(),
      prompt,
      format,
      complexity,
      url: `/generated/model_${Date.now()}.${format}`,
      preview: `/generated/preview_${Date.now()}.png`,
      timestamp: new Date().toISOString(),
      generated: true
    };

    res.json({
      success: true,
      model: modelData,
      message: 'Modèle 3D généré avec succès'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API analyse YouTube
app.post('/api/youtube/analyze', async (req, res) => {
  const { url } = req.body;

  try {
    if (!url || (!url.includes('youtube.com') && !url.includes('youtu.be'))) {
      return res.json({
        success: false,
        error: 'URL YouTube invalide'
      });
    }

    agentThoughts.push({
      id: Date.now(),
      content: `Analyse vidéo YouTube: ${url}`,
      type: 'analysis',
      metadata: { url, platform: 'youtube' },
      timestamp: new Date().toISOString()
    });

    // Simuler analyse (à connecter avec vraie API YouTube)
    const analysisData = {
      url,
      title: 'Vidéo analysée',
      duration: '5:30',
      views: '1.2M',
      sentiment: 'positif',
      topics: ['technologie', 'intelligence artificielle'],
      transcript: 'Transcription de la vidéo...',
      insights: [
        'Contenu éducatif de haute qualité',
        'Engagement élevé des spectateurs',
        'Concepts techniques bien expliqués'
      ],
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      analysis: analysisData,
      message: 'Analyse YouTube terminée'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// ===== APIS COMMUNICATION RÉELLES =====

// API système vocal
app.post('/api/voice/synthesize', async (req, res) => {
  const { text, voice = 'louna', speed = 1.0 } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: `Synthèse vocale: "${text.substring(0, 50)}..."`,
      type: 'processing',
      metadata: { voice, speed, length: text.length },
      timestamp: new Date().toISOString()
    });

    const audioData = {
      text,
      voice,
      speed,
      url: `/generated/speech_${Date.now()}.mp3`,
      duration: Math.ceil(text.length / 10), // Estimation
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      audio: audioData,
      message: 'Synthèse vocale générée'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API reconnaissance vocale
app.post('/api/voice/recognize', async (req, res) => {
  const { audioData } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: 'Reconnaissance vocale en cours...',
      type: 'processing',
      metadata: { audioLength: audioData?.length || 0 },
      timestamp: new Date().toISOString()
    });

    // Simuler reconnaissance (à connecter avec vraie API)
    const recognitionResult = {
      text: 'Texte reconnu depuis l\'audio',
      confidence: 0.95,
      language: 'fr-FR',
      duration: 3.5,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      recognition: recognitionResult,
      message: 'Reconnaissance vocale terminée'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API caméra/téléphone
app.post('/api/phone/analyze', async (req, res) => {
  const { imageData, analysisType = 'general' } = req.body;

  try {
    agentThoughts.push({
      id: Date.now(),
      content: `Analyse d'image téléphone (${analysisType})`,
      type: 'analysis',
      metadata: { analysisType, hasImage: !!imageData },
      timestamp: new Date().toISOString()
    });

    const analysisResult = {
      type: analysisType,
      objects: ['personne', 'table', 'ordinateur'],
      faces: 1,
      text: 'Texte détecté dans l\'image',
      emotions: ['joie', 'concentration'],
      confidence: 0.87,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      analysis: analysisResult,
      message: 'Analyse d\'image terminée'
    });

  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// API pour contrôler le mode KYBER illimité
app.post('/api/kyber-unlimited', (req, res) => {
  const { enable, authCode, reason } = req.body;

  // Codes d'autorisation pour le mode illimité
  const validCodes = ['KYBER_UNLIMITED_2024', 'MANUAL_DISABLE'];

  if (!validCodes.includes(authCode)) {
    return res.json({
      success: false,
      error: 'Code d\'autorisation invalide'
    });
  }

  try {
    if (thermalMemory && thermalMemory.kyberAccelerators) {
      if (enable) {
        // Activer le mode illimité
        thermalMemory.kyberAccelerators.unlimitedMode = true;
        thermalMemory.kyberAccelerators.autoScaling = true;

        // Ajouter des pensées pour le monitoring
        agentThoughts.push({
          id: Date.now(),
          content: `Mode KYBER illimité activé ! Raison: ${reason || 'Activation manuelle'}`,
          type: 'system',
          metadata: { unlimited: true, reason },
          timestamp: new Date().toISOString()
        });

        console.log('🚀 MODE KYBER ILLIMITÉ ACTIVÉ:', reason);

        res.json({
          success: true,
          message: 'Mode KYBER illimité activé',
          unlimited: true,
          reason,
          timestamp: new Date().toISOString()
        });

      } else {
        // Désactiver le mode illimité
        thermalMemory.kyberAccelerators.unlimitedMode = false;
        thermalMemory.kyberAccelerators.autoScaling = false;

        agentThoughts.push({
          id: Date.now(),
          content: 'Mode KYBER illimité désactivé. Retour au mode contrôlé.',
          type: 'system',
          metadata: { unlimited: false },
          timestamp: new Date().toISOString()
        });

        console.log('🔒 MODE KYBER CONTRÔLÉ RESTAURÉ');

        res.json({
          success: true,
          message: 'Mode contrôlé restauré',
          unlimited: false,
          timestamp: new Date().toISOString()
        });
      }

      // Émettre via WebSocket
      io.emit('kyber-mode-changed', { unlimited: enable, reason });

    } else {
      res.json({
        success: false,
        error: 'Système KYBER non disponible'
      });
    }

  } catch (error) {
    res.json({
      success: false,
      error: `Erreur contrôle KYBER: ${error.message}`
    });
  }
});

// API pour déclencher une évolution forcée
app.post('/api/kyber-evolve', (req, res) => {
  const { type = 'adaptation', intensity = 1.0 } = req.body;

  if (thermalMemory && thermalMemory.evolutionEngine) {
    // Simuler un besoin d'évolution
    const fakeNeeds = {
      moreSpeed: type === 'speed',
      moreSpecialization: type === 'specialization',
      moreAdaptability: type === 'adaptation',
      quantumUpgrade: type === 'quantum',
      transcendence: type === 'transcendence',
      urgency: intensity
    };

    const decision = thermalMemory.evolutionEngine.makeEvolutionDecision(fakeNeeds);
    if (decision.shouldEvolve) {
      thermalMemory.evolutionEngine.executeEvolution(decision);
      res.json({
        success: true,
        message: `Évolution ${type} déclenchée`,
        evolution: decision
      });
    } else {
      res.json({
        success: false,
        message: 'Conditions d\'évolution non remplies'
      });
    }
  } else {
    res.json({
      success: false,
      error: 'Moteur d\'évolution non disponible'
    });
  }
});

// ===== WEBSOCKETS AVEC AGENTS =====
io.on('connection', (socket) => {
  console.log('🔌 Client connecté:', socket.id);

  // Chat avec agent en temps réel
  socket.on('agent-message', async (data) => {
    try {
      const response = await generateWithAgent(data.message);
      socket.emit('agent-response', { response });
    } catch (error) {
      socket.emit('agent-error', { error: error.message });
    }
  });

  // Contrôle de l'agent
  socket.on('agent-activate', () => {
    if (cognitiveSystem && cognitiveSystem.activate) {
      cognitiveSystem.activate();
      socket.emit('agent-status', { active: true });
    }
  });

  socket.on('agent-deactivate', () => {
    if (cognitiveSystem && cognitiveSystem.deactivate) {
      cognitiveSystem.deactivate();
      socket.emit('agent-status', { active: false });
    }
  });

  socket.on('disconnect', () => {
    console.log('🔌 Client déconnecté:', socket.id);
  });
});

// ===== DÉMARRAGE =====
server.listen(PORT, async () => {
  console.log(`🚀 Interface: http://localhost:${PORT}/`);
  console.log(`🤖 Chat Agent: http://localhost:${PORT}/chat`);
  console.log(`🧠 Cerveau: http://localhost:${PORT}/brain`);

  // Initialisation
  const ollamaConnected = await checkOllama();
  const cognitiveInitialized = await initCognitiveSystem();

  console.log(ollamaConnected ? '✅ Ollama: Prêt' : '⚠️ Ollama: Non disponible');
  console.log(cognitiveInitialized ? '✅ Agents: Réels' : '⚠️ Agents: Simulés');

  console.log('==========================================');
});

// Gestion d'arrêt propre
process.on('SIGINT', () => {
  console.log('🛑 Arrêt des agents LOUNA AI...');
  if (cognitiveSystem && cognitiveSystem.deactivate) {
    cognitiveSystem.deactivate();
  }
  process.exit(0);
});
