#!/bin/bash

# 🎯 LOUNA AI LAUNCHER - Interface Graphique
# Version: 2.1.0 - Launcher Graphique
# Double-cliquez pour lancer !

# Obtenir le répertoire du script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Fonction pour afficher le menu
show_menu() {
    clear
    echo "🧠 =================================================="
    echo "🎯        LOUNA AI LAUNCHER v2.1.0"
    echo "🚀        QI: 235 (<PERSON><PERSON>ie Exceptionnel)"
    echo "🧠 =================================================="
    echo ""
    echo "Choisissez une option :"
    echo ""
    echo "1) 🚀 Lancer LOUNA COMPLET (Version Test - 24 Apps)"
    echo "2) ⚡ Lancer LOUNA AUTO (Version Production Auto)"
    echo "3) 🔥 Lancer LOUNA STANDARD (Version Simple)"
    echo "4) 📊 Voir le statut des serveurs"
    echo "5) 🛑 Arrêter tous les serveurs"
    echo "6) 📝 Voir les logs"
    echo "7) ❌ Quitter"
    echo ""
    echo "🧠 =================================================="
    echo -n "Votre choix (1-7): "
}

# Fonction pour arrêter les serveurs
stop_servers() {
    echo "🛑 Arrêt des serveurs LOUNA..."
    
    # Arrêter les processus sur les ports utilisés
    for port in 3001 3002 3005; do
        if lsof -i:$port &> /dev/null; then
            echo "⚠️  Arrêt du processus sur le port $port..."
            kill $(lsof -t -i:$port) 2> /dev/null || true
        fi
    done
    
    sleep 2
    echo "✅ Tous les serveurs arrêtés"
}

# Fonction pour vérifier le statut
check_status() {
    echo "📊 Statut des serveurs LOUNA:"
    echo ""
    
    for port in 3001 3002 3005; do
        if lsof -i:$port &> /dev/null; then
            echo "✅ Port $port: ACTIF"
        else
            echo "❌ Port $port: INACTIF"
        fi
    done
    
    echo ""
    echo "Appuyez sur Entrée pour continuer..."
    read
}

# Fonction pour voir les logs
view_logs() {
    echo "📝 Logs récents:"
    echo ""
    
    LOG_DIR="$SCRIPT_DIR/logs"
    if [ -d "$LOG_DIR" ]; then
        ls -la "$LOG_DIR" | tail -10
    else
        echo "Aucun log trouvé"
    fi
    
    echo ""
    echo "Appuyez sur Entrée pour continuer..."
    read
}

# Fonction pour lancer LOUNA COMPLET
launch_complete() {
    echo "🚀 Lancement de LOUNA COMPLET..."
    stop_servers
    
    cd "$SCRIPT_DIR/04-SERVEURS"
    
    echo "🔥 Démarrage du serveur complet..."
    echo "🌐 Interface: http://localhost:3001/"
    echo "📋 Routes: http://localhost:3001/routes"
    echo ""
    echo "⚡ Appuyez sur Ctrl+C pour arrêter"
    echo ""
    
    node louna-test-complet.js
}

# Fonction pour lancer LOUNA AUTO
launch_auto() {
    echo "⚡ Lancement de LOUNA AUTO..."
    stop_servers
    
    cd "$SCRIPT_DIR/04-SERVEURS"
    
    echo "🤖 Démarrage du serveur automatique..."
    echo "🌐 Interface: http://localhost:3001/"
    echo ""
    echo "⚡ Appuyez sur Ctrl+C pour arrêter"
    echo ""
    
    node louna-production-auto-complet.js
}

# Fonction pour lancer LOUNA STANDARD
launch_standard() {
    echo "🔥 Lancement de LOUNA STANDARD..."
    stop_servers
    
    cd "$SCRIPT_DIR/04-SERVEURS"
    
    echo "📡 Démarrage du serveur standard..."
    echo "🌐 Interface: http://localhost:3001/"
    echo ""
    echo "⚡ Appuyez sur Ctrl+C pour arrêter"
    echo ""
    
    node louna-production-server.js
}

# Boucle principale
while true; do
    show_menu
    read choice
    
    case $choice in
        1)
            launch_complete
            ;;
        2)
            launch_auto
            ;;
        3)
            launch_standard
            ;;
        4)
            check_status
            ;;
        5)
            stop_servers
            echo ""
            echo "Appuyez sur Entrée pour continuer..."
            read
            ;;
        6)
            view_logs
            ;;
        7)
            echo "👋 Au revoir !"
            exit 0
            ;;
        *)
            echo "❌ Option invalide. Appuyez sur Entrée pour continuer..."
            read
            ;;
    esac
done
