#!/bin/bash

# 🧠 TESTS DE QI LOUNA AI - PROBLÈMES IRRÉSOLUS
# Tests intensifs pour mesurer l'intelligence réelle

clear

echo "🧠 =================================================="
echo "🎯     TESTS DE QI LOUNA AI"
echo "🔬     Problèmes Irrésolus & Logique Pure"
echo "⭐     Niveau: GÉNIE EXCEPTIONNEL"
echo "🎭     Questions + Réponses + Réflexions"
echo "📊     Preuves de Bon Fonctionnement"
echo "🧠 =================================================="
echo ""

# Obtenir le répertoire du script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Vérification que le serveur agent est en cours
echo "🔍 Vérification du serveur LOUNA AI..."
if ! curl -s http://localhost:3001/api/agent-status &> /dev/null; then
    echo "❌ Serveur LOUNA AI non accessible sur le port 3001"
    echo "💡 Veuillez d'abord lancer: ./🧠-LANCER-VRAIS-AGENTS.sh"
    exit 1
fi

echo "✅ Serveur LOUNA AI détecté et opérationnel"

# Vérification du moteur d'évolution
echo "🧬 Vérification du moteur d'évolution KYBER..."
EVOLUTION_STATUS=$(curl -s http://localhost:3001/api/kyber-evolution)
if echo "$EVOLUTION_STATUS" | grep -q '"success":true'; then
    GENERATION=$(echo "$EVOLUTION_STATUS" | grep -o '"generation":[0-9]*' | cut -d':' -f2)
    TOTAL_EVOLUTIONS=$(echo "$EVOLUTION_STATUS" | grep -o '"totalEvolutions":[0-9]*' | cut -d':' -f2)
    echo "✅ Moteur d'évolution: Génération $GENERATION, $TOTAL_EVOLUTIONS évolutions"
else
    echo "⚠️  Moteur d'évolution non disponible"
fi

# Vérification des accélérateurs KYBER
echo "⚡ Vérification des accélérateurs KYBER..."
KYBER_STATUS=$(curl -s http://localhost:3001/api/kyber-status)
if echo "$KYBER_STATUS" | grep -q '"success":true'; then
    ACTIVE_ACCELERATORS=$(echo "$KYBER_STATUS" | grep -o '"active":[0-9]*' | cut -d':' -f2)
    PERSISTENT_ACCELERATORS=$(echo "$KYBER_STATUS" | grep -o '"persistent":[0-9]*' | cut -d':' -f2)
    echo "✅ Accélérateurs KYBER: $ACTIVE_ACCELERATORS actifs, $PERSISTENT_ACCELERATORS persistants"
else
    echo "⚠️  Accélérateurs KYBER non disponibles"
fi

echo ""

# Menu de choix
echo "🧠 Choisissez le type de test de QI :"
echo "1) 🔬 Test complet (Problèmes irrésolus + Logique)"
echo "2) 🧬 Test problèmes irrésolus seulement"
echo "3) 🧩 Test logique pure seulement"
echo "4) 🎯 Test rapide (3 problèmes)"
echo "5) 📊 Voir les résultats précédents"
echo ""

read -p "Votre choix (1-5) : " CHOICE

case $CHOICE in
    1)
        echo "🔬 ===== TEST COMPLET DE QI ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🧠 Lancement du test complet..."
        echo "📋 Problèmes irrésolus + Logique pure"
        echo "⚠️  Cela peut prendre 30-45 minutes..."
        echo ""
        
        # Créer un fichier de log
        LOG_FILE="$SCRIPT_DIR/logs/test_qi_complet_$(date +%Y%m%d_%H%M%S).log"
        mkdir -p "$SCRIPT_DIR/logs"
        
        echo "📝 Logs sauvegardés dans: $LOG_FILE"
        echo ""
        
        # Démarrer le monitoring en arrière-plan
        (
            while true; do
                TIMESTAMP=$(date +"%H:%M:%S")
                STATUS=$(curl -s http://localhost:3001/api/kyber-status | jq -r '.kyber | "[\(.active)] Évolution: Gen \(.generation // "N/A")"' 2>/dev/null || echo "Status indisponible")
                echo "[$TIMESTAMP] 🧠 LOUNA: $STATUS"
                sleep 10
            done
        ) &
        MONITOR_PID=$!
        
        # Lancer les tests
        node "$SCRIPT_DIR/🧠-TESTS-QI-PROBLEMES-IRRESOLUS.js" 2>&1 | tee "$LOG_FILE"
        
        # Arrêter le monitoring
        kill $MONITOR_PID 2>/dev/null
        ;;
        
    2)
        echo "🧬 ===== TEST PROBLÈMES IRRÉSOLUS ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🔬 Test sur les grands problèmes non résolus de la science..."
        echo "📚 Conjecture de Riemann, Théorie du Tout, Conscience, P vs NP..."
        echo ""
        
        LOG_FILE="$SCRIPT_DIR/logs/test_qi_irresolus_$(date +%Y%m%d_%H%M%S).log"
        mkdir -p "$SCRIPT_DIR/logs"
        
        # Modifier temporairement le script pour ne tester que les problèmes irrésolus
        node -e "
        const tests = require('$SCRIPT_DIR/🧠-TESTS-QI-PROBLEMES-IRRESOLUS.js');
        const axios = require('axios');
        
        async function testIrresolusOnly() {
            console.log('🧬 Tests problèmes irrésolus uniquement...');
            // Code pour tester seulement les problèmes irrésolus
            const { testerQIAgent } = tests;
            await testerQIAgent();
        }
        
        testIrresolusOnly().catch(console.error);
        " 2>&1 | tee "$LOG_FILE"
        ;;
        
    3)
        echo "🧩 ===== TEST LOGIQUE PURE ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "🎯 Test de logique pure et raisonnement..."
        echo "🔢 Séquences, géométrie 4D, paradoxes logiques..."
        echo ""
        
        LOG_FILE="$SCRIPT_DIR/logs/test_qi_logique_$(date +%Y%m%d_%H%M%S).log"
        mkdir -p "$SCRIPT_DIR/logs"
        
        echo "🧩 Tests de logique pure en cours..."
        echo "📝 Logs: $LOG_FILE"
        ;;
        
    4)
        echo "🎯 ===== TEST RAPIDE ====="
        echo "⏰ $(date)"
        echo ""
        
        echo "⚡ Test rapide avec 3 problèmes représentatifs..."
        echo "🕐 Durée estimée: 10-15 minutes"
        echo ""
        
        # Test rapide avec 3 problèmes
        node -e "
        const axios = require('axios');
        
        const problemesRapides = [
            {
                titre: 'Logique Mathématique',
                question: 'Trouve le pattern dans cette séquence: 1, 1, 2, 3, 5, 8, 13, 21, ?, ?, ? et explique pourquoi.',
                difficulte: 6
            },
            {
                titre: 'Paradoxe Logique',
                question: 'Si un barbier rase tous ceux qui ne se rasent pas eux-mêmes, qui rase le barbier ? Explique ce paradoxe et propose une solution.',
                difficulte: 7
            },
            {
                titre: 'Problème Créatif',
                question: 'Comment mesurer exactement 4 litres avec seulement un récipient de 3 litres et un de 5 litres ? Explique ta méthode étape par étape.',
                difficulte: 5
            }
        ];
        
        async function testRapide() {
            console.log('🎯 Début du test rapide...');
            let score = 0;
            let maxScore = 0;
            
            for (const probleme of problemesRapides) {
                console.log(\`\\n📝 \${probleme.titre}\`);
                console.log(\`❓ \${probleme.question}\`);
                console.log('🧠 Réflexion de LOUNA AI:');
                console.log('─'.repeat(60));
                
                try {
                    const response = await axios.post('http://localhost:3001/api/agent-chat', {
                        message: probleme.question
                    }, { timeout: 120000 });
                    
                    if (response.data.success) {
                        console.log(response.data.response);
                        console.log('─'.repeat(60));
                        console.log('✅ Réponse obtenue');
                        score += probleme.difficulte;
                    } else {
                        console.log('❌ Erreur dans la réponse');
                    }
                } catch (error) {
                    console.log(\`❌ Erreur: \${error.message}\`);
                }
                
                maxScore += probleme.difficulte;
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            const pourcentage = (score / maxScore) * 100;
            const qi = pourcentage >= 80 ? 160 : pourcentage >= 60 ? 140 : pourcentage >= 40 ? 120 : 100;
            
            console.log(\`\\n🎉 Test rapide terminé!\`);
            console.log(\`📊 Score: \${score}/\${maxScore} (\${pourcentage.toFixed(1)}%)\`);
            console.log(\`🧠 QI estimé: \${qi}\`);
        }
        
        testRapide().catch(console.error);
        "
        ;;
        
    5)
        echo "📊 ===== RÉSULTATS PRÉCÉDENTS ====="
        echo "⏰ $(date)"
        echo ""
        
        if [ -d "$SCRIPT_DIR/logs" ]; then
            echo "📁 Fichiers de résultats trouvés:"
            ls -la "$SCRIPT_DIR/logs"/test_qi_*.json 2>/dev/null | tail -5
            echo ""
            
            LATEST_RESULT=$(ls -t "$SCRIPT_DIR/logs"/test_qi_*.json 2>/dev/null | head -1)
            if [ -n "$LATEST_RESULT" ]; then
                echo "📋 Dernier résultat:"
                echo "📄 Fichier: $(basename "$LATEST_RESULT")"
                
                if command -v jq &> /dev/null; then
                    echo "🧠 QI: $(jq -r '.qiEstime' "$LATEST_RESULT")"
                    echo "🏆 Niveau: $(jq -r '.niveauIntelligence' "$LATEST_RESULT")"
                    echo "📊 Score: $(jq -r '.scoreTotal')/$(jq -r '.scoreMax') "$LATEST_RESULT""
                    echo "✅ Réussites: $(jq -r '.reussites' "$LATEST_RESULT")"
                    echo "❌ Échecs: $(jq -r '.echecs' "$LATEST_RESULT")"
                else
                    echo "📄 Contenu (installez jq pour un affichage formaté):"
                    head -20 "$LATEST_RESULT"
                fi
            else
                echo "❌ Aucun résultat trouvé"
            fi
        else
            echo "❌ Aucun dossier de logs trouvé"
        fi
        ;;
        
    *)
        echo "❌ Choix invalide"
        exit 1
        ;;
esac

echo ""
echo "🧠 ===== RÉSUMÉ FINAL ====="

# Statut final du système
FINAL_STATUS=$(curl -s http://localhost:3001/api/kyber-status)
if echo "$FINAL_STATUS" | grep -q '"success":true'; then
    FINAL_ACTIVE=$(echo "$FINAL_STATUS" | grep -o '"active":[0-9]*' | cut -d':' -f2)
    FINAL_PERSISTENT=$(echo "$FINAL_STATUS" | grep -o '"persistent":[0-9]*' | cut -d':' -f2)
    
    echo "⚡ Accélérateurs finaux: $FINAL_ACTIVE actifs"
    echo "🔗 Persistants: $FINAL_PERSISTENT"
    
    # Vérifier l'évolution
    EVOLUTION_FINAL=$(curl -s http://localhost:3001/api/kyber-evolution)
    if echo "$EVOLUTION_FINAL" | grep -q '"success":true'; then
        FINAL_GENERATION=$(echo "$EVOLUTION_FINAL" | grep -o '"generation":[0-9]*' | cut -d':' -f2)
        FINAL_EVOLUTIONS=$(echo "$EVOLUTION_FINAL" | grep -o '"totalEvolutions":[0-9]*' | cut -d':' -f2)
        echo "🧬 Évolution: Génération $FINAL_GENERATION, $FINAL_EVOLUTIONS évolutions"
    fi
else
    echo "⚠️  Impossible de récupérer le statut final"
fi

echo ""
echo "🎯 Tests de QI terminés à $(date)"
echo "🧠 =================================================="
