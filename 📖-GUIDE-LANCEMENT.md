# 🚀 GUIDE DE LANCEMENT LOUNA AI v2.1.0

## 🎯 Boutons de Lancement Disponibles

### ⚡ LANCEMENT RAPIDE
**Fichier :** `⚡-LANCEMENT-RAPIDE.sh`
- **Usage :** Double-cliquez pour lancer immédiatement
- **Mode :** Version complète avec 24 applications
- **Port :** 3001
- **Interface :** http://localhost:3001/

### 🎯 LAUNCHER INTERACTIF
**Fichier :** `🎯-LOUNA-LAUNCHER.command`
- **Usage :** Double-cliquez pour ouvrir le menu
- **Options :**
  1. 🚀 LOUNA COMPLET (24 applications)
  2. ⚡ LOUNA AUTO (version automatique)
  3. 🔥 LOUNA STANDARD (version simple)
  4. 📊 Statut des serveurs
  5. 🛑 Arrêter tous les serveurs
  6. 📝 Voir les logs
  7. ❌ Quitter

### 🚀 LANCEMENT COMPLET
**Fichier :** `🚀-LANCER-LOUNA-COMPLET.sh`
- **Usage :** Script détaillé avec logs
- **Fonctionnalités :**
  - Vérification des prérequis
  - Nettoyage automatique
  - Logs détaillés
  - Gestion d'erreurs

## 🌐 Applications Disponibles

### 📱 Communication
- `/phone` - Système téléphone/caméra
- `/chat` - Agents de chat IA
- `/voice` - Système vocal avancé

### 🎨 Génération
- `/video` - Générateur de vidéos
- `/youtube` - Laboratoire YouTube
- `/music` - Générateur musical
- `/image` - Générateur d'images
- `/3d` - Générateur 3D
- `/generation` - Centre de génération

### 🧠 Intelligence
- `/brain` - Dashboard du cerveau
- `/qi` - Test de QI
- `/claude` - Configuration Claude
- `/monitoring` - Monitoring complet

### 🔥 Mémoire Thermique
- `/thermal` - Interface thermique
- `/thermal-dashboard` - Dashboard thermique
- `/brain-3d` - Visualisation 3D
- `/brain-viz` - Visualisation avancée

### ⚡ Système
- `/kyber` - Dashboard KYBER
- `/editor` - Éditeur de code avancé
- `/accelerators` - Accélérateurs

### 🔐 Sécurité
- `/security` - Centre de sécurité
- `/emergency` - Contrôle d'urgence

### 🌐 Web
- `/search` - Recherche web
- `/face` - Reconnaissance faciale

## 📊 APIs Disponibles

### 📈 Statistiques
- `/api/stats` - Statistiques générales
- `/api/test` - Test de l'API
- `/api/kyber/status` - Statut KYBER
- `/api/thermal/status` - Statut thermique

### 🔗 Utilitaires
- `/routes` - Liste des routes disponibles

## 🛠️ Dépannage

### ❌ Port déjà utilisé
```bash
# Arrêter le processus sur le port 3001
kill $(lsof -t -i:3001)
```

### 📝 Voir les logs
```bash
# Les logs sont dans le dossier logs/
ls -la logs/
```

### 🔄 Redémarrer
1. Utilisez le launcher interactif
2. Option 5 : Arrêter tous les serveurs
3. Option 1 : Relancer LOUNA COMPLET

## 🎯 Caractéristiques

- **QI :** 235 (Génie Exceptionnel)
- **Mémoire Thermique :** 37.0°C
- **Accélérateurs KYBER :** 8/16 actifs
- **Applications :** 24 disponibles
- **Mode :** Production complète

## 🚀 Démarrage Rapide

1. **Double-cliquez** sur `⚡-LANCEMENT-RAPIDE.sh`
2. **Attendez** le démarrage (quelques secondes)
3. **Ouvrez** http://localhost:3001/ dans votre navigateur
4. **Explorez** les 24 applications disponibles !

---

**Développé par Jean-Luc PASSAVE**  
**LOUNA AI v2.1.0 - Production Officielle**
