/**
 * 🎯 EXERCICES DE TRÈS HAUT NIVEAU POUR TESTER L'AGENT LOUNA AI
 * Tests intensifs pour déclencher l'auto-scaling KYBER
 */

const axios = require('axios');

const AGENT_URL = 'http://localhost:3001/api/agent-chat';
const KYBER_STATUS_URL = 'http://localhost:3001/api/kyber-status';

// ===== EXERCICES DE TRÈS HAUT NIVEAU =====
const exercicesHautNiveau = [
  {
    niveau: "🔥 EXTRÊME",
    titre: "Système IA Quantique Multi-Dimensionnel",
    message: `Créer un système d'intelligence artificielle quantique avec machine learning avancé, 
    deep neural networks multi-couches, traitement en temps réel, base de données distribuée, 
    sécurité blockchain, interface utilisateur responsive, APIs REST et GraphQL, 
    monitoring avancé avec métriques temps réel, automatisation CI/CD complète, 
    support multi-langue, computer vision, NLP transformer, et déploiement Kubernetes 
    avec load balancing et CDN global pour une scalabilité infinie.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation', 'multilingual', 'blockchain', 'quantum']
  },
  
  {
    niveau: "🚀 ULTRA-COMPLEXE",
    titre: "Plateforme de Trading Algorithmique IA",
    message: `Développer une plateforme de trading algorithmique avec intelligence artificielle, 
    machine learning pour prédiction des marchés, deep learning pour analyse des sentiments, 
    base de données haute performance, sécurité cryptographique avancée, 
    APIs en temps réel, interface de trading professionnelle, monitoring des performances, 
    automatisation des déploiements, support multi-devises et multi-langues, 
    intégration blockchain pour les cryptomonnaies, et architecture microservices scalable.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation', 'multilingual', 'blockchain']
  },
  
  {
    niveau: "🧠 GÉNIE-NIVEAU",
    titre: "Métaverse IA avec Réalité Augmentée",
    message: `Construire un métaverse avec intelligence artificielle, computer vision pour réalité augmentée, 
    deep learning pour avatars intelligents, NLP pour communication naturelle, 
    base de données 3D distribuée, sécurité end-to-end, APIs WebRTC temps réel, 
    interface immersive responsive, monitoring de performance 3D, automatisation DevOps, 
    support multi-langue et multi-plateforme, intégration blockchain pour NFTs, 
    et calcul quantique pour optimisation des rendus.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation', 'multilingual', 'blockchain', 'quantum']
  },
  
  {
    niveau: "⚡ SURHUMAIN",
    titre: "Système de Santé IA Prédictive",
    message: `Développer un système de santé avec IA prédictive, machine learning pour diagnostic, 
    deep learning pour analyse d'images médicales, NLP pour dossiers patients, 
    base de données médicales sécurisées, encryption HIPAA, APIs FHIR, 
    interface médecin-patient, monitoring vital temps réel, automatisation des rapports, 
    support multi-langue pour patients internationaux, blockchain pour traçabilité, 
    et calcul quantique pour simulation moléculaire.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation', 'multilingual', 'blockchain', 'quantum']
  },
  
  {
    niveau: "🌌 TRANSCENDANT",
    titre: "Ville Intelligente IA Totale",
    message: `Créer un système de ville intelligente avec IA omnisciente, machine learning pour trafic, 
    deep learning pour sécurité urbaine, computer vision pour surveillance, 
    NLP pour services citoyens, base de données IoT massive, sécurité cybernétique, 
    APIs gouvernementales, interface citoyenne mobile, monitoring urbain temps réel, 
    automatisation des services publics, support multi-langue pour touristes, 
    blockchain pour votes électroniques, et calcul quantique pour optimisation énergétique.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation', 'multilingual', 'blockchain', 'quantum']
  }
];

// ===== EXERCICES SPÉCIALISÉS =====
const exercicesSpecialises = [
  {
    niveau: "🔬 RECHERCHE AVANCÉE",
    titre: "Laboratoire IA pour Découverte Scientifique",
    message: `Système de recherche scientifique avec IA, machine learning pour hypothèses, 
    deep learning pour analyse de données, base de données scientifiques, 
    sécurité recherche, APIs laboratoire, interface chercheur, monitoring expériences.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation']
  },
  
  {
    niveau: "🎮 GAMING EXTRÊME",
    titre: "Moteur de Jeu IA Adaptatif",
    message: `Moteur de jeu avec IA adaptative, machine learning pour comportement NPCs, 
    computer vision pour réalité augmentée, base de données joueurs, 
    sécurité anti-triche, APIs multijoueur, interface gaming responsive.`,
    manquesAttendus: ['dataProcessing', 'security']
  },
  
  {
    niveau: "🏭 INDUSTRIE 4.0",
    titre: "Usine Intelligente Autonome",
    message: `Usine autonome avec IA industrielle, machine learning pour maintenance prédictive, 
    computer vision pour contrôle qualité, base de données production, 
    sécurité industrielle, APIs IoT, monitoring temps réel, automatisation complète.`,
    manquesAttendus: ['dataProcessing', 'security', 'automation']
  }
];

// ===== FONCTION DE TEST =====
async function testerAgent() {
  console.log('🎯 ===== DÉBUT DES TESTS AGENT HAUT NIVEAU =====');
  console.log('⏰', new Date().toLocaleString());
  console.log('');
  
  // Statut initial
  const statusInitial = await getKyberStatus();
  console.log('📊 Statut KYBER initial:', statusInitial);
  console.log('');
  
  let totalAcceleratorsAdded = 0;
  let totalGapsDetected = 0;
  
  // Tester les exercices de haut niveau
  for (const exercice of exercicesHautNiveau) {
    console.log(`🎯 ===== ${exercice.niveau} =====`);
    console.log(`📝 ${exercice.titre}`);
    console.log('');
    
    const statusAvant = await getKyberStatus();
    console.log(`⚡ Accélérateurs avant: ${statusAvant.active}`);
    
    // Envoyer le message complexe
    console.log('🚀 Envoi du message complexe...');
    const response = await sendMessage(exercice.message);
    
    // Attendre un peu pour l'auto-scaling
    await sleep(3000);
    
    const statusApres = await getKyberStatus();
    const acceleratorsAjoutes = statusApres.active - statusAvant.active;
    
    console.log(`⚡ Accélérateurs après: ${statusApres.active}`);
    console.log(`📈 Accélérateurs ajoutés: ${acceleratorsAjoutes}`);
    console.log(`🔗 Persistants: ${statusApres.persistent}`);
    
    if (response.success) {
      console.log('✅ Réponse agent reçue');
      console.log(`📝 Longueur réponse: ${response.response.length} caractères`);
    } else {
      console.log('❌ Erreur agent:', response.error);
    }
    
    totalAcceleratorsAdded += acceleratorsAjoutes;
    totalGapsDetected += exercice.manquesAttendus.length;
    
    console.log('');
    console.log('⏳ Pause avant exercice suivant...');
    await sleep(2000);
    console.log('');
  }
  
  // Tester les exercices spécialisés
  console.log('🔬 ===== EXERCICES SPÉCIALISÉS =====');
  
  for (const exercice of exercicesSpecialises) {
    console.log(`🎯 ${exercice.niveau}: ${exercice.titre}`);
    
    const statusAvant = await getKyberStatus();
    const response = await sendMessage(exercice.message);
    await sleep(2000);
    const statusApres = await getKyberStatus();
    
    const acceleratorsAjoutes = statusApres.active - statusAvant.active;
    console.log(`⚡ +${acceleratorsAjoutes} accélérateurs`);
    
    totalAcceleratorsAdded += acceleratorsAjoutes;
    totalGapsDetected += exercice.manquesAttendus.length;
    
    await sleep(1000);
  }
  
  // Résultats finaux
  console.log('');
  console.log('🎉 ===== RÉSULTATS FINAUX =====');
  const statusFinal = await getKyberStatus();
  console.log(`⚡ Accélérateurs totaux: ${statusFinal.active}`);
  console.log(`📈 Accélérateurs ajoutés: ${totalAcceleratorsAdded}`);
  console.log(`🔗 Persistants: ${statusFinal.persistent}`);
  console.log(`🔍 Manques détectés: ${totalGapsDetected}`);
  console.log(`🎯 Débit KYBER: ${statusFinal.throughput || 0}`);
  console.log(`📊 Auto-scaling: ${statusFinal.autoScaling ? 'ACTIF' : 'INACTIF'}`);
  
  // Évaluation de performance
  const performance = evaluatePerformance(statusInitial, statusFinal, totalAcceleratorsAdded);
  console.log('');
  console.log('📈 ===== ÉVALUATION PERFORMANCE =====');
  console.log(`🏆 Score: ${performance.score}/100`);
  console.log(`⚡ Réactivité: ${performance.reactivity}`);
  console.log(`🧠 Intelligence: ${performance.intelligence}`);
  console.log(`🔧 Adaptation: ${performance.adaptation}`);
  
  console.log('');
  console.log('🎯 ===== FIN DES TESTS =====');
}

// ===== FONCTIONS UTILITAIRES =====
async function sendMessage(message) {
  try {
    const response = await axios.post(AGENT_URL, { message }, { timeout: 60000 });
    return response.data;
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function getKyberStatus() {
  try {
    const response = await axios.get(KYBER_STATUS_URL, { timeout: 10000 });
    return response.data.kyber || {};
  } catch (error) {
    return { active: 0, persistent: 0, autoScaling: false };
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function evaluatePerformance(initial, final, added) {
  const reactivity = added > 0 ? Math.min(added * 10, 40) : 0;
  const intelligence = final.persistent > 0 ? Math.min(final.persistent * 5, 30) : 0;
  const adaptation = final.autoScaling ? 30 : 0;
  
  const score = reactivity + intelligence + adaptation;
  
  return {
    score,
    reactivity: reactivity > 30 ? 'EXCELLENTE' : reactivity > 15 ? 'BONNE' : 'FAIBLE',
    intelligence: intelligence > 20 ? 'ÉLEVÉE' : intelligence > 10 ? 'MOYENNE' : 'BASIQUE',
    adaptation: adaptation > 0 ? 'ACTIVE' : 'INACTIVE'
  };
}

// ===== TESTS RAPIDES =====
async function testRapide() {
  console.log('⚡ Test rapide de l\'agent...');
  
  const message = `Créer un système complexe avec machine learning, base de données, 
  sécurité avancée, APIs temps réel, interface responsive, monitoring complet, 
  automatisation CI/CD, support multi-langue, blockchain et calcul quantique.`;
  
  const statusAvant = await getKyberStatus();
  console.log(`⚡ Avant: ${statusAvant.active} accélérateurs`);
  
  const response = await sendMessage(message);
  await sleep(3000);
  
  const statusApres = await getKyberStatus();
  console.log(`⚡ Après: ${statusApres.active} accélérateurs`);
  console.log(`📈 Ajoutés: ${statusApres.active - statusAvant.active}`);
  
  return response.success;
}

// ===== EXPORT =====
module.exports = {
  testerAgent,
  testRapide,
  exercicesHautNiveau,
  exercicesSpecialises
};

// ===== EXÉCUTION DIRECTE =====
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--rapide')) {
    testRapide().then(success => {
      console.log(success ? '✅ Test rapide réussi' : '❌ Test rapide échoué');
      process.exit(success ? 0 : 1);
    });
  } else {
    testerAgent().then(() => {
      console.log('🎯 Tests terminés');
      process.exit(0);
    }).catch(error => {
      console.error('❌ Erreur tests:', error.message);
      process.exit(1);
    });
  }
}
