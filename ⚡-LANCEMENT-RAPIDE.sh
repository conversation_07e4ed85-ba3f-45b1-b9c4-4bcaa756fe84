#!/bin/bash

# ⚡ LANCEMENT RAPIDE LOUNA AI
# Double-cliquez pour lancer immédiatement !

clear

echo "⚡ =================================================="
echo "🚀     LANCEMENT RAPIDE LOUNA AI v2.1.0"
echo "🎯     QI: 235 (G<PERSON>ie Exceptionnel)"
echo "⚡ =================================================="

# Obtenir le répertoire du script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Arrêter les processus existants
echo "🛑 Nettoyage des processus existants..."
if lsof -i:3001 &> /dev/null; then
    kill $(lsof -t -i:3001) 2> /dev/null || true
    sleep 1
fi

# Lancer directement la version complète
echo "🚀 Lancement immédiat de LOUNA COMPLET..."
echo "🌐 Interface: http://localhost:3001/"
echo "📊 24 Applications disponibles"
echo ""
echo "⚡ Serveur en cours de démarrage..."

cd "$SCRIPT_DIR/04-SERVEURS"
node louna-test-complet.js
