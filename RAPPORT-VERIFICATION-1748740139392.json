{"timestamp": "2025-06-01T01:08:59.392Z", "summary": {"total": 28, "passed": 14, "failed": 14, "successRate": "50.0"}, "details": [{"test": "Fichier 04-SERVEURS/louna-ai-vrais-agents.js", "passed": true, "details": "Taille: 83020 bytes", "timestamp": "2025-06-01T01:08:59.346Z"}, {"test": "Fichier 03-SYSTEME/ia-principale-claude/claude-agent-connector.js", "passed": true, "details": "Taille: 6766 bytes", "timestamp": "2025-06-01T01:08:59.351Z"}, {"test": "Fichier 03-SYSTEME/ia-formation-deepseek/agent-formateur-deepseek.js", "passed": true, "details": "Taille: 8948 bytes", "timestamp": "2025-06-01T01:08:59.352Z"}, {"test": "Fichier 03-SYSTEME/sauvegarde-renforcee/sauvegarde-continue.js", "passed": true, "details": "Taille: 14682 bytes", "timestamp": "2025-06-01T01:08:59.352Z"}, {"test": "Fichier 03-SYSTEME/securite-renforcee/securite-louna.js", "passed": true, "details": "Taille: 10240 bytes", "timestamp": "2025-06-01T01:08:59.352Z"}, {"test": "Fichier 02-APPLICATIONS/chat/index.html", "passed": false, "details": "<PERSON><PERSON><PERSON> man<PERSON>", "timestamp": "2025-06-01T01:08:59.353Z"}, {"test": "Fichier 02-APPLICATIONS/brain/index.html", "passed": false, "details": "<PERSON><PERSON><PERSON> man<PERSON>", "timestamp": "2025-06-01T01:08:59.353Z"}, {"test": "Fichier 02-APPLICATIONS/creation/ai-language-creator.html", "passed": true, "details": "Taille: 32170 bytes", "timestamp": "2025-06-01T01:08:59.354Z"}, {"test": "API QI Évolutif", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.369Z"}, {"test": "API Pensées Temps Réel", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.370Z"}, {"test": "API Créations Langage IA", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.371Z"}, {"test": "API Stats Agent Formateur", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.371Z"}, {"test": "API Statistiques Sécurité", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.372Z"}, {"test": "API Santé Système", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.372Z"}, {"test": "Interface Interface Principale", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.373Z"}, {"test": "Interface Chat Agent", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.374Z"}, {"test": "Interface <PERSON>", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.375Z"}, {"test": "Interface Mémoire Thermique", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.376Z"}, {"test": "Interface AI Langage", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.377Z"}, {"test": "Ollama", "passed": true, "details": "2 modèles disponibles", "timestamp": "2025-06-01T01:08:59.387Z"}, {"test": "<PERSON><PERSON><PERSON><PERSON>seek-r1:7b", "passed": true, "details": "Taille: 4.36 GB", "timestamp": "2025-06-01T01:08:59.390Z"}, {"test": "<PERSON><PERSON><PERSON><PERSON> incept5/llama3.1-claude:latest", "passed": true, "details": "Taille: 4.34 GB", "timestamp": "2025-06-01T01:08:59.390Z"}, {"test": "Mémoire Thermique", "passed": false, "details": "", "timestamp": "2025-06-01T01:08:59.390Z"}, {"test": "Sécurité - Injection", "passed": true, "details": "Endpoint non trouvé (normal)", "timestamp": "2025-06-01T01:08:59.391Z"}, {"test": "Sécurité - Injection", "passed": true, "details": "Endpoint non trouvé (normal)", "timestamp": "2025-06-01T01:08:59.391Z"}, {"test": "Sécurité - Injection", "passed": true, "details": "Endpoint non trouvé (normal)", "timestamp": "2025-06-01T01:08:59.392Z"}, {"test": "Sécurité - Injection", "passed": true, "details": "Endpoint non trouvé (normal)", "timestamp": "2025-06-01T01:08:59.392Z"}, {"test": "Sécurité - Mode Local", "passed": true, "details": "Mode local activé", "timestamp": "2025-06-01T01:08:59.392Z"}]}